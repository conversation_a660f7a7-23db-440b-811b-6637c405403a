<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="alivc_play_type_edit_text">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/alivc_common_height_tv_50</item>
        <item name="android:layout_marginTop">@dimen/alivc_common_margin_8</item>
        <item name="android:background">@drawable/shape_edit_bg</item>
        <item name="android:textColor">@color/alivc_common_white</item>
        <item name="android:textSize">@dimen/alivc_common_font_14</item>
        <item name="android:paddingLeft">@dimen/alivc_common_margin_14</item>
        <item name="android:paddingRight">@dimen/alivc_common_margin_14</item>
    </style>

    <style name="alivc_info_seekbar" parent="@android:style/Widget.SeekBar">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:maxHeight">@dimen/alivc_player_info_seekbar_height</item>
        <item name="android:minHeight">@dimen/alivc_player_info_seekbar_height</item>
        <item name="android:paddingLeft">@dimen/alivc_player_info_seekbar_offset</item>
        <item name="android:paddingRight">@dimen/alivc_player_info_seekbar_offset</item>
        <item name="android:thumbOffset">@dimen/alivc_player_info_seekbar_offset</item>
    </style>

    <style name="edit_line_style" parent="@android:style/Widget.EditText">
        <item name="android:padding">5dp</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:background">@drawable/edit_round_bkg</item>
        <item name="android:layout_margin">5dp</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="btn_style" parent="@android:style/Widget.Button.Small">
        <item name="android:paddingLeft">10dp</item>
        <item name="android:paddingRight">10dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:background">@drawable/btn_round_bkg</item>
        <item name="android:layout_margin">5dp</item>
    </style>

    <style name="NoActionTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="windowActionBarOverlay">true</item>
    </style>


    <style name="AliPlayerSettingRadioButton">
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
        <item name="android:textSize">@dimen/alivc_common_font_14</item>
        <item name="android:textColor">@color/alivc_common_white</item>
        <item name="android:background">@drawable/aliyun_player_type_selector</item>
    </style>

    <style name="AliPlayerConfigEditText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/alivc_common_height_tv_50</item>
        <item name="android:singleLine">true</item>
        <item name="android:background">@drawable/shape_edit_bg</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/alivc_common_white</item>
        <item name="android:textSize">@dimen/alivc_common_font_14</item>
        <item name="android:layout_marginTop">@dimen/alivc_common_margin_8</item>
        <item name="android:layout_marginLeft">@dimen/alivc_common_margin_16</item>
        <item name="android:layout_marginRight">@dimen/alivc_common_margin_16</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">8</item>
    </style>
</resources>