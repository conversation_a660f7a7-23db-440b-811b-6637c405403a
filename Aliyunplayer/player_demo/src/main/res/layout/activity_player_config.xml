<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/bottom_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/alivc_common_line_1dp"
            android:background="@color/alivc_common_line_cyan_light"/>

        <!--确定，取消-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            android:layout_alignParentBottom="true"
            android:weightSum="2">

            <TextView
                android:id="@+id/tv_confirm_config"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@color/colorPrimary"
                android:text="@string/alivc_player_confirm_config" />

            <TextView
                android:id="@+id/tv_default_config"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@color/alivc_common_bg_cyan_light"
                android:text="@string/alivc_player_normal_config" />

        </LinearLayout>

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/bottom_btn"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/alivc_common_margin_40">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/alivc_common_padding_5"
                    android:paddingLeft="@dimen/alivc_common_padding_10"
                    android:src="@drawable/ic_back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/alivc_player_config_title"
                    android:textColor="@color/alivc_common_white"
                    android:textSize="@dimen/alivc_common_font_18" />

            </RelativeLayout>

            <!--启播-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_start_buffer_level"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_first_start_buffer_level"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--启播-->

            <!--卡顿恢复-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_high_buffer_level"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_high_buffer_level"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--卡顿恢复-->

            <!--最大值-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_max_buffer_packet_duration"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_max_buffer_packet_duration"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--最大值-->


            <!--直播最大延迟-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_max_delay_time"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_max_delay_time"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--直播最大延迟-->

            <!--网络超时-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_net_work_time_out"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_net_work_time_out"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--网络超时-->

            <!--重试-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_retry_count"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_retry_count"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--重试-->

            <!--probe大小-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_probe_size"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_probe_size"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--probe大小-->

            <!--请求referer-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_referrer"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_referrer"
                style="@style/AliPlayerConfigEditText"
                android:inputType="text"
                android:maxLength="20" />
            <!--请求referer-->

            <!--httpProxy代理-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_http_proxy"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_http_proxy"
                style="@style/AliPlayerConfigEditText"
                android:maxLength="50"
                android:inputType="text" />
            <!--httpProxy代理-->

            <!-- 停止显示最后帧 -->
            <Switch
                android:id="@+id/switch_enable_clear_when_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_20"
                android:switchPadding="@dimen/alivc_common_padding_20"
                android:text="@string/alivc_player_config_enable_clean_when_stop"
                android:textSize="@dimen/alivc_common_font_14" />

            <!-- 停止显示最后帧 -->

            <!-- 是否开启SEI -->
            <Switch
                android:id="@+id/switch_enable_sei"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_20"
                android:switchPadding="@dimen/alivc_common_padding_20"
                android:text="@string/alivc_player_config_enable_sei"
                android:textSize="@dimen/alivc_common_font_14" />

            <!--缓存最大时长-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_max_duration"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_max_duration"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--缓存最大时长-->

            <!--缓存最大空间-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_12"
                android:text="@string/alivc_player_config_max_size"
                android:textColor="@color/alivc_common_white"
                android:textSize="@dimen/alivc_common_font_14" />

            <EditText
                android:id="@+id/et_max_size"
                android:inputType="number"
                style="@style/AliPlayerConfigEditText" />
            <!--缓存最大空间-->

            <!-- 开启自定义缓存 -->
            <Switch
                android:id="@+id/switch_enable_cache"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/alivc_common_margin_16"
                android:layout_marginTop="@dimen/alivc_common_margin_20"
                android:switchPadding="@dimen/alivc_common_padding_20"
                android:text="@string/alivc_player_config_enable_cachet"
                android:textSize="@dimen/alivc_common_font_14" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</RelativeLayout>