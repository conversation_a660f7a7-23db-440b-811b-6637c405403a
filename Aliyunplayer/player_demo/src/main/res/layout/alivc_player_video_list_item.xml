<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_play_list_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="@dimen/alivc_common_padding_5">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="@dimen/alivc_common_width_icon_120"
        android:layout_height="@dimen/alivc_common_size_icon_70"
        android:layout_marginRight="@dimen/alivc_common_margin_5"
        android:scaleType="centerInside" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_video_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:textColor="@color/alivc_common_bg_black"
            android:textSize="@dimen/alivc_common_font_15" />

        <TextView
            android:id="@+id/tv_video_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:textSize="@dimen/alivc_common_font_12" />

    </LinearLayout>

</LinearLayout>