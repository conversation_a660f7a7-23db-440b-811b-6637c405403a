<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="@dimen/alivc_common_margin_20"
    android:paddingRight="@dimen/alivc_common_margin_20">

    <!--url-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="url" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_qrcode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerInParent="true"
            android:padding="@dimen/alivc_common_padding_5"
            android:src="@drawable/ic_qrcode"/>

        <EditText
            android:id="@+id/et_live_sts_url"
            android:layout_toLeftOf="@id/iv_qrcode"
            style="@style/alivc_play_type_edit_text" />

    </RelativeLayout>
    <!--url-->

    <!--domain-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="domain" />

    <EditText
        android:id="@+id/et_live_sts_domain"
        style="@style/alivc_play_type_edit_text" />
    <!--domain-->

    <!--app-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="app" />

    <EditText
        android:id="@+id/et_live_sts_app"
        style="@style/alivc_play_type_edit_text" />
    <!--app-->

    <!--stream-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="stream" />

    <EditText
        android:id="@+id/et_live_sts_stream"
        style="@style/alivc_play_type_edit_text" />
    <!--stream-->

    <!--Region-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:text="region" />

    <EditText
        android:id="@+id/et_live_sts_region"
        style="@style/alivc_play_type_edit_text" />
    <!--Region-->

    <!--accessKeyId-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="accessKeyId" />

    <EditText
        android:id="@+id/et_live_sts_access_key_id"
        style="@style/alivc_play_type_edit_text" />
    <!--accessKeyId-->

    <!--accessKeySecret-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="accessKeySecret" />

    <EditText
        android:id="@+id/et_live_sts_access_key_secret"
        style="@style/alivc_play_type_edit_text" />
    <!--accessKeySecret-->

    <!--securityToken-->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/alivc_common_white"
        android:textSize="@dimen/alivc_common_font_14"
        android:layout_marginTop="@dimen/alivc_common_margin_14"
        android:text="securityToken" />

    <EditText
        android:id="@+id/et_live_sts_security_token"
        style="@style/alivc_play_type_edit_text" />
    <!--securityToken-->

    <TextView
        android:id="@+id/tv_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/alivc_common_margin_10"
        android:layout_marginBottom="@dimen/alivc_common_margin_30"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/alivc_common_white"
        android:padding="@dimen/alivc_common_padding_10"
        android:background="@drawable/aliyun_player_refresh_bg_shape"
        android:text="@string/alivc_refresh_sts"/>

</LinearLayout>
