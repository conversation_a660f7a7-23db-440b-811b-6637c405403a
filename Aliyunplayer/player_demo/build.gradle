apply plugin: 'com.android.library'

android {
    compileSdkVersion externalCompileSdkVersion

    defaultConfig {
        minSdkVersion externalMinSdkVersion
        targetSdkVersion externalTargetSdkVersion
        versionCode 1
        versionName "1.0"
    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {


    implementation project(':zxing')
    implementation externalGlide

    implementation project(':Aliyunplayer:AlivcPlayerTools')
}
