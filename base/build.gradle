plugins {
    id 'com.android.library'
}

android {
    //namespace 'com.dep.utils'
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]

    defaultConfig {
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    api rootProject.ext.dependencies["appcompat"]
    api rootProject.ext.dependencies["design"]
    api rootProject.ext.dependencies["constraint-layout"]
    api rootProject.ext.dependencies["viewpager2"]
    api rootProject.ext.dependencies["recyclerview"]
    api rootProject.ext.dependencies["junit"]
    api rootProject.ext.dependencies["annotations"]
    //dx分包
    api rootProject.ext.dependencies["multidex"]
    //Toast弹窗
    api rootProject.ext.dependencies["toast"]
    //屏幕适配
    api rootProject.ext.dependencies["autolayout"]
    //api rootProject.ext.dependencies["autosize"]
    //网络请求框架
    api rootProject.ext.dependencies['okhttp3']
    //本地图片选择库
    api rootProject.ext.dependencies["picture-selector"]
    //本地图片选择库--压缩
    api rootProject.ext.dependencies["picture-compress"]
    //本地图片选择库--裁剪
    api rootProject.ext.dependencies["picture-ucrop"]
    //加载图片
    api rootProject.ext.dependencies["glide"]
    //EventBus通知
    api rootProject.ext.dependencies["androideventbus"]
    //JSON数据解析工具
    api rootProject.ext.dependencies["gson"]
    //视屏播放器完整版引入
    api rootProject.ext.dependencies["videoPlayer"]
    //状态栏控制工具 传送门：https://github.com/gyf-dev/ImmersionBar
    api 'com.geyifeng.immersionbar:immersionbar:3.2.2'

    //推送消息
    api 'com.huawei.hms:push:6.12.0.300'
    api 'com.umeng.umsdk:oppo-push:3.4.0'
    api 'com.umeng.umsdk:xiaomi-push:5.9.9'
    api 'com.umeng.umsdk:vivo-push:3.0.0.7'
}