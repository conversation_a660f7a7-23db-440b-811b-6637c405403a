<?xml version="1.0" encoding="utf-8"?>
<com.biguo.utils.widget.StyleConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="295dp"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:all_round="10dp"
    app:bgGradientStartColor="@color/white">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="3dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:minHeight="32dp"
        android:textColor="@color/tblack"
        android:textSize="16dp"
        android:gravity="center"
        tools:text="温馨提示"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tvContent"/>

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="24dp"
        android:textColor="@color/tblack"
        android:gravity="center"
        tools:text="对话框内容"
        android:textSize="14dp"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@id/horizontalLineView"/>

    <View
        android:id="@+id/horizontalLineView"
        android:background="@color/tblack3"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintTop_toBottomOf="@id/tvContent"/>

    <TextView
        android:id="@+id/tvNegative"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_below="@id/tvTitle"
        android:clickable="true"
        android:gravity="center"
        android:text="取消"
        android:textSize="16dp"
        android:textColor="@color/tblack3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/verticalLineView"
        app:layout_constraintEnd_toStartOf="@id/verticalLineView"
        app:layout_constraintBottom_toBottomOf="@id/verticalLineView"/>

    <View
        android:id="@+id/verticalLineView"
        android:background="@color/tblack3"
        android:layout_width="0.5dp"
        android:layout_height="48dp"
        app:layout_constraintStart_toEndOf="@id/tvNegative"
        app:layout_constraintEnd_toStartOf="@id/tvPositive"
        app:layout_constraintTop_toBottomOf="@id/horizontalLineView"/>

    <TextView
        android:id="@+id/tvPositive"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:clickable="true"
        android:gravity="center"
        android:text="确认"
        android:textSize="16dp"
        android:textColor="@color/theme"
        android:layout_marginTop="1dp"
        app:layout_constraintStart_toEndOf="@id/verticalLineView"
        app:layout_constraintTop_toTopOf="@id/verticalLineView"
        app:layout_constraintBottom_toBottomOf="@id/verticalLineView"
        app:layout_constraintEnd_toEndOf="parent"/>

</com.biguo.utils.widget.StyleConstraintLayout>
