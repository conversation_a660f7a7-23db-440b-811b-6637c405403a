<?xml version="1.0" encoding="utf-8"?>
<paths>
    <!--1、对应内部内存卡根目录：Context.getFileDir()，path指定了根目录下的子目录-->
    <files-path
        name="int_root"
        path="/" />
    <!--2、对应应用默认缓存根目录：Context.getCacheDir()，path指定了根目录下的子目录-->
    <cache-path
        name="app_cache"
        path="/" />
    <!--3、对应外部内存卡根目录：Environment.getExternalStorageDirectory()，path指定了根目录下的子目录-->
    <external-path
        name="ext_root"
        path="/" />
    <!--4、对应外部内存卡根目录下的APP公共目录：Context.getExternalFileDir(String)，path指定了根目录下的子目录-->
    <external-files-path
        name="ext_pub"
        path="/" />
    <!--5、对应外部内存卡根目录下的APP缓存目录：Context.getExternalCacheDir()，path指定了根目录下的子目录-->
    <external-cache-path
        name="ext_cache"
        path="/" />
</paths>
