<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--StyleConstraintLayout和StyleLinearLayoutLayout的共用属性-->
    <attr name="left_top_round" format="dimension"/><!--左上角的圆角大小-->
    <attr name="left_bottom_round" format="dimension"/><!--左下角的圆角大小-->
    <attr name="right_top_round" format="dimension"/><!--右上角的圆角大小-->
    <attr name="right_bottom_round" format="dimension"/><!--右下角的圆角大小-->
    <attr name="all_round" format="dimension"/><!--对应位置的圆角大小-->
    <attr name="bgGradientStartColor" format="color"/><!--背景渐变色之开头颜色-->
    <attr name="bgGradientEndColor" format="color"/><!--背景渐变色之结尾颜色-->
    <attr name="bgGradientStartWeight" format="float"/><!--背景渐变色之开头颜色在整个控件中的比重-->
    <attr name="bgGradientEndWeight" format="float"/><!--背景渐变色之结尾颜色在整个控件中的比重-->
    <attr name="bgcGradientAngle" format="float"/><!--背景渐变色方向 角度[-90° - 90°]-->
    <attr name="borderWidth" format="dimension"/><!--边框宽度-->
    <attr name="borderColor" format="color"/><!--边框颜色-->
    <attr name="shadowColor" format="color"/><!--阴影颜色-->
    <attr name="shadowWidth" format="dimension"/><!--阴影宽度-->
    <attr name="shadowOffsetX" format="dimension"/><!--阴影水平偏移-->
    <attr name="shadowOffsetY" format="dimension"/><!--阴影垂直偏移-->
    <declare-styleable name="StyleConstraintLayout">
        <attr name="left_top_round"/>
        <attr name="left_bottom_round"/>
        <attr name="right_top_round"/>
        <attr name="right_bottom_round"/>
        <attr name="all_round"/>
        <attr name="bgGradientStartColor"/>
        <attr name="bgGradientEndColor"/>
        <attr name="bgGradientStartWeight"/>
        <attr name="bgGradientEndWeight"/>
        <attr name="bgcGradientAngle"/>
        <attr name="borderWidth"/>
        <attr name="borderColor"/>
        <attr name="shadowColor"/>
        <attr name="shadowWidth"/>
        <attr name="shadowOffsetX"/>
        <attr name="shadowOffsetY"/>
    </declare-styleable>

    <declare-styleable name="StyleLinearLayoutLayout">
        <attr name="left_top_round"/>
        <attr name="left_bottom_round"/>
        <attr name="right_top_round"/>
        <attr name="right_bottom_round"/>
        <attr name="all_round"/>
        <attr name="bgGradientStartColor"/>
        <attr name="bgGradientEndColor"/>
        <attr name="bgGradientStartWeight"/>
        <attr name="bgGradientEndWeight"/>
        <attr name="bgcGradientAngle"/>
        <attr name="borderWidth"/>
        <attr name="borderColor"/>
        <attr name="shadowColor"/>
        <attr name="shadowWidth"/>
        <attr name="shadowOffsetX"/>
        <attr name="shadowOffsetY"/>
    </declare-styleable>

    <declare-styleable name="StyleTextView">
        <attr name="left_top_round"/>
        <attr name="left_bottom_round"/>
        <attr name="right_top_round"/>
        <attr name="right_bottom_round"/>
        <attr name="all_round"/>
        <attr name="bgGradientStartColor"/>
        <attr name="bgGradientEndColor"/>
        <attr name="bgGradientStartWeight"/>
        <attr name="bgGradientEndWeight"/>
        <attr name="bgcGradientAngle"/>
        <attr name="borderWidth"/>
        <attr name="borderColor"/>
        <attr name="shadowColor"/>
        <attr name="shadowWidth"/>
        <attr name="shadowOffsetX"/>
        <attr name="shadowOffsetY"/>
    </declare-styleable>

    <declare-styleable name="ArrowLayout">
        <attr name="window_color_res" format="color"/>
        <attr name="shadow_color_res" format="color"/>
        <attr name="shadow_width" format="dimension"/>
        <attr name="round" format="dimension"/>
        <attr name="arrow_offset_x" format="dimension"/>
        <attr name="arrow_anchor_x" format="enum">
            <enum name="ARROW_LEFT" value="0"/>
            <enum name="ARROW_CENTER" value="1"/>
            <enum name="ARROW_RIGHT" value="2"/>
        </attr>
        <attr name="arrow_width" format="dimension"/>
        <attr name="arrow_height" format="dimension"/>
        <attr name="arrow_toward" format="enum">
            <enum name="arrow_toward_top" value="0"/>
            <enum name="arrow_toward_bottom" value="1"/>
        </attr>
        <attr name="max_height" format="dimension"/>
        <attr name="max_width" format="dimension"/>
    </declare-styleable>
</resources>