apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.android["compileSdkVersion"]
    defaultConfig {
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        renderscriptSupportModeEnabled true
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    api ('net.polyv.android:polyvSDKLiveScenes:1.10.4'){
        exclude group:'com.tencent.bugly'
        exclude group:'com.aliyun.ams'
    }

    //glide
    api("com.github.bumptech.glide:okhttp3-integration:4.9.0") {
//        exclude group:'com.github.bumptech.glide',module:'glide'
    }
    /// 添加4.10.0的依赖
    api 'com.github.bumptech.glide:glide:4.9.0'
    //与主项目的ArmsMvp框架有冲突，不能继承两次AppGlideModule类，与保利威官方确认过，注释掉不会有影响
    //annotationProcessor 'com.github.bumptech.glide:compiler:4.9.0'

    api 'pl.droidsonroids.gif:android-gif-drawable:1.2.23'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'

    api 'androidx.room:room-runtime:2.0.0'
    annotationProcessor 'androidx.room:room-compiler:2.0.0'
    api 'androidx.room:room-rxjava2:2.0.0'

    //svga动画特效库
    api("com.github.yyued:SVGAPlayer-Android:2.6.1")

    //下拉刷新&上拉加载更多控件
    api 'me.dkzwm.widget.srl:core:1.6.6.4'
    api 'me.dkzwm.widget.srl:ext-util:1.6.6.4'
    api 'me.dkzwm.widget.srl:ext-material:1.6.6.4'
    api 'me.dkzwm.widget.srl:ext-classics:1.6.6.4'
}
