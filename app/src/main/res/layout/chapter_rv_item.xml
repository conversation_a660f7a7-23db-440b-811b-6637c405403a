<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/conlItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/app_space"
    android:layout_marginRight="@dimen/app_space"
    android:paddingTop="@dimen/app_space"
    android:paddingBottom="@dimen/app_space">

    <View
        android:id="@+id/v"
        android:layout_width="@dimen/dp_5"
        android:layout_height="@dimen/dp_5"
        android:layout_marginLeft="@dimen/dp_5"
        android:background="@drawable/circle_orange"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/app_space"
        android:layout_marginRight="@dimen/app_space"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_12"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvCount"
        app:layout_constraintStart_toEndOf="@+id/v"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableRight="@drawable/chapter_icon_do"
        android:drawablePadding="@dimen/dp_5"
        android:gravity="center"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>