<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/replayCommentBottomBgView"
        android:background="@drawable/bg_round_bottom_5_bgc"
        android:layout_marginStart="52dp"
        android:layout_marginEnd="10dp"
        android:minHeight="15dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/loadReplyCommentView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingStart="0dp"
            android:paddingTop="5dp"
            android:paddingEnd="8dp"
            android:paddingBottom="10dp"
            android:text="展开更多回复"
            android:textColor="@color/tblack2"
            style="@style/lightText" />

        <ImageView
            android:id="@+id/downView"
            android:src="@drawable/arrow_back"
            android:rotation="-90"
            android:layout_width="5dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="6dp"
            android:layout_toEndOf="@id/loadReplyCommentView"
            android:layout_alignTop="@id/loadReplyCommentView"
            android:layout_alignBottom="@id/loadReplyCommentView"/>
    </RelativeLayout>

    <ImageView
        android:id="@+id/lineView"
        android:layout_width="0dp"
        android:layout_height="10dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/split_bottom_line"
        android:layout_marginStart="52dp"
        app:layout_constraintTop_toBottomOf="@+id/replayCommentBottomBgView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <TextView
        android:id="@+id/loadTextView"
        android:text="没有更多评论了~"
        android:textColor="@color/tblack3"
        android:drawableTop="@drawable/load_comment_error"
        android:drawablePadding="20dp"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="20dp"
        android:paddingBottom="10dp"
        style="@style/lightText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lineView"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>