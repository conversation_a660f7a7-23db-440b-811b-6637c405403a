<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ck_user_icon_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ck_user_icon_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_navigationbar"
        android:gravity="center_vertical"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:padding="@dimen/dp_10"
            android:src="@drawable/arrow_back"
            android:visibility="visible" />

        <TextView
            android:id="@+id/navigation_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:text="学籍资料"
            android:textColor="@color/tblack"
            android:textSize="@dimen/sp_16" />

    </androidx.appcompat.widget.Toolbar>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_20"
            android:paddingRight="@dimen/dp_20">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/bg_round_5_bgc"
                android:drawableLeft="@drawable/ck_studydata_icon_notify"
                android:drawablePadding="@dimen/dp_10"
                android:lineSpacingExtra="@dimen/dp_5"
                android:padding="@dimen/dp_10"
                android:text="注：居住证、社保卡、工作证明三选一，技能证书是指公共英语二三级，国家计算机二级，英语四级等。"
                android:textColor="@color/tblack3"
                android:textSize="@dimen/sp_12" />

            <FrameLayout
                android:id="@+id/flIdcard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ck_bot_border_bgc"
                android:paddingTop="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="身份证照片"
                    android:textColor="@color/tblack2"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvIdcardStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center_vertical"
                    android:drawableRight="@drawable/arrow_right"
                    android:drawablePadding="@dimen/dp_10"
                    android:text="未上传"
                    android:textColor="@color/ck_theme"
                    android:textSize="@dimen/sp_12" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flBlue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ck_bot_border_bgc"
                android:paddingTop="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="蓝底照片"
                    android:textColor="@color/tblack2"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvBlueStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center_vertical"
                    android:drawableRight="@drawable/arrow_right"
                    android:drawablePadding="@dimen/dp_10"
                    android:text="未上传"
                    android:textColor="@color/ck_theme"
                    android:textSize="@dimen/sp_12" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flEducation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ck_bot_border_bgc"
                android:paddingTop="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="学历证书"
                    android:textColor="@color/tblack2"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvEducationStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center_vertical"
                    android:drawableRight="@drawable/arrow_right"
                    android:drawablePadding="@dimen/dp_10"
                    android:text="未上传"
                    android:textColor="@color/ck_theme"
                    android:textSize="@dimen/sp_12" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flThree"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ck_bot_border_bgc"
                android:paddingTop="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="居住证/社保卡/工作证明"
                    android:textColor="@color/tblack2"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvThreeStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center_vertical"
                    android:drawableRight="@drawable/arrow_right"
                    android:drawablePadding="@dimen/dp_10"
                    android:text="未上传"
                    android:textColor="@color/ck_theme"
                    android:textSize="@dimen/sp_12" />

            </FrameLayout>

            <FrameLayout
                android:id="@+id/flSkill"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/ck_bot_border_bgc"
                android:paddingTop="@dimen/dp_20"
                android:paddingBottom="@dimen/dp_20">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="技能证书"
                    android:textColor="@color/tblack2"
                    android:textSize="@dimen/sp_14" />

                <TextView
                    android:id="@+id/tvSkillStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right|center_vertical"
                    android:drawableRight="@drawable/arrow_right"
                    android:drawablePadding="@dimen/dp_10"
                    android:text="未上传"
                    android:textColor="@color/ck_theme"
                    android:textSize="@dimen/sp_12" />

            </FrameLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>