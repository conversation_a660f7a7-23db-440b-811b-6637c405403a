<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.PayTuitionActivity" />
    </data>

    <androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">

                <com.dep.biguo.widget.RoundedImageView
                    android:id="@+id/inviteToIntroduceView"
                    android:adjustViewBounds="true"
                    android:src="@drawable/invite_to_introduce_enter"
                    android:onClick="@{onClickListener.onViewClicked}"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:radius="6dp"/>

                <TextView
                    style="@style/TextView_V2"
                    android:layout_marginTop="15dp"
                    android:paddingBottom="5dp"
                    android:text="@string/pay_tuition_name" />

                <EditText
                    android:id="@+id/inputNameView"
                    style="@style/EditText_V2"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_32"
                    android:background="@drawable/input_bottom_line"
                    android:hint="@string/pay_tuition_input_real_name"
                    android:maxLength="20" />

                <TextView
                    android:id="@+id/selectedTitleTypeView"
                    style="@style/TextView_V2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:drawableEnd="@drawable/arrow_black_down"
                    android:drawablePadding="4dp"
                    android:onClick="@{onClickListener.onViewClicked}"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="报读形式" />

                <TextView
                    android:id="@+id/selectedTypeView"
                    style="@style/EditText_V2"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp_32"
                    android:background="@drawable/input_bottom_line"
                    android:gravity="center_vertical"
                    android:hint="请选择报读形式"
                    android:maxLength="20"
                    android:onClick="@{onClickListener.onViewClicked}" />

                <TextView
                    style="@style/TextView_V2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="学费总额" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/input_bottom_line"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingBottom="1dp">

                    <TextView
                        style="@style/EditText_V2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥  "
                        android:textSize="@dimen/sp_20" />

                    <com.dep.biguo.widget.DecimalEditView
                        android:id="@+id/inputAllAmountView"
                        style="@style/EditText_V2"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_32"
                        android:layout_weight="1"
                        android:inputType="numberDecimal"
                        android:maxLength="12"
                        android:textSize="@dimen/sp_20" />

                    <ImageView
                        android:id="@+id/clearAllView"
                        android:layout_width="40dp"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener.onViewClicked}"
                        android:paddingTop="6dp"
                        android:paddingBottom="6dp"
                        android:scaleType="fitEnd"
                        android:src="@drawable/close_icon" />

                </LinearLayout>

                <TextView
                    android:id="@+id/inputFirstAmountTitleView"
                    style="@style/TextView_V2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="首缴金额" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/input_bottom_line"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingBottom="1dp">

                    <TextView
                        style="@style/EditText_V2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥  "
                        android:textSize="@dimen/sp_20" />

                    <com.dep.biguo.widget.DecimalEditView
                        android:id="@+id/inputFirstAmountView"
                        style="@style/EditText_V2"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_32"
                        android:layout_weight="1"
                        android:inputType="numberDecimal"
                        android:maxLength="12"
                        android:textSize="@dimen/sp_20" />

                    <ImageView
                        android:id="@+id/clearFirstView"
                        android:layout_width="40dp"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener.onViewClicked}"
                        android:paddingTop="6dp"
                        android:paddingBottom="6dp"
                        android:scaleType="fitEnd"
                        android:src="@drawable/close_icon" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/stagesLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/surplusMoneyView"
                        style="@style/TextView_V2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:text="分期总额：¥2980.00（不含手续费）"
                        app:changeColor="@color/theme"
                        app:endChar="（"
                        app:startChar="：" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/stageRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="com.google.android.flexbox.FlexboxLayoutManager" />
                </LinearLayout>

                <TextView
                    style="@style/TextView_V2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:text="@string/pay_tuition_remarks"
                    android:textSize="@dimen/sp_14" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_input_promoter"
                    android:orientation="vertical">

                    <com.dep.biguo.widget.LayoutUpScrollEditView
                        android:id="@+id/inputRemarkView"
                        style="@style/EditText_V2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#00FFFFFF"
                        android:gravity="start"
                        android:hint="@string/pay_tuition_input_leave_comments"
                        android:lines="5"
                        android:maxLength="100"
                        android:padding="8dp" />

                    <TextView
                        android:id="@+id/remarkCountView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="4dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="4dp"
                        android:text="0/100"
                        android:textColor="@color/tblack3" />
                </LinearLayout>

                <TextView
                    android:id="@+id/commitView"
                    android:layout_width="match_parent"
                    android:layout_height="42dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="@dimen/dp_30"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/selector_v2_click"
                    android:gravity="center"
                    android:onClick="@{onClickListener.onViewClicked}"
                    android:text="@string/pay_tuition_start"
                    android:textColor="@color/twhite"
                    android:textSize="@dimen/sp_18" />
            </LinearLayout>
        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>

</layout>
