<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_round_top_10_white"
    android:orientation="vertical">

    <TextView
        android:gravity="center"
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_20"
        android:layout_alignTop="@+id/ivClose"
        android:layout_alignBottom="@id/ivClose"
        android:text="@string/practice_error_v2"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_12"
        android:textStyle="bold" />

    <ImageView
        android:layout_marginTop="@dimen/dp_10"
        android:id="@+id/ivClose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/dp_20"
        android:src="@drawable/ic_close_black_24dp" />

    <View
        android:layout_marginTop="@dimen/dp_10"
        android:id="@+id/VDline"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_below="@id/tvTitle"
        android:layout_alignStart="@id/tvTitle"
        android:layout_alignEnd="@id/ivClose"
        android:background="@color/guobi_div_line_color" />

    <TextView
        android:id="@+id/tvTitle2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/VDline"
        android:layout_alignStart="@id/tvTitle"
        android:layout_marginTop="@dimen/dp_10"
        android:text="@string/practice_error_classification_label"
        android:textColor="@color/tblack2"
        android:textSize="@dimen/sp_14" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvErrorType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tvTitle2"
        android:layout_alignStart="@id/VDline"
        android:layout_alignEnd="@id/VDline"
        android:layout_marginTop="@dimen/dp_10"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="3" />


    <TextView
        android:id="@+id/tvTitle3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/rvErrorType"
        android:layout_alignStart="@id/tvTitle"
        android:text="我要反馈"
        android:textColor="@color/tblack2"
        android:textSize="@dimen/sp_14" />


    <EditText
        android:id="@+id/etDetail"
        android:layout_width="match_parent"
        android:layout_height="74dp"
        android:layout_below="@+id/tvTitle3"
        android:layout_alignStart="@id/VDline"
        android:layout_alignEnd="@id/VDline"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/practice_input_bg"
        android:gravity="top"
        android:hint="请在此输入您要反馈的详细内容"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_5"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_12" />

    <EditText
        android:id="@+id/etConnection"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_below="@+id/etDetail"
        android:layout_alignStart="@id/VDline"
        android:layout_alignEnd="@id/VDline"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/practice_input_bg"
        android:gravity="top"
        android:hint="请留下您的联系方式（选填）"
        android:paddingStart="@dimen/dp_10"
        android:paddingTop="@dimen/dp_5"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_12" />

    <TextView
        android:id="@+id/tvDescribed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/etConnection"
        android:layout_alignStart="@id/VDline"
        android:layout_marginTop="@dimen/dp_15"
        android:text="具体描述"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_13" />

    <TextView
        android:id="@+id/tvExample"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvDescribed"
        android:layout_alignStart="@id/VDline"
        android:layout_alignEnd="@id/VDline"
        android:layout_marginTop="@dimen/dp_3"
        android:lineSpacingExtra="@dimen/dp_2"
        android:text="@string/practice_error_example"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_11" />

    <TextView
        android:id="@+id/tvReward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvExample"
        android:layout_alignStart="@id/VDline"
        android:layout_alignEnd="@id/VDline"
        android:layout_marginTop="@dimen/dp_10"
        android:lineSpacingExtra="@dimen/dp_2"
        android:text="注：反馈成功+1果币"
        android:textColor="@color/theme"
        android:textSize="@dimen/sp_11" />

    <TextView
        android:id="@+id/tvSubmit"
        android:layout_width="@dimen/dp_120"
        android:layout_height="@dimen/dp_35"
        android:layout_below="@+id/tvReward"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_10"
        android:background="@drawable/bg_round_200_theme"
        android:gravity="center"
        android:padding="@dimen/dp_8"
        android:text="确认提交"
        android:textColor="@color/twhite"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold" />

</RelativeLayout>