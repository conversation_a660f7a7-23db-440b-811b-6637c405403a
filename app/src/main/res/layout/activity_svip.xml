<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/ivBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivTop"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:scaleType="fitXY"
                android:src="@drawable/svip_icon_top"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:fontFamily="sans-serif-medium"
                android:rotation="-2"
                android:visibility="visible"
                android:id="@+id/tvCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已开通856人啦"
                android:textColor="@color/twhite"
                android:textSize="@dimen/sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/ivTop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.503"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivTop"
                app:layout_constraintVertical_bias="0.74" />

            <TextView
                android:id="@+id/tvVip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:drawableTop="@drawable/svip_icon_vip"
                android:drawablePadding="@dimen/dp_15"
                android:gravity="center"
                android:padding="@dimen/dp_20"
                android:text="VIP题库"
                android:textColor="@color/tblack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvSecret"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivTop" />

            <TextView
                android:id="@+id/tvSecret"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:drawableTop="@drawable/svip_icon_secret"
                android:drawablePadding="@dimen/dp_15"
                android:gravity="center"
                android:padding="@dimen/dp_20"
                android:text="考前押密"
                android:textColor="@color/tblack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toStartOf="@+id/tvCustomer"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/tvVip"
                app:layout_constraintTop_toBottomOf="@+id/ivTop" />

            <TextView
                android:id="@+id/tvCustomer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:drawableTop="@drawable/svip_icon_customer"
                android:drawablePadding="@dimen/dp_15"
                android:gravity="center"
                android:padding="@dimen/dp_20"
                android:text="免费咨询"
                android:textColor="@color/tblack"
                android:textSize="@dimen/sp_12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/tvSecret"
                app:layout_constraintTop_toBottomOf="@+id/ivTop" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/conl1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginRight="@dimen/dp_15"
                android:background="@drawable/bg_round_10_white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvVip">

                <LinearLayout
                    android:id="@+id/llDetail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <View
                        android:layout_width="@dimen/dp_4"
                        android:layout_height="@dimen/dp_18"
                        android:background="@drawable/label_svip" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:text="详情介绍"
                        android:textColor="@color/tblack"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvDetail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_15"
                    android:lineSpacingExtra="@dimen/dp_2"
                    android:textColor="@color/tblack"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/llDetail" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp_15"
                android:layout_marginTop="@dimen/dp_15"
                android:layout_marginRight="@dimen/dp_15"
                android:background="@drawable/bg_round_10_white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/conl1">

                <LinearLayout
                    android:id="@+id/ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_20"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <View
                        android:layout_width="@dimen/dp_4"
                        android:layout_height="@dimen/dp_18"
                        android:background="@drawable/label_svip" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dp_10"
                        android:text="特权介绍"
                        android:textColor="@color/tblack"
                        android:textSize="@dimen/sp_16"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_15"
                    android:lineSpacingExtra="@dimen/dp_2"
                    android:text="@string/svip_content"
                    android:textColor="@color/tblack"
                    android:textSize="@dimen/sp_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ll" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <ImageView
        android:id="@+id/ivBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/app_space"
        android:src="@drawable/svip_icon_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_30"
        android:text="优惠价只需￥0"
        android:textColor="@color/twhite"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/ivBtn"
        app:layout_constraintStart_toStartOf="@+id/ivBtn"
        app:layout_constraintTop_toTopOf="@+id/ivBtn" />

    <TextView
        android:id="@+id/tvBuy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dp_7"
        android:padding="@dimen/dp_10"
        android:text="立即开通"
        android:textColor="#FF5334"
        android:textSize="@dimen/sp_16"
        app:layout_constraintBottom_toBottomOf="@+id/ivBtn"
        app:layout_constraintEnd_toEndOf="@+id/ivBtn"
        app:layout_constraintTop_toTopOf="@+id/ivBtn" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBuy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="ivBtn,tvPrice,tvBuy" />

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="@dimen/title_navigationbar"
        android:background="@color/tran"
        android:gravity="center_vertical"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/navigation_iv_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:padding="@dimen/dp_10"
            android:src="@drawable/arrow_back_white"
            android:visibility="visible" />

    </androidx.appcompat.widget.Toolbar>

</androidx.constraintlayout.widget.ConstraintLayout>