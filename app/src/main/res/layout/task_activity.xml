<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!--自定义ToolBar会计算一个状态栏和一个导航栏的高度赋值给该控件-->
                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/headLayout"
                        android:background="@drawable/task_head_bg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">

                        <TextView
                            android:id="@+id/ruleView"
                            android:text="积分规则"
                            android:textSize="13dp"
                            android:textColor="@color/twhite"
                            android:gravity="center"
                            android:background="@drawable/user_recharge_v2_bg"
                            android:backgroundTint="#33FFFFFF"
                            android:layout_width="80dp"
                            android:layout_height="26dp"
                            android:layout_marginTop="9dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"/>

                        <TextView
                            android:id="@+id/integralTitleView"
                            android:text="我的积分"
                            android:textSize="14dp"
                            android:textColor="@color/twhite"
                            android:drawableEnd="@drawable/integral_48"
                            android:drawablePadding="4dp"
                            android:layout_marginStart="50dp"
                            android:layout_marginTop="9dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@+id/ruleView"
                            app:layout_constraintStart_toStartOf="parent"/>

                        <com.biguo.utils.widget.StyleTextView
                            android:id="@+id/validIntegralView"
                            android:text="即将过期0积分"
                            android:textColor="#FFD3C9"
                            android:textSize="8dp"
                            android:paddingStart="5dp"
                            android:paddingTop="2dp"
                            android:paddingEnd="5dp"
                            android:paddingBottom="2dp"
                            android:visibility="invisible"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            app:bgGradientStartColor="#1A000000"
                            app:all_round="10dp"
                            app:layout_constraintStart_toEndOf="@id/integralTitleView"
                            app:layout_constraintTop_toTopOf="@id/integralTitleView"
                            app:layout_constraintBottom_toBottomOf="@id/integralTitleView"/>

                        <TextView
                            android:id="@+id/integralView"
                            android:text="0"
                            android:textColor="@color/twhite"
                            android:textSize="36dp"
                            android:drawableEnd="@drawable/arrow_right_write"
                            android:drawablePadding="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintStart_toStartOf="@id/integralTitleView"
                            app:layout_constraintTop_toBottomOf="@id/integralTitleView"/>

                        <TextView
                            android:id="@+id/exchangeView"
                            android:text="兑好礼"
                            android:textSize="16dp"
                            android:textColor="@color/theme"
                            android:layout_marginTop="10dp"
                            android:background="@drawable/bg_round_5_white"
                            android:backgroundTint="@color/twhite"
                            android:gravity="center"
                            android:layout_width="74dp"
                            android:layout_height="28dp"
                            app:layout_constraintStart_toStartOf="@id/integralTitleView"
                            app:layout_constraintTop_toBottomOf="@id/integralView"/>

                        <com.biguo.utils.widget.StyleConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="80dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            app:bgGradientStartColor="@color/white"
                            app:shadowColor="@color/gray_gradient_1_bg"
                            app:shadowWidth="10dp"
                            app:all_round="10dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/exchangeView"
                            app:layout_constraintEnd_toEndOf="parent">

                            <LinearLayout
                                android:id="@+id/dayLuckDrawLayout"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toStartOf="@id/lineView"
                                app:layout_constraintBottom_toBottomOf="parent">
                                <TextView
                                    android:text="每日抽奖"
                                    android:textColor="@color/tblack"
                                    android:textSize="14dp"
                                    android:drawableStart="@drawable/prize_box"
                                    android:drawablePadding="4dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                                <TextView
                                    android:text="每日免费抽奖活动"
                                    android:textSize="10dp"
                                    android:textColor="@color/tblack3"
                                    android:layout_marginTop="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                            </LinearLayout>

                            <View
                                android:id="@+id/lineView"
                                android:background="@color/gray"
                                android:layout_marginTop="12dp"
                                android:layout_marginBottom="12dp"
                                android:layout_width="1dp"
                                android:layout_height="match_parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"/>

                            <LinearLayout
                                android:id="@+id/integralLuckDrawLayout"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                app:layout_constraintStart_toEndOf="@id/lineView"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent">
                                <TextView
                                    android:text="积分抽奖"
                                    android:textColor="@color/tblack"
                                    android:textSize="14dp"
                                    android:drawableStart="@drawable/yoyo"
                                    android:drawablePadding="4dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                                <TextView
                                    android:text="积分抽奖可兑好礼"
                                    android:textSize="10dp"
                                    android:textColor="@color/tblack3"
                                    android:layout_marginTop="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"/>

                            </LinearLayout>
                        </com.biguo.utils.widget.StyleConstraintLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/taskRecyclerView"
                        android:nestedScrollingEnabled="false"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:listitem="@layout/task_content_item"
                        app:layout_constraintTop_toBottomOf="@id/headLayout"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

                </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</FrameLayout>