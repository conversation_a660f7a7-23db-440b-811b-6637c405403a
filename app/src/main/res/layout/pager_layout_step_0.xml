<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">
        <ImageView
            android:id="@+id/imageView"
            android:src="@drawable/report_class_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/titleView"
            android:text="您现在的自考状态？"
            android:textColor="@color/tblack"
            android:textSize="26dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/normalText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imageView"
            app:layout_constraintEnd_toEndOf="parent"/>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/unReportView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="20dp"
            android:paddingStart="12dp"
            android:paddingTop="32dp"
            android:paddingEnd="20dp"
            android:paddingBottom="32dp"
            android:background="@drawable/bg_round_10_gray"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/okReportView">
            <ImageView
                android:id="@+id/unReportLabelView"
                android:src="@drawable/report_match_organization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/unReportNameView"
                android:text="未报班"
                android:textSize="21dp"
                android:textColor="@color/tblack"
                android:layout_marginStart="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toEndOf="@+id/unReportLabelView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/recommendNameView"/>

            <TextView
                android:text=""
                android:textSize="14dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                app:layout_constraintStart_toEndOf="@+id/unReportNameView"
                app:layout_constraintTop_toTopOf="@+id/unReportNameView"
                app:layout_constraintBottom_toBottomOf="@+id/unReportNameView"
                style="@style/lightText"/>

            <TextView
                android:id="@+id/recommendNameView"
                android:text="为您提供专属备考指导"
                android:textSize="14dp"
                android:textColor="@color/tblack"
                android:letterSpacing="-0.1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="@+id/unReportNameView"
                app:layout_constraintTop_toBottomOf="@+id/unReportNameView"
                app:layout_constraintBottom_toBottomOf="parent"
                style="@style/lightText"/>

            <TextView
                android:text=""
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:letterSpacing="-0.1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                app:layout_constraintStart_toEndOf="@+id/recommendNameView"
                app:layout_constraintTop_toTopOf="@+id/recommendNameView"
                app:layout_constraintBottom_toBottomOf="@+id/recommendNameView"
                style="@style/lightText"/>

            <ImageView
                android:src="@drawable/report_match_organization_enter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/okReportView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:paddingStart="12dp"
            android:paddingTop="32dp"
            android:paddingEnd="20dp"
            android:paddingBottom="32dp"
            android:background="@drawable/bg_round_10_gray"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/unReportView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/placeholder2View">
            <ImageView
                android:id="@+id/okReportLabelView"
                android:src="@drawable/report_match_tiku"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/okReportNameView"
                android:text="已报班"
                android:textSize="21dp"
                android:textColor="@color/tblack"
                android:layout_marginStart="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toEndOf="@+id/okReportLabelView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/matchNameView"/>

            <TextView
                android:text=""
                android:textSize="14dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                app:layout_constraintStart_toEndOf="@+id/okReportNameView"
                app:layout_constraintTop_toTopOf="@+id/okReportNameView"
                app:layout_constraintBottom_toBottomOf="@+id/okReportNameView"
                style="@style/lightText"/>

            <TextView
                android:id="@+id/matchNameView"
                android:text="为您匹配考试题库"
                android:textSize="14dp"
                android:textColor="@color/tblack"
                android:letterSpacing="-0.1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="@+id/okReportNameView"
                app:layout_constraintTop_toBottomOf="@+id/okReportNameView"
                app:layout_constraintBottom_toBottomOf="parent"
                style="@style/lightText"/>

            <TextView
                android:text=""
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:letterSpacing="-0.1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                app:layout_constraintStart_toEndOf="@+id/matchNameView"
                app:layout_constraintTop_toTopOf="@+id/matchNameView"
                app:layout_constraintBottom_toBottomOf="@+id/matchNameView"
                style="@style/lightText"/>

            <ImageView
                android:src="@drawable/report_match_tiku_enter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/placeholder2View"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            app:layout_constraintVertical_weight="2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/okReportView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>