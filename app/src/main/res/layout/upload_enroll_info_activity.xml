<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/scrollView"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <TextView
                android:id="@+id/hintView"
                android:text="证件信息仅用于学历提升报名使用，个人信息"
                android:textColor="@color/tblack"
                android:textSize="12dp"
                android:background="@color/gray"
                android:drawableStart="@drawable/enroll_info_pledge"
                android:drawablePadding="4dp"
                android:paddingStart="@dimen/screenSpace"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="30dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace">
                <com.dep.biguo.widget.DiversificationTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="*姓名"
                    android:textColor="@color/tblack"
                    android:textSize="16dp"
                    app:start="0"
                    app:end="1"
                    app:changeColor="@color/theme"/>

                <EditText
                    android:id="@+id/inputNameView"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:layout_marginStart="10dp"
                    android:gravity="end"
                    android:lines="1"
                    android:maxLength="10"
                    android:hint="请输入您的姓名"
                    android:textSize="16dp"
                    android:textStyle="normal"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:background="@color/tran" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:background="@drawable/split_bottom_line"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace">
                <com.dep.biguo.widget.DiversificationTextView
                    android:id="@+id/inputIDCardTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="*身份证号码"
                    android:textColor="@color/tblack"
                    android:textSize="16dp"
                    app:start="0"
                    app:end="1"
                    app:changeColor="@color/theme"/>

                <EditText
                    android:id="@+id/inputIDCardView"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:layout_marginStart="10dp"
                    android:gravity="end"
                    android:lines="1"
                    android:maxLength="20"
                    android:labelFor="@+id/inputIDCardTitleView"
                    android:hint="请输入您的身份证号码"
                    android:textSize="16dp"
                    android:textStyle="normal"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:digits="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
                    android:inputType="number"
                    android:background="@color/tran" />
            </LinearLayout>

            <com.dep.biguo.widget.DiversificationTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:text="*蓝底照片"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                app:start="0"
                app:end="1"
                app:changeColor="@color/theme"/>
            <TextView
                android:text="*JPG格式，480*640，300DP"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/IDPhotoRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/upload_enroll_info_image_item"
                tools:itemCount="1"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="1"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:text="*身份证正反面"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                app:start="0"
                app:end="1"
                app:changeColor="@color/theme"/>
            <TextView
                android:text="*请确保证件清晰完整"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/IDCardRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/upload_enroll_info_image_item"
                tools:itemCount="2"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:text="*学历证书"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                app:start="0"
                app:end="1"
                app:changeColor="@color/theme"/>
            <TextView
                android:text="*报读专升本层次学生需提供教育部学历证书电子注册备案表；未获得专科及以上毕业证书的考生，须提交教育部学籍在线验证报告，请确保证件清晰完整（根据报读形式上传任一类型证件）"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/certificateRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/upload_enroll_info_image_item"
                tools:itemCount="2"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:text="*异地证明"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                app:start="0"
                app:end="1"
                app:changeColor="@color/theme"/>
            <TextView
                android:text="*上传任一类型证件即可，请确保证件清晰完整"
                android:textSize="12dp"
                android:textColor="@color/tblack2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/unLocalRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginEnd="@dimen/screenSpace"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/upload_enroll_info_image_item"
                tools:itemCount="5"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:text="其他"
                android:textColor="@color/tblack"
                android:textSize="16dp"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/otherRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/screenSpace"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="@dimen/screenSpace"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/upload_enroll_info_image_item"
                tools:itemCount="1"
                app:spanCount="1"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"/>

            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/commitView"
                android:text="确定"
                android:textSize="16dp"
                android:textColor="@color/twhite"
                android:gravity="center"
                android:layout_marginTop="40dp"
                android:layout_marginBottom="20dp"
                android:layout_width="180dp"
                android:layout_height="40dp"
                app:bgGradientStartColor="#4875FF"
                app:all_round="20dp"
                android:layout_gravity="center"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>