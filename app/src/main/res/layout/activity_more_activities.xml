<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bgc"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <!-- ��题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="2dp">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="7dp"
            android:layout_height="14dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"
            android:src="@drawable/arrow_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="更多活动"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="130dp">

        <!-- 1. 背景图片 (包含渐变和插画) -->
        <!-- 假设 background_banner 是一个包含整个背景和人物插画的图片 -->
        <ImageView
            android:id="@+id/iv_background_banner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/center_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription"
            tools:src="@tools:sample/backgrounds/scenic" />



        <!-- 3. 搜索栏容器 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_search_bar"
            android:layout_width="345dp"
            android:layout_height="40dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_search_bar_container"
            android:elevation="2dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- 3.1 搜索图标 (圆圈内的放大镜图标) -->
            <ImageView
                android:id="@+id/iv_search_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="15dp"
                android:src="@drawable/ic_search"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <!-- 3.2 搜索按钮 -->
            <TextView
                android:id="@+id/btn_search"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:background="@drawable/bg_search_button_unified"
                android:gravity="center"
                android:text="搜索"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- 3.3 搜索输入框 -->
            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:background="@null"
                android:hint="搜索关键字，查询活动"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:paddingStart="0dp"
                android:paddingEnd="0dp"
                android:singleLine="true"
                android:textColor="#333333"
                android:textColorHint="#AAAAAA"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_search"
                app:layout_constraintStart_toEndOf="@id/iv_search_icon"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 筛选栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white">

        <!-- 形式筛选 -->
        <LinearLayout
            android:id="@+id/ll_filter_form"
            style="@style/FilterButton"
            android:layout_marginStart="15dp"
            android:background="@drawable/bg_filter_button_selected"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_filter_time"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_filter_form"
                style="@style/FilterButtonText.Selected"
                android:text="形式" />

            <ImageView
                style="@style/FilterButtonIcon"
                android:src="@drawable/arrow_down"
                android:tint="@color/theme_red" />
        </LinearLayout>

        <!-- 时间筛选 -->
        <LinearLayout
            android:id="@+id/ll_filter_time"
            style="@style/FilterButton"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_filter_button_default"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_filter_type"
            app:layout_constraintStart_toEndOf="@id/ll_filter_form"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_filter_time"
                style="@style/FilterButtonText"
                android:text="时间" />

            <ImageView
                style="@style/FilterButtonIcon"
                android:src="@drawable/arrow_down"
                android:tint="@color/gray_text" />
        </LinearLayout>

        <!-- 类型筛选 -->
        <LinearLayout
            android:id="@+id/ll_filter_type"
            style="@style/FilterButton"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_filter_button_default"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ll_filter_sort"
            app:layout_constraintStart_toEndOf="@id/ll_filter_time"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_filter_type"
                style="@style/FilterButtonText"
                android:text="类型" />

            <ImageView
                style="@style/FilterButtonIcon"
                android:src="@drawable/arrow_down"
                android:tint="@color/gray_text" />
        </LinearLayout>

        <!-- 排序筛选 -->
        <LinearLayout
            android:id="@+id/ll_filter_sort"
            style="@style/FilterButton"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_filter_button_default"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/ll_filter_type"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_filter_sort"
                style="@style/FilterButtonText"
                android:text="排序" />

            <ImageView
                style="@style/FilterButtonIcon"
                android:src="@drawable/arrow_down"
                android:tint="@color/gray_text" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/line" />

    <!-- 活动数量显示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingHorizontal="15dp">

        <TextView
            android:id="@+id/tv_total_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="49项活动"
            android:textColor="#333333"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_activities"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingHorizontal="15dp"
            android:paddingTop="8dp" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 搜索容器（隐藏状态） -->
    <LinearLayout
        android:id="@+id/ll_search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone" />

    <!-- 筛选按钮（隐藏，兼容现有代码） -->
    <TextView
        android:id="@+id/tv_filter"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- 搜索图标（隐藏，兼容现有代码） -->
    <ImageView
        android:id="@+id/iv_search"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</LinearLayout>