<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.CircleDetailActivity" />
    </data>
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="horizontal"
            android:minHeight="44dp"
            android:fitsSystemWindows="true"
            android:gravity="center_vertical"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/backView"
                android:ellipsize="end"
                android:minWidth="36dp"
                android:onClick="@{onClickListener}"
                android:drawableStart="@drawable/arrow_back"
                android:paddingStart="10dp"
                android:paddingEnd="0dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"/>

            <com.dep.biguo.widget.RoundedImageView
                android:id="@+id/avatorView"
                android:src="@drawable/default_avatar"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:radius="32dp"/>

            <TextView
                android:id="@+id/nicknameView"
                android:textColor="@color/tblack"
                android:textSize="16dp"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginHorizontal="10dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                style="@style/normalText"/>

            <ImageView
                android:id="@+id/shareView"
                android:src="@drawable/share_circle"
                android:minWidth="36dp"
                android:onClick="@{onClickListener}"
                android:paddingVertical="12dp"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"/>

        </LinearLayout>

        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeView"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                tools:listitem="@layout/circle_detail_head"
                tools:itemCount="1"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        </com.dep.biguo.widget.SmartRefreshLayout>

        <com.biguo.utils.widget.StyleLinearLayout
            android:id="@+id/conlComment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:background="@color/white"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:paddingLeft="@dimen/app_space"
            android:paddingRight="@dimen/app_space"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="-5dp"
            android:layout_marginEnd="-5dp"
            app:bgGradientStartColor="@color/white"
            app:shadowWidth="5dp"
            app:shadowColor="@color/gray_gradient_0_bg"
            app:shadowOffsetY="-5dp">
            <TextView
                android:id="@+id/inputCommentView"
                android:textColor="@color/tblack"
                android:textColorHint="@color/tblack3"
                android:textSize="14dp"
                android:maxLines="5"
                android:maxLength="100"
                android:hint="我来说两句..."
                android:lines="1"
                android:gravity="center_vertical"
                android:background="@drawable/bg_round_200_bgc"
                android:onClick="@{onClickListener}"
                android:paddingStart="10dp"
                android:paddingTop="2dp"
                android:paddingEnd="10dp"
                android:paddingBottom="2dp"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="32dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/sendView"/>


            <CheckBox
                android:id="@+id/goodView"
                android:text="0"
                android:textSize="14dp"
                android:textColor="@color/tblack2"
                android:gravity="center"
                android:drawableStart="@drawable/like_check"
                android:button="@null"
                android:drawablePadding="4dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:onClick="@{onClickListener.onClick}"
                android:layout_width="wrap_content"
                android:layout_height="32dp"/>

            <TextView
                android:id="@+id/replayView"
                android:text="0"
                android:textSize="14dp"
                android:textColor="@color/tblack2"
                android:gravity="center"
                android:drawableStart="@drawable/circle_icon_replay"
                android:drawablePadding="4dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:onClick="@{onClickListener.onClick}"
                android:layout_width="wrap_content"
                android:layout_height="32dp"/>
        </com.biguo.utils.widget.StyleLinearLayout>
    </LinearLayout>
</layout>