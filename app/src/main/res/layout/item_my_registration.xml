<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_marginHorizontal="15dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp">

        <!-- 关键改动：用 FrameLayout 包裹 ImageView -->
        <FrameLayout
            android:id="@+id/imageContainer"
            android:layout_width="120dp"
            android:layout_height="120dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/ivActivityImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/ShapeAppearance.App.CornerSize5dp"
                tools:ignore="ContentDescription"
                tools:src="@tools:sample/backgrounds/scenic" />

        </FrameLayout>

        <!-- 标题，约束到新的 FrameLayout -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="#333333"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/imageContainer"
            app:layout_constraintTop_toTopOf="@id/imageContainer"
            tools:text="创意市集手作体验活动ssssssssssssssssssssssssssssssssssssssss" />

        <!-- 时间信息 -->
        <TextView
            android:id="@+id/tvTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:drawableStart="@mipmap/time"
            android:drawablePadding="5dp"
            android:drawableTint="@color/gray2"
            android:gravity="center_vertical"
            android:textColor="#999999"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="@id/tvTitle"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:text="2025.06.23 14:00-16:00" />

        <!-- 地点信息 -->
        <TextView
            android:id="@+id/tvLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:drawableStart="@mipmap/address"
            android:drawablePadding="5dp"
            android:drawableTint="@color/gray2"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="#999999"
            android:textSize="13sp"
            app:layout_constraintEnd_toEndOf="@id/tvTitle"
            app:layout_constraintStart_toStartOf="@id/tvTitle"
            app:layout_constraintTop_toBottomOf="@id/tvTime"
            tools:text="创意园区A区ssssssssssssssssssssssssssssss" />


        <!-- 状态/操作按钮 -->
        <TextView
            android:id="@+id/btnStatus"
            android:layout_width="80dp"
            android:layout_height="28dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/bg_status_pending_stroke"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="待审核"
            android:textColor="#F44336"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>