<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.dialog.SelectCounsellingZixuanDialog" />
    </data>
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        xmlns:tools="http://schemas.android.com/tools"
        android:background="@drawable/bg_round_top_10_white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/closeView"
                android:src="@drawable/close"
                android:padding="16dp"
                android:scaleX="1.2"
                android:scaleY="1.2"
                android:onClick="@{onClickListener}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:tint="@color/tblack2"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <ImageView
                android:id="@+id/thumbnailView"
                android:src="@color/bgc"
                android:layout_marginTop="20dp"
                android:layout_marginStart="10dp"
                android:layout_width="100dp"
                android:layout_height="100dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/titleView"
                android:text="计算机科学与技术"
                android:textSize="18dp"
                android:textColor="@color/tblack"
                android:layout_marginStart="10dp"
                android:maxLines="2"
                android:ellipsize="end"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toEndOf="@+id/thumbnailView"
                app:layout_constraintTop_toTopOf="@id/thumbnailView"
                app:layout_constraintEnd_toStartOf="@id/closeView"/>

            <TextView
                android:id="@+id/subtitleView"
                android:text="专业老师全程跟踪指导"
                android:textSize="12dp"
                android:textColor="@color/tblack3"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="10dp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="@id/titleView"
                app:layout_constraintTop_toBottomOf="@id/titleView"
                app:layout_constraintEnd_toEndOf="parent"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/priceView"
                android:text="¥260.00/科"
                android:textSize="14dp"
                android:textColor="@color/theme"
                android:includeFontPadding="false"
                android:layout_marginTop="5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:startChar="¥"
                app:endChar="/"
                app:size="21dp"
                app:layout_constraintStart_toStartOf="@id/titleView"
                app:layout_constraintBottom_toTopOf="@+id/descView"/>

            <TextView
                android:id="@+id/descView"
                android:text="-"
                android:textSize="10dp"
                android:textColor="@color/tblack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="@id/titleView"
                app:layout_constraintBottom_toBottomOf="@id/thumbnailView"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/lineView"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="@color/gray"
            android:layout_marginStart="@dimen/screenSpace"
            android:layout_marginEnd="@dimen/screenSpace"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/thumbnailView"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="264dp"
            android:padding="10dp"
            tools:listitem="@layout/integral_exchange_item"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/lineView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/exchangeView"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        <TextView
            android:id="@+id/mergeBuyView"
            android:text="确认报考科目"
            android:textSize="16dp"
            android:textColor="@color/twhite"
            android:gravity="center"
            android:background="@drawable/bg_round_200_theme"
            android:layout_marginBottom="30dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:onClick="@{onClickListener}"
            app:layout_constraintHorizontal_weight="3"/>

    </LinearLayout>
</layout>