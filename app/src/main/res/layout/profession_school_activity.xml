<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ProfessionSchoolActivity" />
    </data>
    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:tools="http://schemas.android.com/tools"
        android:fitsSystemWindows="true"
        android:background="@color/white">

        <TextView
            android:id="@+id/tvClose"
            android:drawableStart="@drawable/arrow_back"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:onClick="@{onClickListener}"/>

        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/stepView"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:layout_below="@+id/tvClose"
            android:layout_alignParentStart="true"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:visibility="gone"
            app:all_round="2dp"
            app:bgGradientStartWeight="1"
            app:bgGradientEndWeight="0"
            app:bgGradientStartColor="@color/theme"
            app:bgGradientEndColor="@color/theme_alpha_5"/>

        <LinearLayout
            android:id="@+id/stepMessageLayout"
            android:layout_below="@id/tvClose"
            android:layout_alignParentStart="true"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="请选择在读或想考的院校与专业～"
                android:textSize="20dp"
                android:textColor="@color/tblack"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/pViewProfession"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_alignParentEnd="true"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:background="@color/white"
            android:layout_below="@+id/stepMessageLayout"
            android:layout_centerHorizontal="true"
            android:paddingTop="20dp">

            <RelativeLayout
                android:id="@+id/rl_senction"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:gravity="center_vertical"
                android:background="@drawable/bg_item_rv_school"
                android:layout_alignParentTop="true">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="@dimen/dp_160"
                    android:layout_height="match_parent">
                    <TextView
                        android:id="@+id/zkView"
                        android:text="自考"
                        android:textColor="@color/tblack"
                        android:textSize="16dp"
                        android:padding="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:onClick="@{onClickListener}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ckView"
                        app:layout_constraintHorizontal_chainStyle="packed"/>

                    <TextView
                        android:id="@+id/ckView"
                        android:text="成考"
                        android:textColor="@color/tblack2"
                        android:textSize="16dp"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:padding="10dp"
                        android:onClick="@{onClickListener}"
                        android:layout_marginStart="30dp"
                        app:layout_constraintStart_toEndOf="@id/zkView"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <ImageView
                        android:id="@+id/indicatorView"
                        android:layout_width="20dp"
                        android:layout_height="wrap_content"
                        android:background="@drawable/live_indicator"
                        android:layout_marginBottom="6dp"
                        app:layout_constraintStart_toStartOf="@id/zkView"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@id/zkView"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvCurAddress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:layout_marginRight="@dimen/dp_20"
                    android:drawableLeft="@drawable/current_address"
                    android:drawablePadding="@dimen/dp_2"
                    android:text="@string/tv_click_select"
                    android:textColor="@color/theme"
                    android:textSize="14dp"
                    android:onClick="@{onClickListener}"/>
            </RelativeLayout>

           <androidx.viewpager2.widget.ViewPager2
               android:id="@+id/viewPager"
               android:layout_width="match_parent"
               android:layout_height="match_parent"
               android:layout_below="@+id/rl_senction"/>
        </RelativeLayout>
    </RelativeLayout>
</layout>
