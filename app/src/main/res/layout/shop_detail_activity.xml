<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ShopDetailActivity" />
    </data>
    <com.dep.biguo.widget.SmartRefreshLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/swipeLayout"
        android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fillViewport="true">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:id="@+id/coverView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="211dp"
                        android:gravity="center_horizontal"
                        android:orientation="vertical" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <LinearLayout
                            android:gravity="bottom"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <com.dep.biguo.widget.DiversificationTextView
                                android:id="@+id/priceView"
                                android:text="¥1000"
                                android:textSize="16dp"
                                android:textColor="@color/theme"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:startChar="¥"
                                app:size="34dp"/>


                            <com.dep.biguo.widget.DiversificationTextView
                                android:id="@+id/originalPriceView"
                                android:text="原价¥"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="10dp"
                                android:textColor="@color/tblack3"
                                android:layout_marginStart="4dp"
                                android:layout_marginBottom="2dp"
                                app:line_position="middle"/>

                        </LinearLayout>

                        <TextView
                            android:id="@+id/courseNameView"
                            android:text="书名"
                            android:textSize="18dp"
                            android:textColor="@color/tblack"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>

                        <View
                            android:background="@color/bgc"
                            android:layout_marginTop="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="10dp"/>

                        <com.biguo.utils.widget.StyleTextView
                            android:text="购买中有任何问题均可添加客服微信咨询"
                            android:textSize="14dp"
                            android:textColor="@color/theme"
                            android:drawableStart="@drawable/book_guarantee"
                            android:drawablePadding="5dp"
                            android:paddingStart="15dp"
                            android:paddingTop="5dp"
                            android:paddingEnd="15dp"
                            android:paddingBottom="5dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:all_round="5dp"
                            app:bgGradientStartColor="@color/theme_alpha_5"/>

                        <LinearLayout
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:layout_marginTop="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:text="服务　"
                                android:textSize="14dp"
                                android:textColor="@color/tblack"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:text="包邮"
                                android:textSize="14dp"
                                android:textColor="@color/tblack"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <LinearLayout
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:layout_marginTop="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:text="发货　全国多仓　"
                                android:textSize="14dp"
                                android:textColor="@color/tblack"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <TextView
                                android:text="48小时内就近发货"
                                android:textSize="12dp"
                                android:textColor="@color/tblack"
                                style="@style/lightText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>
                        </LinearLayout>

                        <View
                            android:background="@color/bgc"
                            android:layout_marginTop="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="10dp"/>


                        <TextView
                            android:id="@+id/introduceView"
                            android:text="文字介绍"
                            android:textColor="@color/tblack2"
                            android:textSize="12dp"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="15dp"
                            android:layout_marginBottom="10dp"
                            android:nestedScrollingEnabled="false"/>

                    </LinearLayout>
                </LinearLayout>
            </ScrollView>
            <RelativeLayout
                android:id="@+id/bottomLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:elevation="20dp"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/customerView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:drawableTop="@drawable/group_customer_service"
                    android:onClick="@{onClickListener}"
                    android:drawablePadding="2dp"
                    android:paddingStart="15dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="15dp"
                    android:text="客服"
                    android:textColor="@color/tblack2"
                    android:textSize="10dp" />

                <TextView
                    android:id="@+id/shopCarView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableTop="@drawable/shop_car"
                    android:drawablePadding="2dp"
                    android:paddingStart="15dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="15dp"
                    android:layout_toEndOf="@id/customerView"
                    android:layout_centerVertical="true"
                    android:text="购物车"
                    android:textColor="@color/tblack2"
                    android:textSize="10dp"
                    android:onClick="@{onClickListener}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <com.biguo.utils.widget.StyleLinearLayout
                    android:id="@+id/buyLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    app:bgGradientEndColor="@color/theme"
                    app:bgGradientEndWeight="0.5"
                    app:bgGradientStartColor="#29D53E43"
                    app:bgGradientStartWeight="0.5">

                    <TextView
                        android:id="@+id/addCartView"
                        android:layout_width="120dp"
                        android:layout_height="54dp"
                        android:layout_toStartOf="@id/groupBuyView"
                        android:onClick="@{onClickListener}"
                        android:gravity="center"
                        android:text="加入购物车"
                        android:textColor="@color/theme"
                        android:textSize="16dp"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/buyView"
                        android:layout_width="120dp"
                        android:layout_height="54dp"
                        android:layout_alignParentEnd="true"
                        android:onClick="@{onClickListener}"
                        android:gravity="center"
                        android:text="立即购买"
                        android:textColor="@color/twhite"
                        android:textSize="16dp"/>
                </com.biguo.utils.widget.StyleLinearLayout>

            </RelativeLayout>
        </LinearLayout>
    </com.dep.biguo.widget.SmartRefreshLayout>
</layout>

