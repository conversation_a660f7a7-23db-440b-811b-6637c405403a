<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp_180"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_100"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:singleLine="true"
        android:maxEms="9"
        android:id="@+id/tv_name"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/dp_14"
        android:layout_marginTop="@dimen/dp_5"
        android:ellipsize="end"
        android:maxLines="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_image"
        tools:text="名字" />

    <TextView
        android:text="10000"
        android:id="@+id/tv_price"
        android:textSize="14dp"
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        android:layout_marginTop="@dimen/dp_5"
        android:textColor="@color/auxiliary_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_name"
        android:layout_marginTop="@dimen/dp_5"
        android:textColor="@color/black3"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_name" />

</androidx.constraintlayout.widget.ConstraintLayout>