<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ScholarshipRankActivity" />
    </data>
    <androidx.core.widget.NestedScrollView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:fillViewport="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:background="@color/white"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="50dp">
                <TextView
                    android:id="@+id/provinceView"
                    android:text="报考省份"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/selectProvinceView"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:hint="请选择报考省份"
                    android:textColorHint="@color/tblack3"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_round_10_gray"
                    android:onClick="@{onClickListener.onClick}"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:drawableEnd="@drawable/arrow_black_down"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"/>
            </LinearLayout>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:layout_height="50dp">
                <TextView
                    android:id="@+id/scoreView"
                    android:text="科目成绩"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <EditText
                    android:id="@+id/inputScoreView"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:hint="请输入科目成绩"
                    android:textColorHint="@color/tblack3"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_round_10_gray"
                    android:inputType="numberDecimal"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"/>
            </LinearLayout>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:layout_height="50dp">
                <TextView
                    android:id="@+id/numberView"
                    android:text="准考证号"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <EditText
                    android:id="@+id/inputNumberView"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:hint="请输入准考证号"
                    android:textColorHint="@color/tblack3"
                    android:gravity="center_vertical"
                    android:inputType="number"
                    android:background="@drawable/bg_round_10_gray"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"/>
            </LinearLayout>

            <LinearLayout
                android:gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:layout_height="50dp">
                <TextView
                    android:id="@+id/passwordView"
                    android:text="查询密码"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <EditText
                    android:id="@+id/inputPasswordView"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:hint="请输入查询密码(选填)"
                    android:textColorHint="@color/tblack3"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_round_10_gray"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"/>
            </LinearLayout>

            <com.biguo.utils.widget.StyleLinearLayout
                android:id="@+id/aliAccountLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_marginTop="30dp"
                android:onClick="@{onClickListener.onClick}"
                app:bgGradientStartColor="@color/gray"
                app:all_round="10dp">
                <ImageView
                    android:src="@drawable/ali_pay_66"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="8dp"
                    android:layout_width="30dp"
                    android:layout_height="30dp"/>

                <TextView
                    android:id="@+id/nameView"
                    android:text="点击设置奖金发放支付宝账号"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:layout_marginStart="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/aliPayAccountView"
                    android:text="（135****4647）"
                    android:textSize="14dp"
                    android:textColor="@color/tblack3"
                    android:lines="1"
                    android:ellipsize="end"
                    android:visibility="invisible"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/changeBindView"
                    android:text="换绑"
                    android:textSize="14dp"
                    android:textColor="@color/tblack"
                    android:drawableEnd="@drawable/arrow_right"
                    android:drawablePadding="4dp"
                    android:layout_marginEnd="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </com.biguo.utils.widget.StyleLinearLayout>

            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/commitView"
                android:text="提交"
                android:textSize="18dp"
                android:textColor="@color/twhite"
                android:gravity="center"
                android:onClick="@{onClickListener.onClick}"
                android:layout_marginTop="25dp"
                android:layout_width="295dp"
                android:layout_height="45dp"
                app:all_round="40dp"
                app:bgGradientStartColor="@color/theme"/>

            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/ruleView"
                android:text="奖学金规则"
                android:textColor="@color/tblack3"
                android:textSize="13dp"
                android:lineSpacingExtra="5dp"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="30dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:start="0"
                app:end="5"
                app:size="14dp"
                app:changeColor="@color/tblack"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>