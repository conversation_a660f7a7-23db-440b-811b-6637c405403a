<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.dialog.GoodsUpgradeDialog" />
    </data>
    <com.biguo.utils.widget.StyleConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:paddingBottom="15dp"
        app:all_round="10dp"
        app:bgGradientStartColor="@color/white">
        <ImageView
            android:id="@+id/closeView"
            android:src="@drawable/close"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:padding="10dp"
            android:onClick="@{onClickListener}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/titleView"
            android:text="套餐升级"
            android:textSize="18dp"
            android:textColor="@color/tblack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/closeView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/closeView"/>

        <TextView
            android:id="@+id/contentView"
            android:text="您已开通押密中的部分课程，升级将自动减免相关费用！升级后不再赠送已减免的课程"
            android:textSize="14dp"
            android:textColor="@color/tblack"
            style="@style/lightText"
            android:layout_marginTop="10dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleView"
            app:layout_constraintEnd_toEndOf="parent"/>


        <com.biguo.utils.widget.StyleLinearLayout
            android:id="@+id/upgradeLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginTop="20dp"
            android:padding="15dp"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/contentView"
            app:layout_constraintEnd_toEndOf="parent"
            app:all_round="10dp"
            app:borderWidth="1dp"
            app:borderColor="@color/theme"
            app:bgGradientStartColor="@color/theme_alpha_5">
            <com.dep.biguo.widget.SideAlignTextView
                android:id="@+id/buyTopicNameView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:leftText="已购题库"
                app:leftTextColor="@color/tblack2"
                app:leftStyle="light"
                app:textAlain="top"
                app:rightText="高频考点、VIP题库、模拟试题"
                app:rightTextColor="@color/tblack"
                app:rightStyle="light"/>

            <com.dep.biguo.widget.SideAlignTextView
                android:id="@+id/buyVideoNameView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:leftText="已购视频"
                app:leftTextColor="@color/tblack2"
                app:leftStyle="light"
                app:textAlain="top"
                app:rightText="串讲视频"
                app:rightTextColor="@color/tblack"
                app:rightStyle="light"/>

            <com.dep.biguo.widget.SideAlignTextView
                android:id="@+id/discountPriceView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:leftText="减免金额"
                app:leftTextColor="@color/tblack2"
                app:leftStyle="light"
                app:rightTextColor="@color/theme"
                app:rightText="-¥87"/>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/minPriceDescLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:background="#fff"
                    android:rotation="45"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="2dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />
                <TextView
                    android:id="@+id/minPriceDescView"
                    android:text="优惠金额超出产品价格，系统自动调整为最低支付金额"
                    android:textSize="10dp"
                    android:textColor="@color/tblack2"
                    android:letterSpacing="-0.1"
                    android:layout_marginTop="6dp"
                    style="@style/lightText"
                    android:gravity="center"
                    android:padding="4dp"
                    android:background="@drawable/bg_round_5_white"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.biguo.utils.widget.StyleLinearLayout>

        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/singleView"
            android:text="单独购买"
            android:textSize="16dp"
            android:textColor="@color/theme"
            android:gravity="center"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="10dp"
            android:onClick="@{onClickListener}"
            app:borderColor="@color/theme"
            app:borderWidth="1dp"
            app:all_round="36dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/upgradeLayout"
            app:layout_constraintEnd_toStartOf="@+id/groupBuyView"/>

        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/groupBuyView"
            android:text="立即参团"
            android:textSize="16dp"
            android:textColor="@color/twhite"
            android:gravity="center"
            android:layout_width="0dp"
            android:layout_height="36dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="15dp"
            android:onClick="@{onClickListener}"
            app:bgGradientStartColor="#F96B63"
            app:bgGradientEndColor="@color/theme"
            app:all_round="36dp"
            app:layout_constraintStart_toEndOf="@id/singleView"
            app:layout_constraintTop_toBottomOf="@id/upgradeLayout"
            app:layout_constraintEnd_toEndOf="parent"/>
    </com.biguo.utils.widget.StyleConstraintLayout>
</layout>
