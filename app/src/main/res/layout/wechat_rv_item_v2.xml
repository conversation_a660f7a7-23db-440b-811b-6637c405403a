<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_round_10_solid_stroke"
    android:layout_marginLeft="@dimen/dp_4"
    android:layout_marginRight="@dimen/dp_4"
    android:padding="@dimen/dp_5">

    <ImageView
        android:layout_marginStart="@dimen/dp_10"
        android:id="@+id/ivAvatar"
        android:layout_width="@dimen/dp_60"
        android:layout_height="@dimen/dp_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_marginStart="@dimen/dp_10"
        android:id="@+id/tvName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="x老师"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_13"
        app:layout_constraintBottom_toTopOf="@id/tvWechat"
        app:layout_constraintStart_toEndOf="@id/ivAvatar"
        app:layout_constraintTop_toTopOf="@id/ivAvatar" />

    <TextView
        android:paddingLeft="@dimen/dp_10"
        android:id="@+id/tvSchool"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="x校区"
        android:textColor="@color/tblack3"
        android:textSize="@dimen/sp_10"
        app:layout_constraintBottom_toBottomOf="@id/tvName"
        app:layout_constraintStart_toEndOf="@id/tvName"
        app:layout_constraintTop_toTopOf="@id/tvName" />

    <TextView
        android:id="@+id/tvWechat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="手机/微信号"
        android:textColor="@color/tblack2"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintStart_toStartOf="@id/tvName"
        app:layout_constraintTop_toBottomOf="@+id/tvName" />

    <LinearLayout
        android:orientation="vertical"
        android:paddingEnd="@dimen/dp_10"
        app:layout_constraintBottom_toBottomOf="@id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingBottom="@dimen/dp_10"
        android:paddingTop="@dimen/dp_10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@id/ivAvatar">

        <TextView
            android:layout_marginBottom="@dimen/dp_5"
            android:id="@+id/tvCopy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_200_theme"
            android:paddingLeft="@dimen/dp_10"
            android:paddingTop="@dimen/dp_3"
            android:paddingRight="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_3"
            android:text="@string/wechat_add"
            android:textColor="@color/twhite"
            android:textSize="@dimen/sp_12" />

        <TextView
            android:layout_marginTop="@dimen/dp_5"
            android:id="@+id/tvCall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_round_200_theme"
            android:paddingLeft="@dimen/dp_10"
            android:paddingTop="@dimen/dp_3"
            android:paddingRight="@dimen/dp_10"
            android:paddingBottom="@dimen/dp_3"
            android:text="@string/wechat_call_long"
            android:textColor="@color/twhite"
            android:textSize="@dimen/sp_12" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>