<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.GraduateProxyTableActivity" />
    </data>
   <androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_above="@id/buyView"
       android:background="@color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/inputLayout"
            android:orientation="vertical"
            android:paddingHorizontal="15dp"
            android:gravity="center_horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/input_bottom_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="真实姓名"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:paddingVertical="10dp"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>

                <com.dep.biguo.widget.ClearEditText
                    android:id="@+id/nameView"
                    android:textSize="16dp"
                    android:hint="请填写真实姓名"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:imeOptions="actionNext"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="10dp"
                    android:background="@color/tran"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>
            </LinearLayout>

            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/input_bottom_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="联系方式"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:paddingVertical="10dp"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>

                <com.dep.biguo.widget.ClearEditText
                    android:id="@+id/contactView"
                    android:textSize="16dp"
                    android:hint="请填写联系方式"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:imeOptions="actionNext"
                    android:paddingHorizontal="10dp"
                    android:inputType="phone"
                    android:paddingVertical="10dp"
                    android:background="@color/tran"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>
            </LinearLayout>

            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/input_bottom_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="申请毕业院校"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:paddingVertical="10dp"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>

                <com.dep.biguo.widget.ClearEditText
                    android:id="@+id/schoolView"
                    android:textSize="16dp"
                    android:hint="请填写申请毕业院校"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:imeOptions="actionNext"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="10dp"
                    android:background="@color/tran"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>
            </LinearLayout>

            <LinearLayout
                android:orientation="horizontal"
                android:background="@drawable/input_bottom_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:text="申请毕业专业"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:paddingVertical="10dp"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>

                <com.dep.biguo.widget.ClearEditText
                    android:id="@+id/professionView"
                    android:textSize="16dp"
                    android:hint="请填写申请毕业专业"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/tblack3"
                    android:imeOptions="actionNext"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="10dp"
                    android:background="@color/tran"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    style="@style/lightText"/>
            </LinearLayout>

            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/buyView"
                android:text="确认并支付"
                android:textSize="18dp"
                android:textColor="@color/twhite"
                android:gravity="center"
                android:onClick="@{onClickListener}"
                android:layout_marginTop="40dp"
                android:layout_marginBottom="20dp"
                android:layout_width="295dp"
                android:layout_height="45dp"
                app:all_round="40dp"
                app:bgGradientStartColor="@color/theme"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</layout>
