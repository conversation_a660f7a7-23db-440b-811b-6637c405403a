<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/ck_login_bg">

    <TextView
        android:id="@+id/tvLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_40"
        android:layout_marginTop="@dimen/dp_80"
        android:text="注册"
        android:textColor="@color/tblack"
        android:textSize="@dimen/sp_30"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="@dimen/dp_8"
        android:layout_height="@dimen/dp_8"
        android:background="@drawable/ck_circle_stroke_theme"
        app:layout_constraintStart_toEndOf="@+id/tvLabel"
        app:layout_constraintTop_toBottomOf="@+id/tvLabel"
        app:layout_constraintTop_toTopOf="@+id/tvLabel" />

    <com.dep.biguo.widget.ClearEditText
        android:id="@+id/etUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_20"
        android:layout_marginRight="@dimen/dp_40"
        android:background="@drawable/ck_et_bot_border"
        android:hint="@string/username_hint"
        android:inputType="number"
        android:maxLength="12"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvLabel"
        app:layout_constraintTop_toBottomOf="@+id/tvLabel" />

    <EditText
        android:id="@+id/etVerify"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/ck_et_bot_border"
        android:hint="@string/verify_hint"
        android:inputType="number"
        android:maxLength="20"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="@+id/etUsername"
        app:layout_constraintStart_toStartOf="@+id/etUsername"
        app:layout_constraintTop_toBottomOf="@+id/etUsername" />

    <TextView
        android:id="@+id/tvGetVerify"
        android:layout_width="@dimen/dp_80"
        android:layout_height="@dimen/dp_30"
        android:background="@drawable/ck_btn_round_2_bg"
        android:gravity="center"
        android:text="@string/get_verify_hint"
        android:textColor="@color/twhite"
        app:layout_constraintBottom_toBottomOf="@+id/etVerify"
        app:layout_constraintEnd_toEndOf="@id/etVerify"
        app:layout_constraintTop_toTopOf="@+id/etVerify" />

    <com.dep.biguo.widget.ClearEditText
        android:id="@+id/etPassword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/ck_et_bot_border"
        android:hint="@string/password_hint"
        android:inputType="textPassword"
        android:maxLength="20"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="@+id/etUsername"
        app:layout_constraintStart_toStartOf="@+id/etUsername"
        app:layout_constraintTop_toBottomOf="@+id/etVerify" />

    <com.dep.biguo.widget.ClearEditText
        android:id="@+id/etInvite"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        android:background="@drawable/ck_et_bot_border"
        android:hint="@string/invite_hint"
        android:inputType="text"
        android:maxLength="20"
        android:paddingTop="@dimen/dp_10"
        android:paddingBottom="@dimen/dp_10"
        android:textColor="@color/tblack"
        android:textColorHint="@color/tblack3"
        android:textSize="@dimen/sp_14"
        app:layout_constraintEnd_toEndOf="@+id/etUsername"
        app:layout_constraintStart_toStartOf="@+id/etUsername"
        app:layout_constraintTop_toBottomOf="@+id/etPassword" />

    <TextView
        android:id="@+id/tvRegister"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_50"
        android:background="@drawable/ck_btn_bg"
        android:gravity="center"
        android:paddingLeft="@dimen/dp_70"
        android:paddingTop="@dimen/dp_7"
        android:paddingRight="@dimen/dp_70"
        android:paddingBottom="@dimen/dp_7"
        android:text="@string/register"
        android:textColor="@color/twhite"
        android:textSize="@dimen/sp_14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etInvite" />


    <CheckBox
        android:id="@+id/tvAgree"
        android:button="@drawable/uncheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="@+id/etInvite"/>

    <TextView
        android:id="@+id/tvAgreeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="10dp"
        android:text="我已阅读并同意自考笔果题库《用户协议》、《隐私政策》"
        android:textColor="@color/tblack3"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toEndOf="@id/tvAgree"
        app:layout_constraintTop_toTopOf="@id/tvAgree"
        app:layout_constraintBottom_toBottomOf="@id/tvAgree"/>


</androidx.constraintlayout.widget.ConstraintLayout>