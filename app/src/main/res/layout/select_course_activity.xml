<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.core.widget.NestedScrollView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:orientation="vertical"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:paddingBottom="15dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/planView"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp"
                android:layout_gravity="center_horizontal"
                android:layout_width="match_parent"
                android:layout_height="50dp"/>

            <TextView
                android:text="我的考试科目"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                android:paddingBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/joinRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/select_course_item"
                tools:itemCount="3"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

            <TextView
                android:id="@+id/noJoinView"
                android:text="暂无考试科目"
                android:textSize="14dp"
                android:textColor="@color/tblack3"
                android:paddingBottom="10dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:text="点击添加科目"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                android:paddingTop="20dp"
                android:paddingBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/unJoinRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="true"
                tools:listitem="@layout/select_course_item"
                tools:itemCount="3"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

            <TextView
                android:id="@+id/noUnJoinView"
                android:text="暂无可添加科目"
                android:textSize="14dp"
                android:textColor="@color/tblack3"
                android:paddingBottom="10dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:text="已通过科目"
                android:textSize="16dp"
                android:textColor="@color/tblack"
                android:paddingTop="20dp"
                android:paddingBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/passRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/select_course_item"
                tools:itemCount="3"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

            <TextView
                android:id="@+id/noPassView"
                android:text="暂无已通过科目"
                android:textSize="14dp"
                android:textColor="@color/tblack3"
                android:paddingBottom="10dp"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</layout>