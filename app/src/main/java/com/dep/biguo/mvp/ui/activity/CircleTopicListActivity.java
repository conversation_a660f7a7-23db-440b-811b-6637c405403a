package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.databinding.CircleTopicListActivityBinding;
import com.dep.biguo.di.component.DaggerCircleTopicListComponent;
import com.dep.biguo.mvp.contract.CircleTopicListContract;
import com.dep.biguo.mvp.presenter.CircleTopicListPresenter;
import com.dep.biguo.mvp.ui.adapter.CircleTopicListAdapter;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.component.AppComponent;

import org.simple.eventbus.Subscriber;

import java.util.List;
import java.util.Map;

public class CircleTopicListActivity extends BaseLoadSirActivity<CircleTopicListPresenter> implements CircleTopicListContract.View {
    private static final String SELECT_TOPIC = "select_topic";

    private CircleTopicListActivityBinding binding;
    private NormalToolbarUtil toolbarUtil;

    private CircleTopicListAdapter adapter;

    private boolean isSelectTopic;

    public static void start(Context context){
        Intent intent = new Intent(context, CircleTopicListActivity.class);
        context.startActivity(intent);
    }

    public static void start(Context context, Map<Integer, CircleBean.Topic> selectMap, ActivityResultLauncher<Intent> launcher){
        Intent intent = new Intent(context, CircleTopicListActivity.class);
        intent.putExtra(SELECT_TOPIC, GsonUtils.toJson(selectMap));
        launcher.launch(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerCircleTopicListComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.circle_topic_list_activity);
        toolbarUtil = new NormalToolbarUtil(this)
                .setCenterText("热门话题")
                .setRightText("完成")
                .setRightOnClickListener(v -> {
                    Intent intent = new Intent();
                    intent.putExtra(CirclePushActivity.TOPIC, GsonUtils.toJson(adapter.getSelectList()));
                    setResult(RESULT_OK, intent);
                    finish();
                });

        adapter = new CircleTopicListAdapter();
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CircleBean.Topic topic = adapter.getItem(i);
            if(isSelectTopic){
                if(adapter.isSelect(topic)){
                    adapter.setCancel(topic);
                }else if(adapter.getSelectCount() == 2){
                    showMessage("最多选择2个话题");
                }else {
                    adapter.setSelect(topic);
                }
                adapter.notifyItemChanged(i);

            }else {
                CircleTopicActivity.start(CircleTopicListActivity.this, topic);
            }
        });
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));
        binding.swipeView.bindAdapter(adapter, binding.recyclerView, page -> mPresenter.getTopicList(page));
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        String selectJson = getIntent().getStringExtra(SELECT_TOPIC);
        Map<Integer, CircleBean.Topic> selectMap = GsonUtils.fromJson(selectJson, new TypeToken<Map<Integer, CircleBean.Topic>>(){}.getType());
        isSelectTopic = selectMap != null;
        toolbarUtil.setRightVisibility(isSelectTopic ? View.VISIBLE : View.GONE);
        adapter.setSelectTopic(isSelectTopic);
        adapter.setSelectMap(selectMap);
    }

    @Override
    public void onRequest() {
        mPresenter.getTopicList(binding.swipeView.getCurrentPage());
    }

    @Override
    public void getTopicListSuccess(List<CircleBean.Topic> list) {
        if(binding.swipeView.isRefreshing()){
            adapter.setNewData(list);
        }else {
            adapter.addData(list);
        }

        binding.swipeView.finishLoadMore(true, AppUtil.isEmpty(list));
    }

    @Subscriber(tag = EventBusTags.REFRESH_CIRCLE)
    public void autoRefresh(String tag){
        binding.swipeView.autoRefresh();
    }
}