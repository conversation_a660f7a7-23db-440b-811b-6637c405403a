package com.dep.biguo.mvp.model;

import android.app.Application;
import android.text.TextUtils;

import com.dep.biguo.bean.AppVersionBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ClassTeacherEvaluateBean;
import com.dep.biguo.mvp.contract.ClassTeacherEvaluateContract;
import com.dep.biguo.mvp.model.api.service.CircleService;
import com.dep.biguo.mvp.model.api.service.PracticeService;
import com.dep.biguo.mvp.model.api.service.UserService;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.integration.IRepositoryManager;
import com.jess.arms.mvp.BaseModel;

import java.io.File;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.http.Field;


@ActivityScope
public class ClassTeacherEvaluateModel extends BaseModel implements ClassTeacherEvaluateContract.Model {
    @Inject Gson mGson;
    @Inject Application mApplication;

    @Inject
    public ClassTeacherEvaluateModel(IRepositoryManager repositoryManager) {
        super(repositoryManager);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mGson = null;
        this.mApplication = null;
    }

    @Override
    public Observable<BaseResponse<List<ClassTeacherEvaluateBean>>> getClassTeacher() {
        return Observable.just(mRepositoryManager.obtainRetrofitService(UserService.class)
                        .getClassTeacher())
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }


    @Override
    public Observable<BaseResponse> accessClassTeacher(int teacher_id, int product_id, String content, List<String> filepath, int is_replace, int score_grade) {
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        builder.addFormDataPart("teacher_id", teacher_id+"");
        builder.addFormDataPart("product_id", product_id+"");
        builder.addFormDataPart("content", content);
        builder.addFormDataPart("is_replace", is_replace+"");
        builder.addFormDataPart("score_grade", score_grade+"");

        for (int i = 0; i < filepath.size(); i++) {
            File imageFile = new File(filepath.get(i));
            MediaType mediaType=MediaType.Companion.parse("image/*");
            RequestBody imageFileBody = RequestBody.create(imageFile, mediaType);
            builder.addFormDataPart("file_img_" + (i + 1), imageFile.getName(), imageFileBody);
        }

        return Observable.just(mRepositoryManager.obtainRetrofitService(UserService.class)
                        .accessClassTeacher(builder.build()))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }
}