package com.dep.biguo.mvp.contract;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.mvp.contract.view.ICommonView;
import com.jess.arms.mvp.IModel;

import java.util.List;

import io.reactivex.Observable;


public interface CircleTopicContract {
    interface View extends ICommonView {
        void getCircleDataSuccess(List<CircleBean.Moment> list);

        void commentSuccess(CircleBean.Moment topic);

        void deleteComment(CircleBean.Moment moment);
    }

    interface Model extends IModel {
        Observable<BaseResponse<List<CircleBean.Moment>>> getCircle(int topic_id, int filter, int page);

        Observable<BaseResponse> comment(int posts_id, int comment_id, String comment);

        Observable<BaseResponse> good(int id, int replay, int is_praise);

        Observable<BaseResponse> reportMoment(int post_id, String reason);

        Observable<BaseResponse> deleteMoment(int post_id);
    }
}
