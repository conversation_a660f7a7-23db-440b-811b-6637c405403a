package com.dep.biguo.mvp.ui.fragment;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.DividerItemDecoration;

import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.ProfessionSchoolFragmentBinding;
import com.dep.biguo.di.component.DaggerZkProfessionSchoolComponent;
import com.dep.biguo.mvp.contract.ZkProfessionSchoolContract;
import com.dep.biguo.mvp.presenter.ZkProfessionSchoolPresenter;
import com.dep.biguo.mvp.ui.activity.CityActivity;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.mvp.ui.adapter.ProfessionAdapter;
import com.dep.biguo.mvp.ui.adapter.SchoolAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.umengPush.UmengPushHelper;
import com.dep.biguo.widget.OneKeyService;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.kingja.loadsir.callback.Callback;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;


public class ZkProfessionSchoolFragment extends BaseLoadSirFragment<ZkProfessionSchoolPresenter> implements ZkProfessionSchoolContract.View, View.OnClickListener {
    public static final int BK_LAYER = 1;
    public static final int ZK_LAYER = 2;

    private ProfessionSchoolFragmentBinding binding;

    private ProfessionAdapter mProfessionAdapter = new ProfessionAdapter(new ArrayList<>());
    private SchoolAdapter mSchoolAdapter = new SchoolAdapter(new ArrayList<>());

    private ProvinceBean provinceBean;
    private CityBean.City city;
    private int mLayer = BK_LAYER; //默认本科

    public static ZkProfessionSchoolFragment newInstance(ProvinceBean provinceBean, CityBean.City city){
        ZkProfessionSchoolFragment fragment = new ZkProfessionSchoolFragment();
        fragment.provinceBean = provinceBean;
        fragment.city = city;
        return fragment;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerZkProfessionSchoolComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.profession_school_fragment, container, false);
        binding = DataBindingUtil.bind(view);
        binding.benKeView.setOnClickListener(this);
        binding.zhuanKeView.setOnClickListener(this);
        binding.gaoShengBenView.setVisibility(View.GONE);

        initViewProfession();
        return binding.getRoot();
    }

    private void initViewProfession() {
        mSchoolAdapter.bindToRecyclerView(binding.rvSchool);
        mSchoolAdapter.setEmptyView(getSmallEmptyView(getActivity()));
        mSchoolAdapter.setOnItemClickListener((adapter, view, position) -> {
            SchoolBean bean = mSchoolAdapter.getData().get(position);
            mSchoolAdapter.setSelectedBean(bean);
            mProfessionAdapter.setSchool_id(bean.getId());//设置学校ID，用以分辨不同学校的同种专业
            mPresenter.getProfession(bean.getName());
        });
        DividerItemDecoration divider = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL);
        divider.setDrawable(ContextCompat.getDrawable(getContext(), R.drawable.profession_v2_divider));
        binding.rvProfession.addItemDecoration(divider);
        mProfessionAdapter.setAppType(Constant.ZK);
        mProfessionAdapter.bindToRecyclerView(binding.rvProfession);
        mProfessionAdapter.setEmptyView(getSmallEmptyView(getActivity()));
        mProfessionAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (AppUtil.isEmpty(mProfessionAdapter.getData())) return;

            //当用户选择了专业，就要停止一键检测的服务了
            OneKeyService.stopService(getActivity().getApplication());

            SchoolBean schoolBean = mSchoolAdapter.getSelectSchool();
            ProfessionBean professionBean = mProfessionAdapter.getItem(position);
            mPresenter.bindProvinceProfession(city, schoolBean, professionBean);
        });


    }

    public View getSmallEmptyView(Context mContext) {
        TextView tvEmpty = new TextView(mContext);
        tvEmpty.setTextSize(13);
        tvEmpty.setText("空荡荡，啥子也没有哦^_^");
        tvEmpty.setTextColor(ContextCompat.getColor(mContext, R.color.tblack3));
        tvEmpty.setGravity(Gravity.CENTER);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER;
        tvEmpty.setLayoutParams(params);
        tvEmpty.setPadding(0, DisplayHelper.dp2px(mContext,30), 0, 0);
        return tvEmpty;
    }
    @Override
    public View initLoadSir() {
        return binding.pViewProfession;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onRequest() {
        if (null != UserCache.getSchool(Constant.ZK)) {
            if (null != UserCache.getProfession(Constant.ZK)) {
                mLayer = UserCache.getProfession(Constant.ZK).getLayer();
                binding.rvProfession.scrollToPosition(0);
                changeLayer(mLayer);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if(AppUtil.isEmpty(mSchoolAdapter.getData())) {
            mPresenter.getSchoolList();

        }else if(AppUtil.isEmpty(mProfessionAdapter.getData())){
            getSchoolListSuccess(mSchoolAdapter.getData());
        }
    }

    @Override
    public void onClick(View view) {
        if(view == binding.benKeView){
            if (AppUtil.isEmpty(mSchoolAdapter.getData())) return;
            mLayer = BK_LAYER;
            changerProfession(mLayer);

        }else if(view == binding.zhuanKeView){
            if (AppUtil.isEmpty(mSchoolAdapter.getData())) return;
            mLayer = ZK_LAYER;
            changerProfession(mLayer);
        }
    }

    private void changerProfession(int mLayer) {
        binding.rvProfession.scrollToPosition(0);
        changeLayer(mLayer);
        mPresenter.getProfession(mSchoolAdapter.getSelectSchool().getName());
    }

    @Override
    public int getProvinceId() {
        return provinceBean.getId();
    }

    @Override
    public int getLayer() {
        return mLayer;
    }

    @Override
    public void getSchoolListSuccess(List<SchoolBean> data) {
        if (AppUtil.isEmpty(data)) return;

        mSchoolAdapter.setNewData(data);

        SchoolBean selectSchool = null;
        SchoolBean cacheSchool = UserCache.getSchool(Constant.ZK);
        //有缓存，则找与缓存学校ID相同的那个学校
        if (cacheSchool != null) {
            for (SchoolBean school : data) {
                if (school.getId() == cacheSchool.getId()) {
                    selectSchool = school;
                    break;
                }
            }
        }
        //没有缓存 或 缓存的学校不在列表中，则默认选中第一个学校
        if(selectSchool == null){
            selectSchool = data.get(0);
        }

        //设置选中的学校
        mSchoolAdapter.setSelectedBean(selectSchool);
        //设置已选中学校的ID，用以分辨不同学校的同种专业
        mProfessionAdapter.setSchool_id(selectSchool.getId());
        //切换加载状态页的控制目标
        changeLoadSirTarget(binding.rvProfession, (Callback.OnReloadListener) v -> {
            showLoadingView();
            mPresenter.getProfession(mSchoolAdapter.getSelectSchool().getName());
        });
        //请求选中的
        mPresenter.getProfession(selectSchool.getName());
    }

    @Override
    public void getProfessionSuccess(List<ProfessionBean> data) {
        mProfessionAdapter.setNewData(data);
    }

    @Override
    public void bindSuccess(SchoolBean schoolBean,ProfessionBean professionBean) {
        boolean isFirstBind = UserCache.getCity(Constant.ZK) == null && UserCache.getCity(Constant.CK) == null;
        new UmengEventUtils(getActivity())
                .addParams("select_time", TimeFormatUtils.formatChinese(System.currentTimeMillis()/1000))
                .addParams("selected_college", schoolBean.getId())
                .addParams("selected_major", professionBean.getId())
                .pushEvent(UmengEventUtils.CLICK_SELECT_COLLEGE_AND_MAJOR);

        //缓存省份
        UserCache.cacheProvince(provinceBean);
        //缓存省份
        UserCache.cacheCity(city, Constant.ZK);
        //缓存学校
        UserCache.cacheSchool(schoolBean, Constant.ZK);
        //缓存专业
        UserCache.cacheProfession(professionBean, Constant.ZK);
        //注册推送标签
        UmengPushHelper.addExamInfoTag(getContext());
        //所选专业有变，首页选中的课程就要清空
        UserCache.cacheHomeCode("");

        UserCache.removeCourse();
        EventBus.getDefault().post(new ProfessionBean(), EventBusTags.CHANGE_PROFESSION);
        ArmsUtils.startActivity(MainActivity.class);

        if (isFirstBind) {
            MainAppUtils.loginSuccessStartSurvey();
        }

        for(Activity activity : AppManager.getAppManager().getActivityList()){
            if(activity instanceof CityActivity){
                activity.finish();
            }
        }
        getActivity().finish();
    }


    @Override
    public void changeLayer(int layer) {
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) binding.indicatorView.getLayoutParams();
        if(layer == BK_LAYER){
            layoutParams.startToStart = binding.benKeView.getId();
            layoutParams.endToEnd = binding.benKeView.getId();
        }else {
            layoutParams.startToStart = binding.zhuanKeView.getId();
            layoutParams.endToEnd = binding.zhuanKeView.getId();
        }
        binding.indicatorView.setLayoutParams(layoutParams);
    }


}