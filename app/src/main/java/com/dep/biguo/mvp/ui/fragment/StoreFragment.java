package com.dep.biguo.mvp.ui.fragment;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;

import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.KeyboardUtils;
import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.LayerBean;
import com.dep.biguo.bean.OrganizationRecommendBean;
import com.dep.biguo.bean.OrganizationStudyBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.databinding.StoreFragmentBinding;
import com.dep.biguo.di.component.DaggerStoreComponent;
import com.dep.biguo.dialog.FilterDownDialog;
import com.dep.biguo.mvp.contract.StoreContract;
import com.dep.biguo.mvp.presenter.StorePresenter;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.OrderListActivity;
import com.dep.biguo.mvp.ui.activity.OrganizationRecommendActivity;
import com.dep.biguo.mvp.ui.activity.OrganizationReservationHistoryActivity;
import com.dep.biguo.mvp.ui.activity.OrganizationStudyGoodsActivity;
import com.dep.biguo.mvp.ui.adapter.OrganizationStudyAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.widget.AlignGridItemDecoration;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.di.component.AppComponent;

import java.util.ArrayList;
import java.util.List;

public class StoreFragment extends BaseLoadSirFragment<StorePresenter> implements StoreContract.View, View.OnClickListener {
    private StoreFragmentBinding binding;
    private OrganizationStudyAdapter studyAdapter;

    private List<ProvinceBean> provinceList;//省份集合
    private List<SchoolBean> schoolList;//学校集合
    private List<LayerBean> layerList;//层次集合

    private ProvinceBean selectProvince;
    private SchoolBean selectSchool;
    private LayerBean selectLayer;

    private static final int province = 0;
    private static final int school = 1;
    private static final int layer = 2;

    private OrganizationRecommendBean recommendBean;

    public static StoreFragment newInstance() {
        return new StoreFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerStoreComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.store_fragment, container, false);
        binding = DataBindingUtil.bind(view);
        binding.setOnClickListener(this);

        studyAdapter = new OrganizationStudyAdapter();
        studyAdapter.setOnItemClickListener((adapter, childView, position) -> {
            OrganizationStudyBean.OrganizationItem item = studyAdapter.getItem(position);
            OrganizationStudyGoodsActivity.start(getContext(), item.getProduct_id(), true);
        });

        //监听输入框是否有获取焦点，从而显示搜索按钮
        binding.inputSearchView.setOnFocusChangeListener((v, hasFocus) -> {
            binding.searchView.setVisibility(hasFocus ? View.VISIBLE : View.INVISIBLE);

            int animatorRes = hasFocus ? R.animator.scale_show : R.animator.scale_hide;
            Animation animation = AnimationUtils.loadAnimation(getContext(), animatorRes);
            binding.searchView.startAnimation(animation);
        });

        //监听软键盘上的搜索按钮
        binding.inputSearchView.setOnEditorActionListener((v, actionId, event) -> {
            KeyboardUtils.hideKeyboard(binding.inputSearchView);
            onClick(binding.searchView);
            return true;
        });

        //去掉左右列表两边的间距，就是可以用的部分
        int usableWidth = DisplayHelper.getWindowWidth(getContext()) - binding.recyclerView.getPaddingStart() * 2;
        //可用的宽度除以每项的宽度，得到可以排列的个数
        int itemCount = usableWidth / DisplayHelper.dp2px(getContext(), 156);
        GridLayoutManager manager = (GridLayoutManager) binding.recyclerView.getLayoutManager();
        manager.setSpanCount(itemCount);
        binding.recyclerView.setLayoutManager(manager);
        binding.recyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));

        binding.swipeView.bindAdapter(studyAdapter, binding.recyclerView, page -> {
            KeyboardUtils.hideKeyboard(binding.inputSearchView);

            String search = binding.inputSearchView.getText().toString();
            int province_id = selectProvince == null ? 0 : selectProvince.getId();
            int school_id = selectSchool == null ? 0 : selectSchool.getId();
            int layer_id = selectLayer == null ? 0 : selectLayer.getId();
            mPresenter.getSearch(search, province_id, school_id, layer_id, page);
        });
        return view;
    }

    @Override
    public View initLoadSir() {
        return binding.recyclerViewLayout;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onRequest() {
        mPresenter.getHead(false);
        mPresenter.getSearch("", 0, 0, 0, binding.swipeView.getCurrentPage());
    }

    @Override
    public void onClick(View view) {
        if(view == binding.orderView){//我的订单
            if(!MainAppUtils.checkLogin(getContext())) return;
            OrderListActivity.start(getContext(), true);

        }else if(view == binding.reservationView){//我的预约
            if(!MainAppUtils.checkLogin(getContext())) return;
            OrganizationReservationHistoryActivity.start(getContext());

        }else if(view == binding.joinView){//机构入驻
            if(recommendBean == null) {
                mPresenter.getHead(true);
            }else {
                getHeadSuccess(recommendBean);
            }

        }else if(view == binding.listView){//机构列表
            //OrganizationRecommendActivity.start(getContext());

        }else if(view == binding.searchView){
            binding.swipeView.autoRefresh();

        }else if(view == binding.provinceLayout){//选择省份
            //点击了省份
            if(AppUtil.isEmpty(provinceList)){
                mPresenter.getProvince();
            }else {
                getProvince(provinceList);
            }

        }else if(view == binding.schoolLayout){//选择学习
            //点击了学校
            TextView[] checkView = {binding.provinceView};
            if(!checkHasInput(checkView))return;

            if(AppUtil.isEmpty(schoolList)){
                mPresenter.getSchool(selectProvince.getId(), 0, 0);
            }else {
                getSchool(schoolList);
            }

        }else if(view == binding.layerLayout){//选择层次
            //点击了层次
            TextView[] checkView = {binding.provinceView, binding.schoolView};
            if(!checkHasInput(checkView))return;

            if(AppUtil.isEmpty(layerList)){
                mPresenter.getLayer(selectSchool.getId(), 0, 0);
            }else {
                getLayer(layerList);
            }

        }
    }
    /**检查是否有选择前置条件
     * @param textView 需要检查的控件
     */
    private boolean checkHasInput(TextView[] textView){
        for(TextView itemView : textView) {
            if (itemView == binding.provinceView && selectProvince == null) {
                showMessage("请先选择省份");
                return false;
            }else if (itemView == binding.schoolView && selectSchool == null) {
                showMessage("请先选择院校");
                return false;
            }else if (itemView == binding.layerView && selectLayer == null) {
                showMessage("请先选择层次");
                return false;
            }
        }
        return true;
    }

    public void showPopupDialog(List<Object> list, TextView view, int selectType){
        if(AppUtil.isEmpty(list)){
            showMessage("暂无可选选项");
            return;
        }

        int delay = 0;
        if(binding.inputSearchView.hasFocus()){
            //当软件盘打开的情况下，延迟100毫秒，给隐藏软键盘留出一段时间，避免下拉弹窗的位置异常
            delay = 100;
            KeyboardUtils.hideKeyboard(binding.inputSearchView);
        }

        new Handler().postDelayed(() -> {
            FilterDownDialog downDialog = new FilterDownDialog(getContext());
            downDialog.setOnCheckListener((obj, position) -> {
                view.setText(getPopupItemName(obj));
                clearInput(selectType);
                select(obj, selectType);
            });
            downDialog.setOnDismissListener(() -> setSelecting(null));
            downDialog.setList(list);
            downDialog.setSelect(getSelect(selectType));
            downDialog.setHeight(binding.recyclerView.getHeight());
            downDialog.showAsDropDown(view);
            setSelecting(view);
        }, delay);
    }

    /**返回下拉弹窗的某一项内容
     * @param obj 可以传递ProvinceBean，SchoolBean，ProfessionBean，String
     * @return
     */
    private String getPopupItemName(Object obj){
        if(obj instanceof ProvinceBean){
            ProvinceBean bean = (ProvinceBean) obj;
            return bean.getName();

        }else if(obj instanceof SchoolBean){
            SchoolBean bean = (SchoolBean) obj;
            return bean.getName();

        }else if(obj instanceof LayerBean){
            LayerBean bean = (LayerBean) obj;
            return bean.getName();

        }else if(obj instanceof String){
            return obj.toString();
        }else {
            return "";
        }
    }

    /**当逆向选择时，需要清空输入的信息
     * @param selectType
     */
    public void clearInput(int selectType){
        if(selectType == province || selectType == school){
            //将与层次相关的信息删除掉
            layerList = null;
            selectLayer = null;
            binding.layerView.setText("层次");
        }
        if(selectType == province){
            //将与学校相关的信息删除掉
            schoolList = null;
            selectSchool = null;
            binding.schoolView.setText("院校");
        }
    }

    /**选中下拉弹窗的某一项内容
     * @param obj 可以传递ProvinceBean，SchoolBean，ProfessionBean，String
     */
    private void select(Object obj, int selectType){
        if(selectType == province){
            selectProvince = (ProvinceBean) obj;

        }else if(selectType == school){
            selectSchool = (SchoolBean) obj;

        }else if(selectType == layer){
            selectLayer = (LayerBean) obj;
        }

        binding.swipeView.autoRefresh();
    }

    /**返回所选中的那一项
     * @param selectType
     * @return
     */
    private Object getSelect(int selectType){
        if(selectType == province){
            return selectProvince;

        }else if(selectType == school){
            return selectSchool;

        }else if(selectType == layer){
            return selectLayer;
        }
        return null;
    }

    public void setSelecting(TextView selectingView){
        TextView[] textViews = {binding.provinceView, binding.schoolView, binding.layerView};
        for(TextView itemView : textViews){
            if(itemView == selectingView){
                int color = ResourcesCompat.getColor(getResources(), R.color.theme, getContext().getTheme());
                itemView.setTextColor(color);

                Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.arrow_black_up, getContext().getTheme());
                drawable = TintDrawableUtil.TintDrawable(drawable, color);
                itemView.setCompoundDrawablesRelative(null, null, drawable, null);

            }else {
                int color = ResourcesCompat.getColor(getResources(), R.color.tblack, getContext().getTheme());
                itemView.setTextColor(color);

                TextDrawableLoader.loadRight(getContext(), itemView, R.drawable.arrow_black_down);
            }
        }
    }

    @Override
    public void getProvince(List<ProvinceBean> list) {
        this.provinceList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.provinceView, province);
    }

    @Override
    public void getSchool(List<SchoolBean> list) {
        this.schoolList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.schoolView, school);
    }

    @Override
    public void getLayer(List<LayerBean> list) {
        this.layerList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.layerView, layer);
    }

    @Override
    public void getHeadSuccess(OrganizationRecommendBean bean) {
        this.recommendBean = bean;
        if (!TextUtils.isEmpty(bean.getXcx_path())) {//跳转小程序
            if (!MainAppUtils.checkLogin(getContext())) return;
            WxMinApplication.StartWechat(getContext(), bean.getXcx_path(), bean.getUrl());

        } else if (!TextUtils.isEmpty(bean.getUrl())) {//跳转H5页面
            HtmlActivity.start(getActivity(), bean.getUrl());
        }
    }

    @Override
    public void getSearchFail() {
        binding.swipeView.finishLoadMore(false);
    }

    @Override
    public void getSearchSuccess(List<OrganizationStudyBean.OrganizationItem> list) {
        if(binding.swipeView.isRefreshing()){
            studyAdapter.setNewData(list);
        }else {
            studyAdapter.addData(list);
        }

        binding.swipeView.finishLoadMore(true, AppUtil.isEmpty(list));
    }
}