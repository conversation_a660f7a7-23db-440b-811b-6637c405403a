package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;

import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.TruePaperNewItemBean;
import com.dep.biguo.databinding.TruePaperActivityBinding;
import com.dep.biguo.di.component.DaggerTruePaperComponent;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.TruePaperContract;
import com.dep.biguo.mvp.presenter.TruePaperPresenter;
import com.dep.biguo.mvp.ui.adapter.TruePaperAdapter;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.utils.ShareUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.utils.umengPush.UmengPushHelper;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.widget.ToolBar;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.component.AppComponent;
import com.umeng.socialize.UMShareAPI;

import org.simple.eventbus.Subscriber;

import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import static com.jess.arms.utils.Preconditions.checkNotNull;

public class TruePaperActivity extends BaseLoadSirActivity<TruePaperPresenter> implements TruePaperContract.View {
    private TruePaperActivityBinding binding;

    @Inject TruePaperAdapter mTruePaperAdapter;

    private Map<String, Object> paramsMap = PayParamsBean.init().getParamsMap();

    private int mPaperId;

    private int courseId;
    private String courseCode;
    private String courseName;

    public static void start(Context context,int courseId, String courseCode, String courseName){
        Intent intent = new Intent(context, TruePaperActivity.class);
        intent.putExtra(StartFinal.COURSE_ID, courseId);
        intent.putExtra(StartFinal.COURSE_CODE, courseCode);
        intent.putExtra(StartFinal.COURSE_NAME, courseName);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerTruePaperComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.true_paper_activity);
        mTruePaperAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (AppUtil.isEmpty(mTruePaperAdapter.getData())) return;
            TruePaperNewItemBean bean = mTruePaperAdapter.getItem(position);
            mPaperId = bean.getId();
            if (bean.getIs_show() == 0) {
                showPayDialog(bean);

            }else if (bean.getIs_show() == 2) {
                showShareDialog(bean);

            } else {
                startPractice(bean);
            }
        });

        binding.swipeView.bindAdapter(mTruePaperAdapter, binding.rvTruePaper, page -> {
            mPresenter.getTruePaper(page);
        });
        return 0;
    }


    @Override
    public View initLoadSir() {
        return binding.rvTruePaper;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        //因真题页面还可能经过自考的首页进入，绕过了选择课程这个步骤，因此需要传入课程代码和课程名称，当不传入的时候，则认为不是从自考首页进入的，可以取缓存里的
        courseId = getIntent().getIntExtra(StartFinal.COURSE_ID, 0);
        courseCode = getIntent().getStringExtra(StartFinal.COURSE_CODE);
        courseName = getIntent().getStringExtra(StartFinal.COURSE_NAME);
        if(TextUtils.isEmpty(courseCode) || TextUtils.isEmpty(courseName)){
            courseId = UserCache.getCourse().getId();
            courseCode = UserCache.getCourse().getCode();
            courseName = UserCache.getCourse().getName();
        }

        String courseNameAndCode = String.format("[%s]%s", courseCode, courseName);
        String topicType = String.format("「%s」", PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_TRUE));
        String title = String.format("%s\n%s", courseNameAndCode, topicType);
        int color = ResourcesCompat.getColor(getResources(), R.color.tblack3, getTheme());
        SpannableStringBuilder builder = SpannableUtil.setColorString(title, courseNameAndCode.length(), title.length(), color, 0.75f);
        new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText(builder)
                .setTextMaxLine(2)
                .setTarget(ToolBar.Builder.RIGHT)
                .setText("真题下载")
                .setDrawablesRes(new int[]{0,0,R.drawable.real_paper_download, 0})
                .setOnClickListener(v -> TruePaperDownloadFileActivity.start(TruePaperActivity.this, courseName, courseCode, courseId))
                .build();
    }

    @Override
    public void onRequest() {
        mPresenter.getTruePaper(binding.swipeView.getCurrentPage());
    }

    public void showPayDialog(TruePaperNewItemBean bean){
        paramsMap.clear();
        paramsMap.put(PayParamsBean.CODE, courseCode);
        paramsMap.put(PayParamsBean.TYPE, PayUtils.REAL_PAPER);
        paramsMap.put(PayParamsBean.EXAMS_REAL_PAPER_ID, bean.getId()+"");

        new DiscountPayDialog.Builder(this, PayUtils.REAL_PAPER)
                .setGoodsName(String.format("%s %s", bean.getName(), bean.getCourse_name()))
                .setShowGuobi(true)
                .setPrice(Float.parseFloat(bean.getPrice()))
                .setPayPrice(Float.parseFloat(bean.getPrice()))
                .setOnPayListener((joinGroup, discount, payType) -> mPresenter.payOrder(paramsMap, payType))
                .build()
                .show();

        //对显示支付弹窗进行埋点
        new UmengEventUtils(this)
                .addParams("pay_price",bean.getPrice())
                .pushEvent(UmengEventUtils.PAY_DIALOG_SHOW);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(!AppUtil.isEmpty(mTruePaperAdapter.getData())) {
            binding.swipeView.autoRefresh();
        }
    }

    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ToastUtils.show(message);
    }

    public void startPractice(TruePaperNewItemBean bean){
        if (bean.getTotal_nums() == 0) {
            showMessage("暂无数据");
            return;
        }

        //改版之后，防止用户升级到6.3.5版本出现无法从模拟模式切换到答题模式
        if(UserCache.getPracticeMode() == PracticeHelper.MODE_SIMU){
            UserCache.cachePracticeMode(PracticeHelper.MODE_DEFAULT);
        }
        PracticeManager practiceManager = new PracticeManager();
        practiceManager.mTitle = courseName;
        practiceManager.mSubTitle = bean.getName();
        practiceManager.mCode = courseCode;
        practiceManager.courseName = courseName;
        practiceManager.mMainType = PracticeHelper.PRACTICE_TRUE;
        practiceManager.mMainTypeName = PracticeHelper.getPracticeType(PracticeHelper.PRACTICE_TRUE);
        practiceManager.mPaperId = String.valueOf(bean.getId());
        practiceManager.mPaperName = bean.getName();
        practiceManager.mTopicType = 0;
        practiceManager.mTopicTypeName = "混合题型";
        practiceManager.mPracticeType = PracticeHelper.PRACTICE_TRUE;
        practiceManager.mPracticeMode = UserCache.getPracticeMode();
        practiceManager.mTrueShareLock = false;
        practiceManager.version = bean.getVersion();
        practiceManager.expire_time = bean.getRecord_expire_time();
        PracticeV3Activity.start(TruePaperActivity.this, practiceManager);
    }

    public void showShareDialog(TruePaperNewItemBean bean){
        new ShareDialog.Builder(this)
                .setShareUrl(String.format("%s&share_source=1", ShareUtil.getShareUrl()))
                .setShareDialogHint(getString(R.string.share_hint))
                .setShareTitle("笔果自考历年真题，助你考试通关!")
                .setShareContent(String.format("%s《%s》真 题", bean.getName(), bean.getCourse_name()))
                .setShareIcon(R.drawable.app_icon)
                .setOnShareListener(type -> {
                    new UmengEventUtils(TruePaperActivity.this)
                            .addParams("path", "APP：自考-》真题列表-》分享好友")
                            .addParams("platform", type)
                            .pushEvent(UmengEventUtils.CLICK_SHARE_APP);

                    mPresenter.shareUnlock();
                })
                .builder()
                .show();
    }

    public void getTruePaperFail(){
        binding.swipeView.finishRefresh();
        binding.swipeView.finishLoadMore(false);
    }

    @Override
    public void getTruePaperSuccess(List<TruePaperNewItemBean> list) {
        if(binding.swipeView.isRefreshing()){
            mTruePaperAdapter.setNewData(list);
        }else {
            mTruePaperAdapter.addData(list);
        }

        binding.swipeView.finishLoadMore(true, AppUtil.isEmpty(list));
    }

    @Override
    public void paySuccess() {
        UmengPushHelper.addBuyGoodTag(this, PayUtils.REAL_PAPER, courseCode); //注册友盟推送标签
        showMessage("购买成功");
        binding.swipeView.autoRefresh();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Subscriber(tag = EventBusTags.TRUEPAPER_UNLOCK)
    private void shareUnlock(String code) {
        if (mPresenter == null) return;
        binding.swipeView.autoRefresh();
    }

    @Override
    public int getPagerId() {
        return mPaperId;
    }

    @Override
    public String getCode() {
        return courseCode;
    }

    @Override
    public int getCourseId() {
        return courseId;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
    }
}
