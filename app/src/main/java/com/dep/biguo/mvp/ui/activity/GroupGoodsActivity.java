package com.dep.biguo.mvp.ui.activity;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.GroupGoodsActivityBinding;
import com.dep.biguo.dialog.GoodsUpgradeDialog;
import com.dep.biguo.mvp.ui.adapter.ViewPager2Adapter;
import com.dep.biguo.mvp.ui.fragment.BookFragment;
import com.dep.biguo.mvp.ui.fragment.PlanFragment;
import com.dep.biguo.mvp.ui.fragment.SkillTeacherFragment;
import com.dep.biguo.mvp.ui.fragment.VideoListFragment;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.dialog.H5Dialog;
import com.dep.biguo.utils.MathUtil;
import com.dep.biguo.utils.umengPush.UmengPushHelper;
import com.google.android.material.appbar.AppBarLayout;

import androidx.databinding.DataBindingUtil;
import androidx.core.widget.NestedScrollView;

import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.dep.biguo.R;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.GroupGoodInfoBean;
import com.dep.biguo.bean.RealInfoBean;
import com.dep.biguo.di.component.DaggerGroupGoodsComponent;
import com.dep.biguo.mvp.contract.GroupGoodsContract;
import com.dep.biguo.mvp.presenter.GroupGoodsPresenter;
import com.dep.biguo.mvp.ui.fragment.GroupGoodInfoFragment;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.biguo.utils.widget.FloatingImageView;
import com.dep.biguo.dialog.GroupAgainPayDialog;
import com.dep.biguo.dialog.GroupGoodsCommentDialog;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.wxapi.WxMinApplication;
import com.google.android.material.tabs.TabLayoutMediator;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.shuyu.gsyvideoplayer.builder.GSYVideoOptionBuilder;
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack;
import com.shuyu.gsyvideoplayer.utils.OrientationUtils;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.List;

public class GroupGoodsActivity extends BaseLoadSirActivity<GroupGoodsPresenter> implements GroupGoodsContract.View, View.OnClickListener {
    private GroupGoodsActivityBinding binding;

    private List<BaseFragment> fragmentList;

    private boolean isShowAgainPayDialog;//是否已经弹出过挽留弹窗
    private OrientationUtils orientationUtils;
    private boolean isPlay;
    private boolean isPause;
    private ToolBar toolBar;
    private int systemUiVisibility;//切换到全屏前，系统状态栏的状态

    /**非视频类的商品，可以调用这个方法
     * @param activity 上下文
     * @param courseCode 课程代码
     * @param goodsType 商品类型，{@link StartFinal#VIP},{@link StartFinal#YAMI},{@link StartFinal#HIGH_FREQUENCY}
     */
    public static void start(Activity activity, String courseCode, String goodsType){
        start(activity, courseCode, goodsType, 0, 0, StartFinal.NO);
    }

    public static void start(Activity activity, String courseCode, String goodsType, int sourceType, int productId, int newcomers){
        start(activity, courseCode, goodsType, sourceType, productId, newcomers, null);
    }
    /**
     * @param activity 当前显示的页面，用于设置返回值
     * @param courseCode 课程代码
     * @param goodsType 拼团的类型 {@link StartFinal#VIP}, {@link StartFinal#YAMI}, {@link StartFinal#VIDEO1}, {@link StartFinal#VIDEO2}
     */
    public static void start(Activity activity, String courseCode, String goodsType, int sourceType, int productId, int newcomers, ActivityResultLauncher<Intent> launcher) {
        Intent intent = new Intent(activity, GroupGoodsActivity.class);
        intent.putExtra(StartFinal.ACTIVITY_NAME, activity.getClass().getName());
        intent.putExtra(StartFinal.COURSE_CODE, courseCode);
        intent.putExtra(StartFinal.GOODS_TYPE, goodsType);
        intent.putExtra(StartFinal.SOURCE_TYPE, sourceType);
        intent.putExtra(StartFinal.PRODUCT_ID, productId);
        intent.putExtra(StartFinal.NEWCOMERS, newcomers);

        if(launcher == null){
            activity.startActivityForResult(intent, StartFinal.REQUEST_CODE);
        }else {
            launcher.launch(intent);
        }

    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerGroupGoodsComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.group_goods_activity);
        binding.setOnClickListener(this);

        binding.coverLayout.getLayoutParams().height = DisplayHelper.getWindowWidth(this) * 9/16;
        binding.myVideoView.getBackButton().setImageDrawable(null);
        ViewGroup.LayoutParams startButtonLayout = binding.myVideoView.getStartButton().getLayoutParams();
        startButtonLayout.width = DisplayHelper.dp2px(this, 38);
        startButtonLayout.height = DisplayHelper.dp2px(this, 38);

        //埋点获取该页面的上一个页面
        new UmengEventUtils(this)
                .addParams("last_page_name", mPresenter.getActivityName())
                .pushEvent(UmengEventUtils.ENTER_PAGE_FOR_LAST_PAGE);

        binding.appBarLayout.addOnOffsetChangedListener((appBarLayout, i) -> binding.swipeLayout.setEnabled(i >= 0));

        //设置下拉刷新监听
        binding.swipeLayout.setOnRefreshListener(refreshLayout -> {
            if(fragmentList.get(binding.viewPager.getCurrentItem()) instanceof GroupGoodInfoFragment){
                mPresenter.getDetailInfoPager();

            }else if(fragmentList.get(binding.viewPager.getCurrentItem()) instanceof VideoListFragment){
                ((VideoListFragment)fragmentList.get(binding.viewPager.getCurrentItem())).refresh();

            }else if(fragmentList.get(binding.viewPager.getCurrentItem()) instanceof SkillTeacherFragment){
                ((SkillTeacherFragment)fragmentList.get(binding.viewPager.getCurrentItem())).refresh();

            }else if(fragmentList.get(binding.viewPager.getCurrentItem()) instanceof BookFragment){
                ((BookFragment)fragmentList.get(binding.viewPager.getCurrentItem())).refresh();

            }else if(fragmentList.get(binding.viewPager.getCurrentItem()) instanceof PlanFragment){
                ((PlanFragment)fragmentList.get(binding.viewPager.getCurrentItem())).refresh();

            }
        });

        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.swipeLayout;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        mPresenter.init(getIntent());

        toolBar = new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.LEFT)
                .setOnClickListener(v -> showGroupAgainPayDialog())
                .setTarget(ToolBar.Builder.TITLE)
                .setText(mPresenter.getTitle())
                .setTarget(ToolBar.Builder.RIGHT)
                .setDrawablesRes(new int[]{0, 0, R.drawable.head_share, 0})
                .setOnClickListener(v -> {
                    if(AppUtil.isEmpty(fragmentList)) return;
                    ((GroupGoodInfoFragment) fragmentList.get(0)).showShareDialog(StartFinal.DETAIL);
                })
                .build();

        fragmentList = new ArrayList<>();
    }

    @Override
    public void onRequest() {
        mPresenter.getDetailInfoPager();
    }

    @Override
    public void newUserCountdownTime(String time) {
        try {
            String[] timeSplit = time.split(":");
            if(timeSplit.length == 3){
                binding.hoursView.setText(timeSplit[0]);
                binding.minuteView.setText(timeSplit[1]);
                binding.secondView.setText(timeSplit[2]);
            }else {
                binding.countdownLayout.setVisibility(View.GONE);
            }
        }catch (Exception e){
            binding.countdownLayout.setVisibility(View.GONE);
        }
    }

    @Override
    public void getTabOneDataSuccess(GroupGoodInfoBean infoBean){
        binding.swipeLayout.finishRefresh();

        //初始化封面
        initCoverUI(infoBean);
        //新人特惠倒计时
        boolean isNewUser = infoBean.getIs_newcomers() == StartFinal.YES;
        binding.countdownLayout.setVisibility(isNewUser ? View.VISIBLE : View.GONE);
        if(isNewUser) mPresenter.startNewUserCountdownTime();
        //显示或隐藏推荐的押密商品
        initRecommendUI(infoBean);
        //设置购买按钮的数据
        setBuyLayoutData(infoBean, !AppUtil.isEmpty(infoBean.getGroup_id()));
        //当fragment的数量超过两个时，可以认为商品类型是视频，若已购买，需要把目录隐去掉
        removeUnNeedFragmentUI();
        //当已购买时，设置tabLayout隐藏，底部的购买按钮隐藏，评论输入框显示
        showFinishBuyUI(infoBean);
        //添加详情页的滚动监听，让拖动按钮在滚动时，藏到屏幕边缘
        addScrollListener();
        //浮动的客服按钮的显示和隐藏
        binding.customerFloatView.setVisibility(infoBean.getIs_pay() == StartFinal.YES ? View.VISIBLE : View.GONE);

        //首次刷新时，才初始化ViewPage
        if(AppUtil.isEmpty(fragmentList)) {
            createDetailInfoPager();
            createDirectoryPager();
            createTeacherPager();
            createBookPager();
            createLivePlanPager();
            //适配器绑定到TabLayout控件
            adapterBindingToTabLayout();
        }else {
            ((GroupGoodInfoFragment)fragmentList.get(0)).setInfoBean(infoBean);
        }

        if(TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                && infoBean.getReplenish_goods() != null
                && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())){
            String groupId = "";
            int rid = 0;
            if(!infoBean.getConduct_group().isEmpty()){
                groupId = infoBean.getConduct_group().get(0).getGroup_id();
                if(!AppUtil.isEmpty(infoBean.getConduct_group().get(0).getUsers_info())) {
                    rid = infoBean.getConduct_group().get(0).getUsers_info().get(0).getUsers_id();
                }
            }
            showGoodsUpgradeDialog(groupId, rid);
        }
    }

    //创建详情页
    public void createDetailInfoPager(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        GroupGoodInfoFragment goodInfoFragment = GroupGoodInfoFragment.getInstance(infoBean, mPresenter.getGoodsType());
        fragmentList.add(goodInfoFragment);
    }

    //创建目录页
    public void createDirectoryPager(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        boolean isVideo = TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO0)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO1)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO2);
        if(infoBean.getIs_pay() == StartFinal.YES || !isVideo) {//当已购买 或者 不是视频时，则不添加目录
            return;
        }

        VideoListFragment videoListFragment = VideoListFragment.newInstance(infoBean.getCode(), infoBean.getProduct_id(), infoBean.getSource_type());
        //设置回调对象
        videoListFragment.setNotifyListener(listBean -> binding.swipeLayout.finishRefresh());
        videoListFragment.setPlayListener((new_view_id, url) -> {
            String code = mPresenter.getInfoBean().getCode();
            int source_type = mPresenter.getInfoBean().getSource_type();
            int skill_id = 0;
            int product_id = mPresenter.getInfoBean().getProduct_id();
            HtmlVideoActivity.start(getActivity(), code, mPresenter.getGoodsType(), source_type, skill_id, product_id, new_view_id);
        });
        fragmentList.add(videoListFragment);

        //目录添加角标
        showPreviewVideoBadge();
    }

    //创建教师页
    public void createTeacherPager(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        boolean isVideo = TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO0)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO1)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO2);
        if(!isVideo) {//当不是视频时，则不添加老师
            return;
        }

        //老师简介列表
        SkillTeacherFragment teacherFragment = SkillTeacherFragment.newInstance(infoBean.getProduct_id(), 1);
        //设置回调对象
        teacherFragment.setNotifyListener(() -> binding.swipeLayout.finishRefresh());
        fragmentList.add(teacherFragment);
    }

    //创建视频配套的书籍信息页
    public void createBookPager(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        boolean isVideo = TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO0)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO1)
                || TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO2);
        if(!isVideo) {//当不是视频时，则不添加书籍
            return;
        }

        //老师简介列表
        BookFragment bookFragment = BookFragment.newInstance(infoBean.getProduct_id(), 1);
        //设置回调对象
        bookFragment.setNotifyListener(() -> binding.swipeLayout.finishRefresh());
        fragmentList.add(bookFragment);
    }

    //创建直播密训班的直播计划
    public void createLivePlanPager(){
        boolean isLivePlan = TextUtils.equals(mPresenter.getGoodsType(), StartFinal.VIDEO3) && mPresenter.getInfoBean().getSource_type() == 0;
        if(!isLivePlan) {//当不是视频时，则不添加书籍
            return;
        }

        //老师简介列表
        PlanFragment livePlanFragment = PlanFragment.newInstance(mPresenter.getInfoBean().getProduct_id(), mPresenter.getInfoBean().getSource_type());
        //设置回调对象
        livePlanFragment.setNotifyListener(() -> binding.swipeLayout.finishRefresh());
        fragmentList.add(livePlanFragment);
    }

    //设置目录页一栏显示试看角标
    public void showPreviewVideoBadge(){
        //精讲视频且未购买，则显示试看图标
        if(!StartFinal.VIDEO1.equals(mPresenter.getGoodsType())) {
            binding.previewVideoView.setVisibility(View.GONE);
            return;
        }
        binding.previewVideoView.setVisibility(View.VISIBLE);
        binding.tabLayout.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> {
            ViewGroup viewGroup1 = (ViewGroup) binding.tabLayout.getChildAt(0);
            ViewGroup viewGroup2 = (ViewGroup) viewGroup1.getChildAt(1);
            View tabText = viewGroup2.getChildAt(1);
            int[] location = new int[2];
            tabText.getLocationInWindow(location);
            binding.previewVideoView.setX(location[0] + tabText.getMeasuredWidth() + 10);
        });
    }

    //绑定适配器到TabLayout上
    public void adapterBindingToTabLayout(){
        binding.tabLayout.setVisibility(fragmentList.size() == 1 ? View.GONE : View.VISIBLE);

        binding.viewPager.setAdapter(new ViewPager2Adapter<>(getSupportFragmentManager(), getLifecycle(), fragmentList));
        //设置fragment的缓存个数
        binding.viewPager.setOffscreenPageLimit(Math.max(1, fragmentList.size() - 1));
        //初始化tabLayout
        AppUtil.clearTabClickColor(this, binding.tabLayout);
        TabLayoutMediator tabLayoutMediator = new TabLayoutMediator(binding.tabLayout, binding.viewPager, false, true, (tab, position) -> {
            if(fragmentList.get(position) instanceof GroupGoodInfoFragment){
                tab.setText("详情");
            }else if(fragmentList.get(position) instanceof VideoListFragment){
                tab.setText("目录");
            }else if(fragmentList.get(position) instanceof SkillTeacherFragment){
                tab.setText("老师");
            }else if(fragmentList.get(position) instanceof BookFragment){
                tab.setText("教材");
            }else if(fragmentList.get(position) instanceof PlanFragment){
                tab.setText("直播计划");
            }
        });
        //这句话很重要，viewPager与tabLayout绑定
        tabLayoutMediator.attach();
    }

    //初始化封面
    public void initCoverUI(GroupGoodInfoBean infoBean){
        if(!TextUtils.isEmpty(infoBean.getMaster_graph().getGraph_type()) && !TextUtils.isEmpty(infoBean.getMaster_graph().getUrl())) {
            if (infoBean.getMaster_graph().getGraph_type().equals("video")) {
                binding.coverView.setVisibility(View.GONE);
                loadVideoUrl(infoBean.getMaster_graph().getUrl(), infoBean.getMaster_graph().getImage_url());
            } else {
                binding.myVideoView.setVisibility(View.GONE);
                ImageLoader.loadImageNoPlaceholder(binding.coverView, infoBean.getMaster_graph().getUrl());
            }
        }
    }

    //已购买时，移除视频目录列表
    public void removeUnNeedFragmentUI(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        //是精讲，且未购买，显示试看图标
        if(StartFinal.VIDEO1.equals(mPresenter.getGoodsType()) && infoBean.getIs_pay() == StartFinal.NO){
            binding.previewVideoView.setVisibility(View.VISIBLE);

        }else {
            binding.previewVideoView.setVisibility(View.GONE);
        }

        //是视频，且已购买，移除目录
        if((StartFinal.VIDEO1.equals(mPresenter.getGoodsType()) || StartFinal.VIDEO2.equals(mPresenter.getGoodsType()))
                && infoBean.getIs_pay() == StartFinal.YES){
            for(BaseFragment fragment : fragmentList) {
                if(fragment instanceof VideoListFragment) {
                    fragmentList.remove(fragment);//移除的是目录
                    binding.viewPager.getAdapter().notifyDataSetChanged();
                    return;
                }
            }
        }
    }

    //初始化推荐商品
    public void initRecommendUI(GroupGoodInfoBean infoBean){
        if(StartFinal.VIP.equals(mPresenter.getGoodsType())) {
            binding.recommendLayout.setVisibility(PayUtils.YAMI.equals(infoBean.getRemind_buy()) ? View.VISIBLE : View.GONE);
            binding.recommendContentView.setText("购买考前押密赠送VIP题库，是否前往？");

        }else if(StartFinal.VIDEO2.equals(mPresenter.getGoodsType())) {
            binding.recommendLayout.setVisibility(PayUtils.YAMI.equals(infoBean.getRemind_buy()) ? View.VISIBLE : View.GONE);
            binding.recommendContentView.setText("购买考前押密赠送串讲视频，是否前往？");

        }if(StartFinal.HIGH_FREQUENCY.equals(mPresenter.getGoodsType())) {
            binding.recommendLayout.setVisibility(PayUtils.YAMI.equals(infoBean.getRemind_buy()) ? View.VISIBLE : View.GONE);
            binding.recommendContentView.setText("购买考前押密赠送高频考点，是否前往？");

        }else {
            //隐藏推荐商品的布局
            binding.recommendLayout.setVisibility(View.GONE);
        }
    }

    //设置购买按钮的数据
    public void setBuyLayoutData(GroupGoodInfoBean infoBean, boolean isLaunchGroup){
        String singlePrice = getSingPrice(infoBean, false);
        String groupPrice = getGroupPrice(infoBean, false);

        binding.singleBuyView.setText(String.format("单独购买\n¥%s", singlePrice));
        binding.groupBuyView.setText(isLaunchGroup ? "邀请好友" : String.format("发起拼团\n¥%s", groupPrice));
    }

    //显示已购买的UI
    public void showFinishBuyUI(GroupGoodInfoBean infoBean){
        if(infoBean.getIs_pay() == StartFinal.YES){
            binding.bottomLayout.setVisibility(View.GONE);
            binding.commentLayout.setVisibility(infoBean.getIs_comment() == StartFinal.YES ? View.GONE : View.VISIBLE);

        }else {
            binding.bottomLayout.setVisibility(View.VISIBLE);
            binding.commentLayout.setVisibility(View.GONE);
        }
    }

    //添加详情页的滚动监听，让拖动按钮在滚动时，藏到屏幕边缘
    public void addScrollListener(){
        //监听appBarLayout面板滚动结束时，拖动按钮从屏幕外滑动进入
        binding.appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            int lastOffset;//记录当前滚动的距离
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int i) {
                //传递进来的距离与记录不相等，认为有滚动，执行动画，避免视频播放时一直触发该方法
                if(i != lastOffset){
                    moveCustomerAnimation(binding.customerFloatView);
                    lastOffset = i;
                }
            }
        });

        //监听viewPager里的面板滚动结束时，拖动按钮从屏幕外滑动进入。
        //延迟2秒，是因为详情页只是创建了，但是还没有完成添加布局，直接获取控件会报空指针，2秒之后可以完成添加布局，就能正常添加监听
        new Handler().postDelayed(() -> {
            try {
                NestedScrollView detailScrollView = fragmentList.get(0).getView().findViewById(R.id.nestedScrollView);
                detailScrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (nestedScrollView, i, i1, i2, i3) -> {
                    moveCustomerAnimation(binding.customerFloatView);
                });
            }catch (Exception e){
                LogUtil.e("dddd", "添加监听商品详情滚动滚动失败，可能在定时器销毁后才执行延时任务");
            }
        }, 2000);
    }

    //加入拼团
    public void joinGroup(String groupId, int rid) {//加入拼团才回调该方法
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        if(infoBean != null) {
            new UmengEventUtils(GroupGoodsActivity.this)
                    .addParams("price", infoBean.getPrice())
                    .addParams("group_price", UserCache.isMemberShip() ? infoBean.getMember_group_price() : infoBean.getGroup_price())
                    .addParams("code_and_name", String.format("【%s】%s", infoBean.getCode(), infoBean.getName()))
                    .addParams("goods_type", mPresenter.getGoodTypeName())
                    .addParams("buy_type", "加入拼团")
                    .pushEvent(UmengEventUtils.CLICK_BUY);
        }

        //用户正在拼团，则只能去分享拼团
        if(!AppUtil.isEmpty(infoBean.getGroup_id())){
            ((GroupGoodInfoFragment)fragmentList.get(0)).showShareDialog(StartFinal.GROUP);
            return;
        }

        if(TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                && infoBean.getReplenish_goods() != null
                && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())){
            showGoodsUpgradeDialog(groupId, rid);
        }else {
            toJoinGroup(groupId, rid);
        }
    }

    public void toJoinGroup(String groupId, int rid){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        if(StartFinal.YAMI.equals(mPresenter.getGoodsType())) {//压密需要先弹出压密协议
            showSecretAgreementDialog(MainAppUtils.getSecretAgreementTitle(), MainAppUtils.getSecretAgreement(), groupId, rid, true);

        }else if(StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0) {//直播密训班需要先弹出压密协议
            showSecretAgreementDialog("考前密训服务协议", Constant.MX_AGREEMENT_SECRET, groupId, rid, true);

        }else {
            mPresenter.getDiscountCard(infoBean.getGroup_price(), getGroupPrice(infoBean, true), groupId, rid, true);
        }
    }

    //加载封面
    public void loadVideoUrl(String url, String cover){
        //封面图
        ImageView coverView = new ImageView(this);
        coverView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        ImageLoader.loadImage(coverView, cover);

        //外部辅助的旋转，帮助全屏
        orientationUtils = new OrientationUtils(this, binding.myVideoView);
        binding.myVideoView.getFullscreenButton().setOnClickListener(v -> orientationUtils.resolveByClick());
        //初始化不打开外部的旋转
        orientationUtils.setEnable(false);

        new GSYVideoOptionBuilder()
                .setThumbImageView(coverView)//添加封面图
                .setIsTouchWiget(true)//滑动界面改变进度
                .setRotateViewAuto(false)//自动旋转
                .setLockLand(false)//一全屏就锁屏横屏
                .setShowFullAnimation(true)//使用全屏动画效果
                .setNeedLockFull(true)//全屏锁定屏幕功能
                .setSeekRatio(1)//调整触摸滑动快进的比例
                .setHideKey(false)//隐藏虚拟按键
                .setCacheWithPlay(true)//缓存
                .setUrl(url)//视频链接
                .setVideoAllCallBack(new GSYSampleCallBack() {
                    @Override
                    public void onPrepared(String url, Object... objects) {
                        super.onPrepared(url, objects);
                        //开始播放了才能旋转和全屏
                        orientationUtils.setEnable(true);
                        isPlay = true;
                    }

                    @Override
                    public void onQuitFullscreen(String url, Object... objects) {
                        super.onQuitFullscreen(url, objects);
                        if (orientationUtils != null) {
                            orientationUtils.backToProtVideo();
                        }
                    }
                }).setLockClickListener((view, lock) -> {
                    if (orientationUtils != null) {
                        //配合onConfigurationChanged()方法使用
                        orientationUtils.setEnable(!lock);
                    }
                }).build(binding.myVideoView);
    }

    /**页面停止滚动时，拖动按钮从屏幕外滑动进来
     * @param view
     */
    private void moveCustomerAnimation(FloatingImageView view) {
        float hideWidth = view.getMeasuredWidth()/3f*2;
        float start = view.getOrientation() > 0 ? hideWidth : (-DisplayHelper.getWindowWidth(this) + view.getMeasuredWidth()-hideWidth);
        float end = view.getOrientation() > 0 ? 0 : -view.getLeft();

        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationX", start, end);
        animator.setDuration(400);
        animator.start();
    }

    @Override
    public void onClick(View view) {
        if(view == binding.customerView || view == binding.customerFloatView){//未购买状态的客服按钮 和 已购买状态的客服按钮
            WxMinApplication.StartWechat(this);

        }else if(view == binding.recommendLayout){//推荐购买押密
            GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
            GroupGoodsActivity.start(GroupGoodsActivity.this, infoBean.getCode(), StartFinal.YAMI, infoBean.getSource_type(), infoBean.getProduct_id(), infoBean.getIs_newcomers(), null);
            finish();

        }else if(view == binding.closeRecommendView){//关闭推荐购买押密
            binding.recommendLayout.setVisibility(View.GONE);

        }else if(view == binding.singleBuyView){//单独购买
            GroupGoodInfoBean infoBean = mPresenter.getInfoBean();

            if(infoBean != null) {
                new UmengEventUtils(this)
                        .addParams("price", infoBean.getPrice())
                        .addParams("group_price", UserCache.isMemberShip() ? infoBean.getMember_group_price() : infoBean.getGroup_price())
                        .addParams("code_and_name", String.format("【%s】%s", infoBean.getCode(), infoBean.getName()))
                        .addParams("goods_type", mPresenter.getGoodTypeName())
                        .addParams("buy_type", "单独购买")
                        .pushEvent(UmengEventUtils.CLICK_BUY);
            }

            if(!MainAppUtils.checkLogin(this)) return;
            if(TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                    && infoBean.getReplenish_goods() != null
                    && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())){
                showGoodsUpgradeDialog("", 0);
            }else {
                single();
            }


        }else if(view == binding.groupBuyView){//发起拼团GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
            GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
            new UmengEventUtils(this)
                    .addParams("price", infoBean.getPrice())
                    .addParams("group_price", UserCache.isMemberShip() ? infoBean.getMember_group_price() : infoBean.getGroup_price())
                    .addParams("code_and_name", String.format("【%s】%s", infoBean.getCode(), infoBean.getName()))
                    .addParams("goods_type", mPresenter.getGoodTypeName())
                    .addParams("buy_type", "发起拼团")
                    .pushEvent(UmengEventUtils.CLICK_BUY);

            if(!MainAppUtils.checkLogin(this)) return;
            if(TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                    && infoBean.getReplenish_goods() != null
                    && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())){
                showGoodsUpgradeDialog("", 0);
            }else {
                group();
            }

        }else if(view == binding.commentLayout){//发表评论的父布局
            GroupGoodsCommentDialog commentDialog = new GroupGoodsCommentDialog(this, content -> mPresenter.addAssess(content));
            commentDialog.setComment(binding.commentView.getText().toString());
            commentDialog.setOnDismissListener(dialog -> {
                binding.commentView.setText(commentDialog.getComment());
                if(!TextUtils.isEmpty(binding.commentView.getText())){//有文字，则不显示画笔图标
                    binding.commentView.setCompoundDrawables(null, null, null, null);
                }else {//没有文字，显示画笔图标
                    TextDrawableLoader.loadLeft(GroupGoodsActivity.this, binding.commentView, R.drawable.practice_icon_mode1);
                }
            });
            commentDialog.show();
        }
    }

    public void showGoodsUpgradeDialog(String groupId, int rid){
        String singlePrice = getSingPrice(mPresenter.getInfoBean(), true);
        String groupPrice = getGroupPrice(mPresenter.getInfoBean(), true);
        new GoodsUpgradeDialog(this)
                .setGroupId(groupId)
                .setUpgradeBean(mPresenter.getInfoBean().getReplenish_goods(), singlePrice, groupPrice)
                .setOnBuyListener((isGroup1, groupId1) -> {
                    if(!isGroup1){
                        single();
                    }else {
                        if(rid > 0 && TextUtils.isEmpty(groupId1)){
                            showMessage("邀请的团不存在");

                        }else if(rid > 0 && !TextUtils.isEmpty(groupId1)){
                            toJoinGroup(groupId1, rid);

                        }else {
                            group();
                        }
                    }
                }).show();

    }

    private void single(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        if(!AppUtil.isEmpty(infoBean.getGroup_id())){
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setTitle("温馨提示")
                    .setContent("您当前已成功发起拼团，单独购买成功后，已发起的拼团将会关闭，是否继续单独购买？")
                    .setNegativeText("取消")
                    .setPositiveText("购买")
                    .setPositiveClickListener(v -> {
                        if(StartFinal.YAMI.equals(mPresenter.getGoodsType())) {//压密需要先弹出压密协议
                            showSecretAgreementDialog(MainAppUtils.getSecretAgreementTitle(), MainAppUtils.getSecretAgreement(), "", 0,false);

                        }else if(StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0) {//直播密训班需要先弹出压密协议
                            showSecretAgreementDialog("考前密训服务协议", Constant.MX_AGREEMENT_SECRET, "", 0,false);

                        }else {
                            mPresenter.getDiscountCard(infoBean.getPrice(), getSingPrice(infoBean, true), "", 0, false);
                        }
                    })
                    .builder().show();
        }else {
            if(StartFinal.YAMI.equals(mPresenter.getGoodsType())) {//压密需要先弹出压密协议
                showSecretAgreementDialog(MainAppUtils.getSecretAgreementTitle(), MainAppUtils.getSecretAgreement(), "", 0,false);

            }else if(StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0) {//直播密训班需要先弹出压密协议
                showSecretAgreementDialog("考前密训服务协议", Constant.MX_AGREEMENT_SECRET, "", 0,false);

            }else {
                mPresenter.getDiscountCard(infoBean.getPrice(), getSingPrice(infoBean, true), "", 0, false);
            }
        }
    }

    private void group(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        if(!AppUtil.isEmpty(infoBean.getGroup_id())){//正在拼团，则调起分享弹窗
            ((GroupGoodInfoFragment)fragmentList.get(0)).showShareDialog(StartFinal.GROUP);

        }else if(StartFinal.YAMI.equals(mPresenter.getGoodsType())
                || (StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0)) {
            //压密或直播密训班需要先弹出压密协议
            if(StartFinal.YAMI.equals(mPresenter.getGoodsType())) {//压密需要先弹出压密协议
                showSecretAgreementDialog(MainAppUtils.getSecretAgreementTitle(), MainAppUtils.getSecretAgreement(), "", 0,true);

            }else if(StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0) {//直播密训班需要先弹出压密协议
                showSecretAgreementDialog("考前密训服务协议", Constant.MX_AGREEMENT_SECRET, "", 0,true);
            }

        }else {
            mPresenter.getDiscountCard(infoBean.getGroup_price(), getGroupPrice(infoBean, true), "", 0, true);
        }
    }

    public void showSecretAgreementDialog(String title, String url, String groupId, int ird, boolean isGroup){
        new H5Dialog.Builder(this)
                .setTitle(title)
                .setUrl(url, false)
                .setButtonText("我已阅读并同意协议")
                .setEnableText("请阅读协议")
                .setOnAgreeListener(() ->{
                    //判断是否已实名认证
                    mPresenter.getRealInfo(groupId, ird, isGroup);
                })
                .build()
                .show();
    }

    @Override
    public void showPayDialog(List<DiscountBean> allDiscount, String price, String payPrice, String groupId, int rid, boolean isGroup) {
        GroupBean sender = mPresenter.getJoinGroup();
        //当用户发起拼团，且已有人发起拼团，将拼团列表中的第一个团设置到支付弹窗里
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();

        if(sender == null && isGroup && rid == 0 && !infoBean.getConduct_group().isEmpty()){
            sender = infoBean.getConduct_group().get(0);
        }

        DiscountPayDialog payDialog = new DiscountPayDialog.Builder(this, mPresenter.getGoodsType())
                .setGoodsName(String.format("【%s】%s", mPresenter.getGoodTypeName(), infoBean.getName()))
                .setPrice(Float.parseFloat(price))
                .setPayPrice(Float.parseFloat(payPrice))
                .setDiscountList(allDiscount)
                .setDefaultPayType(mPresenter.getPayType())
                .setShowGuobi(StartFinal.VIP.equals(mPresenter.getGoodsType()) || StartFinal.HIGH_FREQUENCY.equals(mPresenter.getGoodsType()))
                .setOnPayListener((joinGroup, discount, payType) -> {
                    if(joinGroup != null){
                        mPresenter.payOrder(isGroup, joinGroup.getGroup_id(), joinGroup.getUsers_info().get(0).getUsers_id(), payType, discount);
                    }else {
                        mPresenter.payOrder(isGroup, groupId, rid, payType, discount);
                    }
                })
                .setSenderGroup(sender)
                .setJoinGroup(mPresenter.getJoinGroup() != null)
                .setOnCloseListener(() -> mPresenter.setJoinGroup(null))
                .build();

        if(StartFinal.HIGH_FREQUENCY.equals(mPresenter.getGoodsType()) && infoBean.getIs_pay_vip() == StartFinal.YES){
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setTitle("温馨提示")
                    .setContent("高频考点来源于VIP题库和历年真题，您已购买VIP题库，是否继续购买高频考点？")
                    .setNegativeText("取消")
                    .setPositiveText("继续购买")
                    .setPositiveClickListener(v -> payDialog.show()).builder()
                    .show();
        }else {
            payDialog.show();
        }
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void paySuccess() {
        binding.swipeLayout.autoRefresh();
        startPaySuccess(mPresenter.getGroupStatus(), mPresenter.getOrderId());
    }

    public void startPaySuccess(int groupStatus, int orderId){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();

        UmengPushHelper.addBuyGoodTag(this, mPresenter.getGoodsType(), infoBean.getCode()); //注册友盟推送标签

        Bundle bundle = new Bundle();
        bundle.putString(PaySuccessActivity.COURSE_NAME, infoBean.getName());
        bundle.putInt(PaySuccessActivity.PRODUCT_ID, infoBean.getProduct_id());
        bundle.putInt(PaySuccessActivity.SOURCE_TYPE, infoBean.getSource_type());
        bundle.putInt(PaySuccessActivity.LIST_TYPE, PayUtils.CONSUMPTION_LIST);
        bundle.putString(PaySuccessActivity.GOODS_TYPE, mPresenter.getGoodsType());
        bundle.putBoolean(PaySuccessActivity.IS_GROUP, groupStatus == 1);
        bundle.putBoolean(PaySuccessActivity.IS_JOIN_GROUP, groupStatus == 2);
        bundle.putInt(PaySuccessActivity.ORDER_ID, orderId);
        bundle.putString(PaySuccessActivity.CODE, infoBean.getCode());

        bundle.putString(PaySuccessActivity.GROUP_ID, orderId+"");
        bundle.putString(PaySuccessActivity.SHARE_TITLE, infoBean.getGroup_share_text());
        bundle.putString(PaySuccessActivity.SHARE_CONTENT, infoBean.getGroup_share_desc());
        //发起拼团，给后台2秒的支付回调时间，2秒后跳到拼团详情
        LoadingDialog.showLoadingDialog(getActivity());
        new Handler().postDelayed(() -> {
            LoadingDialog.hideLoadingDialog();
            PaySuccessActivity.start(getActivity(), bundle);
        }, 2000);
    }

    @Override
    public void realInfoSuccess(RealInfoBean data, boolean isGroup, int rid, String groupId) {
        boolean isAuth = !TextUtils.isEmpty(data.getRealname())
                && !TextUtils.isEmpty(data.get_idcard())
                && !TextUtils.isEmpty(data.getEmail());
        if (isAuth) {
            GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
            String price = isGroup ? infoBean.getGroup_price() : infoBean.getPrice();
            String payPrice = isGroup ? getGroupPrice(infoBean, true) : getSingPrice(infoBean, true);
            mPresenter.getDiscountCard(price, payPrice, groupId, rid, isGroup);
        } else {
            RealInfoActivity.start(this, data);
        }
    }

    @Override
    public void commentSuccess() {
        binding.swipeLayout.autoRefresh();
    }

    @Override
    public Activity getActivity() {
        return this;
    }

    //拼团订单在返回上一页面的时候，弹出挽留弹窗
    private void showGroupAgainPayDialog(){
        GroupGoodInfoBean infoBean = mPresenter.getInfoBean();
        if(infoBean == null) {
            finish();
            return;
        }
        boolean isLogin = UserCache.getUserCache() != null;
        boolean isGrouping = !AppUtil.isEmpty(infoBean.getGroup_id());
        boolean isPay = infoBean.getIs_pay() == StartFinal.YES;

        if(!isLogin //未登录，则直接返回
                || isGrouping //处于拼团中，则直接返回
                || isShowAgainPayDialog //显示过一次挽留弹窗，则直接返回
                || isPay//支付成功，直接返回
        ) {
            finish();
            return;
        }

        GroupBean joinGroup = !AppUtil.isEmpty(infoBean.getConduct_group()) ? infoBean.getConduct_group().get(0) : null;

        //记录此次挽留弹窗已弹出
        isShowAgainPayDialog = true;
        GroupBean finalJoinGroup = joinGroup;
        new GroupAgainPayDialog.Builder(this)
                .showGuobi(false)
                .showPoint(false)
                .setTitle(mPresenter.getGoodTypeName())
                .setBuyText(!AppUtil.isEmpty(infoBean.getConduct_group()) ? "直接拼成" : "发起拼团")
                .setBeans(mPresenter.getInfoBean().getRedeem())
                .setOnBuyListener(payType -> {
                    //挽回弹窗选择购买后，保存一下支付方式
                    mPresenter.setPayType(payType);
                    //若是有正在拼的团，则保存一下
                    mPresenter.setJoinGroup(finalJoinGroup);

                    String groupId = finalJoinGroup != null ? finalJoinGroup.getGroup_id() : "";
                    if(PayUtils.YAMI.equals(mPresenter.getGoodsType())){
                        //如果是押密，要先展示押密协议
                        if(StartFinal.YAMI.equals(mPresenter.getGoodsType())) {//压密需要先弹出压密协议
                            showSecretAgreementDialog(MainAppUtils.getSecretAgreementTitle(), MainAppUtils.getSecretAgreement(), groupId, mPresenter.getRid(), true);

                        }else if(StartFinal.VIDEO3.equals(mPresenter.getGoodsType()) && infoBean.getSource_type() == 0) {//直播密训班需要先弹出压密协议
                            showSecretAgreementDialog("考前密训服务协议", Constant.MX_AGREEMENT_SECRET, groupId, mPresenter.getRid(), true);
                        }

                    }else {
                        mPresenter.getDiscountCard(infoBean.getGroup_price(), getGroupPrice(infoBean, true), groupId, mPresenter.getRid(), true);
                    }
                })
                .setOnCancelBuyListener(() -> finish())
                .builder()
                .show(getSupportFragmentManager());

    }

    public String getGroupPrice(GroupGoodInfoBean infoBean, boolean isBackReplenishGoods){
        String payPrice;
        if(infoBean.getIs_newcomers() == StartFinal.YES){
            payPrice = infoBean.getNewcomers_price();

        } else{
            payPrice = UserCache.isMemberShip() ? infoBean.getMember_group_price() : infoBean.getGroup_price();
        }

        //判断是否有可减免的条件
        if(isBackReplenishGoods && TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                && infoBean.getReplenish_goods() != null
                && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())) {
            if(infoBean.getReplenish_goods().getMin_group().getIs_apply() == 1) {
                //拼团购买的最低价
                payPrice = infoBean.getReplenish_goods().getMin_group().getMin_price();
            }else {
                //正常价格减掉减免价格
                payPrice = MathUtil.StringSubFloat(payPrice, infoBean.getReplenish_goods().getPrice_sum()) + "";
            }
        }
        return payPrice;
    }

    public String getSingPrice(GroupGoodInfoBean infoBean, boolean isBackReplenishGoods){
        String payPrice = UserCache.isMemberShip() ? infoBean.getMember_price() : infoBean.getPrice();
        //判断是否有可减免的条件
        if(isBackReplenishGoods && TextUtils.equals(mPresenter.getGoodsType(), StartFinal.YAMI)
                && infoBean.getReplenish_goods() != null
                && !AppUtil.isEmpty(infoBean.getReplenish_goods().getGoods_infos())) {
            if(infoBean.getReplenish_goods().getMin().getIs_apply() == 1){
                //单独购买的最低价
                payPrice = infoBean.getReplenish_goods().getMin().getMin_price();
            }else {
                //正常价格减掉减免价格
                payPrice = MathUtil.StringSubFloat(payPrice, infoBean.getReplenish_goods().getPrice_sum()) + "";
            }
        }
        return payPrice;
    }

    @Override
    public void onBackPressed() {
        if(mPresenter.getInfoBean() != null) {
            showGroupAgainPayDialog();
        }else {
            finish();
        }
    }

    @Override
    protected void onPause() {
        binding.myVideoView.getCurrentPlayer().onVideoPause();
        super.onPause();
        isPause = true;
    }

    @Override
    protected void onResume() {
        binding.myVideoView.getCurrentPlayer().onVideoResume(false);
        super.onResume();
        isPause = false;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if(orientationUtils == null) return;

        //如果旋转了就全屏
        if (isPlay && !isPause) {
            binding.myVideoView.onConfigurationChanged(this, newConfig, orientationUtils, true, true);
        }
        if(orientationUtils.getIsLand() == 0){
            //竖屏
            getWindow().getDecorView().setSystemUiVisibility(systemUiVisibility);
            toolBar.getLayoutView().setVisibility(View.VISIBLE);
        }else {
            //横屏
            systemUiVisibility = getWindow().getDecorView().getSystemUiVisibility();
            toolBar.getLayoutView().setVisibility(View.GONE);
        }
    }
    @Override
    protected void onDestroy() {
        //埋点统计商品是否支付成功
        new UmengEventUtils(this)
                .addParams("is_pay_success", binding.bottomLayout.getTag() != null ? "是" : "否")
                .pushEvent(UmengEventUtils.PAY_STATUS);

        if (isPlay) {
            binding.myVideoView.getCurrentPlayer().release();
        }
        if (orientationUtils != null)
            orientationUtils.releaseListener();
        super.onDestroy();

    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    private void loginSuccess(UserBean user) {
        binding.swipeLayout.autoRefresh();
    }
}