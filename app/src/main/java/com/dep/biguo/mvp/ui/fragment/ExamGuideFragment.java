package com.dep.biguo.mvp.ui.fragment;

import android.content.Intent;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.android.material.tabs.TabLayout;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.ExamGuideBean;
import com.dep.biguo.bean.jsz.JSZProvinceBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.common.Constant;
import com.dep.biguo.di.component.DaggerExamGuideComponent;
import com.dep.biguo.mvp.contract.ExamGuideContract;
import com.dep.biguo.mvp.presenter.ExamGuidePresenter;
import com.dep.biguo.mvp.ui.adapter.ExamTimeAdapter;
import com.dep.biguo.mvp.ui.adapter.ViewPagerAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;


public class ExamGuideFragment extends BaseFragment<ExamGuidePresenter> implements ExamGuideContract.View {
    @BindView(R.id.tab_layout)
    TabLayout tab_layout;
    @BindView(R.id.viewpager)
    ViewPager viewpager;
    private List<View> mViews;
    private ExamGuideBean data;
    private boolean isFirst = true;

    public static ExamGuideFragment newInstance() {
        ExamGuideFragment fragment = new ExamGuideFragment();
        return fragment;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerExamGuideComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_exam_guide, container, false);
        return view;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        tab_layout.setPadding(0, DisplayHelper.getStatusBarHeight(getContext()), 0, 0);
        if (UserCache.getAppType().equals(Constant.JZS)|| UserCache.getAppType().equals(Constant.YYDJ)) {
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) tab_layout.getLayoutParams();
            params.width = LinearLayout.LayoutParams.MATCH_PARENT;
            tab_layout.setLayoutParams(params);
            tab_layout.setSelectedTabIndicatorHeight(DisplayHelper.dp2px(getContext(), 5));
            tab_layout.setSelectedTabIndicatorColor(getThemeColor());
            tab_layout.setTabIndicatorFullWidth(false);
        }
        mViews = new ArrayList<>();
        LayoutInflater inflater = LayoutInflater.from(getContext());
        initViewPagerJJ(inflater);
        initViewPagerZN(inflater);
        initViewPagerGL(inflater);
        viewpager.setAdapter(new ViewPagerAdapter(mViews));
        viewpager.setOffscreenPageLimit(mViews.size());
        tab_layout.setupWithViewPager(viewpager);
        //tablayout绑定viewpager后会重置tab，所以在绑定之后重新设置tab
        resetTab(tab_layout.getTabAt(0), R.string.exam_jj, 18, getSelectTabColor());
        resetTab(tab_layout.getTabAt(1), R.string.exam_zn, 16, R.color.tblack3);
        resetTab(tab_layout.getTabAt(2), R.string.exam_gl, 16, R.color.tblack3);
        tab_layout.getTabAt(0).select();//设置第一个为选中
        tab_layout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                changeTab(tab, 18, getSelectTabColor());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                changeTab(tab, 16, R.color.tblack3);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

    }

    @Override
    public void setData(@Nullable Object data) {

    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void showMessage(@NonNull String message) {

    }

    private void resetTab(TabLayout.Tab tab, int strId, int textSize, @ColorRes int colorRes) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        TextView textView = new TextView(getContext());
        linearLayout.addView(textView);
        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) textView.getLayoutParams();
        params.width = DisplayHelper.dp2px(getContext(), 72);
        textView.setLayoutParams(params);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, DisplayHelper.dp2px(getContext(), textSize));
        textView.setTextColor(ContextCompat.getColor(getContext(), colorRes));
        textView.setText(strId);
        tab.setCustomView(textView);
    }

    private void changeTab(TabLayout.Tab tab, int textSize, @ColorRes int colorRes) {
        TextView textView = (TextView) tab.getCustomView();
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, DisplayHelper.dp2px(getContext(), textSize));
        textView.setTextColor(ContextCompat.getColor(getContext(), colorRes));
    }

    //考试简介
    private void initViewPagerJJ(LayoutInflater inflater) {
        View pView;
        switch (UserCache.getAppType()) {
            case Constant.YYDJ:
            case Constant.JZS:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_jj_type2, null);
                break;
            case Constant.RLZY:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_jj_type3, null);
                break;
            case Constant.KJ:
            default:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_jj_type1, null);
                break;
        }

        tvContent = pView.findViewById(R.id.tvContent);
        mViews.add(pView);
    }

    //报考指南
    private void initViewPagerZN(LayoutInflater inflater) {
        View pView;
        TabLayout tab_layout_sub;
        switch (UserCache.getAppType()) {
            case Constant.YYDJ:
            case Constant.JZS:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_zn_type2, null);
                tab_layout_sub = pView.findViewById(R.id.tab_layout_sub);
                break;
            case Constant.KJ:
            case Constant.RLZY:
            default:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_zn_type1, null);
                tab_layout_sub = pView.findViewById(R.id.tab_layout_sub);
                tab_layout_sub.setSelectedTabIndicatorColor(getThemeColor());
                tab_layout_sub.setTabTextColors(ContextCompat.getColor(getContext(), R.color.tblack3), getThemeColor());
                break;
        }
        ViewPager viewSubPager = pView.findViewById(R.id.viewpager);
        List<View> views = initSubViews(inflater);
        viewSubPager.setAdapter(new ViewPagerAdapter(views));
        viewSubPager.setOffscreenPageLimit(views.size());
        tab_layout_sub.setupWithViewPager(viewSubPager);
        tab_layout_sub.getTabAt(0).setText(R.string.exam_guide_sub_title1);
        tab_layout_sub.getTabAt(1).setText(R.string.exam_guide_sub_title2);
        tab_layout_sub.getTabAt(2).setText(R.string.exam_guide_sub_title3);
        tab_layout_sub.getTabAt(3).setText(R.string.exam_guide_sub_title4);
        mViews.add(pView);
    }

    //考试攻略
    private void initViewPagerGL(LayoutInflater inflater) {
        View pView;
        switch (UserCache.getAppType()) {
            case Constant.YYDJ:
            case Constant.JZS:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_gl_typy2, null);
                break;
            case Constant.RLZY:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_gl_typy3, null);
                break;
            case Constant.KJ:
            default:
                pView = inflater.inflate(R.layout.view_pager_exam_guide_gl_typy1, null);
                break;
        }
        test_skills = pView.findViewById(R.id.tvContent);
        test_attention = pView.findViewById(R.id.tvContent2);
        mViews.add(pView);
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        if (isVisibleToUser && isFirst) {
            mPresenter.getSkillGuide();
            isFirst = false;
        }
    }


    @Override
    public void setDataView(ExamGuideBean bean) {
        data = bean;
        tvContent.setText(data.getSynopsis());
        registration_date.setText("报名时间为：" + data.getSign_up().getRegistration_date());
        registration_name.setText(data.getSign_up().getRegistration_name());
        registration_name.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(bean.getSign_up().getRegistration_url()));
            startActivity(intent);
        });
        registration_condition.setText(bean.getSign_up().getRegistration_condition());

        if (TextUtils.isEmpty(bean.getExam_schedule().getNotes())) {
            tvNotes.setVisibility(View.GONE);
        } else {
            tvNotes.setText(bean.getExam_schedule().getNotes());
        }
        exam_type.setText(bean.getExam_schedule().getExam_type());
        exam_kind.setText(bean.getExam_schedule().getExam_kind());

        examTimeAdapter.setNewData(bean.getExam_schedule().getExam_date());

        results_date.setText(bean.getScore_query().getResults_date());
        results_name.setText(bean.getScore_query().getResults_name());
        results_name.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(bean.getScore_query().getResults_url()));
            startActivity(intent);
        });
        pass_mark.setText(bean.getScore_query().getPass_mark());
        results_tips.setText(bean.getScore_query().getResults_tips());

        printing_date.setText(bean.getAdmission_ticket().getPrinting_date());
        printing_name.setText(bean.getAdmission_ticket().getPrinting_name());
        printing_name.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(bean.getAdmission_ticket().getPrinting_url()));
            startActivity(intent);
        });
        printing_flow.setText(bean.getAdmission_ticket().getPrinting_flow());

        test_skills.setText(bean.getSkill_certificate().getTest_skills());
        test_attention.setText(bean.getSkill_certificate().getTest_attention());
    }

    private TextView tvContent;
    private TextView registration_date;
    private TextView registration_name;
    private TextView registration_condition;
    private RecyclerView rvExamTime;
    private CommonAdapter examTimeAdapter;
    private TextView tvNotes;
    private TextView exam_type;
    private TextView exam_kind;
    private TextView results_date;
    private TextView results_name;
    private TextView pass_mark;
    private TextView results_tips;
    private TextView printing_date;
    private TextView printing_name;
    private TextView printing_flow;
    private TextView test_skills;
    private TextView test_attention;

    private List<View> initSubViews(LayoutInflater inflater) {
        List<View> views = new ArrayList<>();
        View view1;
        View view2;
        View view3;
        View view4;
        switch (UserCache.getAppType()) {
            case Constant.YYDJ:
            case Constant.JZS:
                view1 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub1_type2, null);
                view2 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub2_type2, null);
                view3 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub3_type2, null);
                view4 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub4_type2, null);
                ((TextView) view1.findViewById(R.id.tvNumber2Content)).setTextColor(getThemeColor());
                ((TextView) view3.findViewById(R.id.tvNumber2Content)).setTextColor(getThemeColor());
                ((TextView) view4.findViewById(R.id.tvNumber2Content)).setTextColor(getThemeColor());
                break;
            case Constant.RLZY:
                view1 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub1_type3, null);
                view2 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub2_type3, null);
                view3 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub3_type3, null);
                view4 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub4_type3, null);
                break;
            case Constant.KJ:
            default:
                view1 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub1_type1, null);
                view2 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub2_type1, null);
                view3 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub3_type1, null);
                view4 = inflater.inflate(R.layout.view_pager_exam_guide_zn_sub4_type1, null);
                break;
        }

        registration_date = view1.findViewById(R.id.tvNumber1Content);
        registration_name = view1.findViewById(R.id.tvNumber2Content);
        registration_condition = view1.findViewById(R.id.tvNumber3Content);
        rvExamTime = view2.findViewById(R.id.rvExamTime);
        tvNotes = view2.findViewById(R.id.tvNotes);
        exam_type = view2.findViewById(R.id.tvNumber2Content);
        exam_kind = view2.findViewById(R.id.tvNumber3Content);
        examTimeAdapter = new ExamTimeAdapter(R.layout.item_rv_skii_exam_time, new ArrayList<>());
        examTimeAdapter.bindToRecyclerView(rvExamTime);
        rvExamTime.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                outRect.top = DisplayHelper.dp2px(getContext(), 8);
            }
        });

        results_date = view3.findViewById(R.id.tvNumber1Content);
        results_name = view3.findViewById(R.id.tvNumber2Content);
        pass_mark = view3.findViewById(R.id.tvNumber3Content);
        results_tips = view3.findViewById(R.id.tvNumber4Content);

        printing_date = view4.findViewById(R.id.tvNumber1Content);
        printing_name = view4.findViewById(R.id.tvNumber2Content);
        printing_flow = view4.findViewById(R.id.tvNumber3Content);

        views.add(view1);
        views.add(view2);
        views.add(view3);
        views.add(view4);
        return views;
    }

    @Subscriber(tag = EventBusTags.CHANGE_PROFESSION)
    private void changeContent(JSZProvinceBean bean) {
        isFirst = true;
    }

    private int getThemeColor() {
        int color = 0;
        switch (UserCache.getAppType()) {
            case Constant.JSZ:
                color = ContextCompat.getColor(getContext(), R.color.jsz_theme);
                break;
            case Constant.KJ:
                color = ContextCompat.getColor(getContext(), R.color.kj_theme);
                break;
            case Constant.JZS:
                color = ContextCompat.getColor(getContext(), R.color.jzs_theme);
                break;
            case Constant.RLZY:
                color = ContextCompat.getColor(getContext(), R.color.rlzy_theme);
                break;
            case Constant.YYDJ:
                color = ContextCompat.getColor(getContext(), R.color.yydj_theme);
                break;
            default:
                color = ContextCompat.getColor(getContext(), R.color.jsz_theme);
                break;

        }
        return color;
    }

    private int getSelectTabColor() {
        @ColorRes int colorRes = 0;
        switch (UserCache.getAppType()) {
            case Constant.JSZ:
                colorRes = R.color.jsz_theme;
                break;
            case Constant.RLZY:
            case Constant.KJ:
                colorRes = R.color.tblack;
                break;
            case Constant.JZS:
                colorRes = R.color.jzs_theme;
                break;
            case Constant.YYDJ:
                colorRes = R.color.yydj_theme;
                break;
            default:
                colorRes = R.color.jsz_theme;
                break;

        }
        return colorRes;
    }
}
