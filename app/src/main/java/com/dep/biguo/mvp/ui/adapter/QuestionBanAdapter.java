package com.dep.biguo.mvp.ui.adapter;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.QuestionBankInfoBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;

import java.util.List;

public class QuestionBanAdapter extends CommonAdapter<QuestionBankInfoBean> {
    public QuestionBanAdapter(@Nullable List<QuestionBankInfoBean> data) {
        super(R.layout.rv_questionban_item, data);
    }

    @Override
    protected void convert(BaseViewHolder holder, QuestionBankInfoBean item) {
        holder.setText(R.id.tvCourseName, String.format("%s (新增%s题)", item.getName(), item.getTotal()));
        holder.setGone(R.id.topicView, !AppUtil.isEmpty(item.getTikus()));
        holder.setText(R.id.topicView, getTopic(item));
    }

    public String getTopic(QuestionBankInfoBean item){
        StringBuilder builder = new StringBuilder();
        for(QuestionBankInfoBean.Tiku tiku : item.getTikus()){
            builder.append(tiku.getName())
                    .append("新增")
                    .append(tiku.getNum())
                    .append("题\n");
        }

        if(builder.length() > 0){
            builder.delete(builder.length() - 1, builder.length());
        }
        return builder.toString();
    }
}
