package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.bean.StudyBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.databinding.StudyActivityBinding;
import com.dep.biguo.di.component.DaggerStudyComponent;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.mvp.contract.StudyContract;
import com.dep.biguo.mvp.presenter.StudyPresenter;
import com.dep.biguo.mvp.ui.adapter.StudyIconAdapter;
import com.dep.biguo.mvp.ui.adapter.StudyQuestionBankAdapter;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.PracticeManager;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.AlignGridItemDecoration;
import com.dep.biguo.widget.DiversificationTextView;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.ToolBar;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.youth.banner.BannerConfig;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

public class StudyActivity extends BaseLoadSirActivity<StudyPresenter> implements StudyContract.View {
    private static final String IS_SHOW_TEST_DATE = "isShowTestDateView";

    private StudyActivityBinding binding;
    private DiversificationTextView testDateView;
    private ToolBar toolBarView;

    @Inject StudyIconAdapter mIconAdapter;
    @Inject StudyQuestionBankAdapter mQuestionBankAdapter;

    private List<Integer> bannerList = new ArrayList<>();

    public static void start(Context context, boolean isShowTestDateView){
        Intent intent = new Intent(context, StudyActivity.class);
        intent.putExtra(IS_SHOW_TEST_DATE, isShowTestDateView);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerStudyComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.study_activity);
        initToolbar();
        initBanner();
        initIconAdapter();
        initQuestionBankAdapter();

        binding.swipeLayout.setOnRefreshListener(refreshLayout -> onRequest());
        return 0;
    }

    private void initBanner() {
        //去掉左右边距，根据比例计算banner图的高度
        binding.bannerView.setImages(new ArrayList<>());
        binding.bannerView.setImageLoader(new BannerRoundImageLoader());
        binding.bannerView.setBannerStyle(BannerConfig.NOT_INDICATOR);
        binding.bannerView.start();
        binding.bannerView.setOnBannerListener(position -> {
            if(bannerList.get(position) == R.drawable.study_super_vip){
                ArmsUtils.startActivity(SvipActivity.class);
            }else {
                RewardCollectActivity.start(getContext());
            }
        });
    }

    public void initToolbar(){
        //懒得写xml文件
        testDateView = new DiversificationTextView(this);
        testDateView.setPadding(DisplayHelper.dp2px(this, 10), 0, DisplayHelper.dp2px(this, 10), 0);
        testDateView.setStartChar("试");
        testDateView.setEndChar("天");
        testDateView.setGravity(Gravity.CENTER);
        testDateView.setColor(getResources().getColor(R.color.theme));
        testDateView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        testDateView.setTextColor(getResources().getColor(R.color.tblack));
        testDateView.setText("距考试0天");
        testDateView.setVisibility(View.INVISIBLE);

        toolBarView = new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText(String.format("%s", UserCache.getCourse().getName()))
                .setOnClickListener(v -> {
                    /*List<String> codeList = new ArrayList<>();
                    codeList.add(UserCache.getCourse().getCode());
                    RealQuery.deleteCacheCourse(codeList);
                    showMessage("该科缓存题库清除成功");*/
                })
                .setTarget(ToolBar.Builder.RIGHT)
                .setViewLayout(testDateView)
                .setTextMaxLength(6)
                .build();
    }

    public void initIconAdapter(){
        binding.iconRecyclerView.getItemAnimator().setChangeDuration(0);
        binding.iconRecyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));
        mIconAdapter.bindToRecyclerView(binding.iconRecyclerView);
        mIconAdapter.setOnItemClickListener((adapter, view, position) -> {
            StudyBean.Icon icon = mIconAdapter.getItem(position);
            CourseGroupBean.CourseBean courseBean = UserCache.getCourse();

            if(StudyIconAdapter.YAMI.equals(icon.getType())){//押密
                if(mPresenter.hasYami() == -1) {//不可预订，Toast提示
                    showMessage("当前科目不支持考前押密预订，敬请谅解！");

                }else if(mPresenter.hasYami() == StartFinal.NO) {//预订押密
                    if(!MainAppUtils.checkLogin(StudyActivity.this)) return;
                    OrderSecretActivity.start(StudyActivity.this, courseBean.getName(), courseBean.getCode());

                }else if(!mPresenter.isBuyYami()) {//未购买，跳转到商品详情页
                    GroupGoodsActivity.start(StudyActivity.this, courseBean.getCode(), StartFinal.YAMI);

                }else if(mPresenter.isBuyYami()){//已购买则跳转到学习页
                    SecretListActivity.start(this, courseBean.getCode(), courseBean.getName());
                }

            }else if(StudyIconAdapter.VIDEO.equals(icon.getType())){//视频
                if(!mPresenter.hasVideo()){
                    showMessage("视频未上架");
                    return;
                }
                VideoTypeListActivity.start(StudyActivity.this, courseBean.getId(), courseBean.getName(), courseBean.getCode());

            }else if(StudyIconAdapter.ERRORS.equals(icon.getType())){//错题
                if(!MainAppUtils.checkLogin(StudyActivity.this)) return;
                if(!MainAppUtils.checkLogin(this)) return;
                startPractice(courseBean, mPresenter.getStudyBean().getError_topic_nums(), 0, PracticeHelper.PRACTICE_ERROR, 0);

            }else if(StudyIconAdapter.COLLECTIONS.equals(icon.getType())){//收藏
                if(!MainAppUtils.checkLogin(StudyActivity.this)) return;
                if(!MainAppUtils.checkLogin(this)) return;
                startPractice(courseBean, mPresenter.getStudyBean().getCollection_topic_nums(),0, PracticeHelper.PRACTICE_COLL, 0);
            }
        });
    }

    public void initQuestionBankAdapter(){
        binding.questionBankRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setColorRes(R.color.bgc));
        mQuestionBankAdapter.bindToRecyclerView(binding.questionBankRecyclerView);
        mQuestionBankAdapter.setOnItemClickListener((adapter, view, position) -> {
            StudyBean.QuestionBank questionBank = mQuestionBankAdapter.getItem(position);
            CourseGroupBean.CourseBean courseBean = UserCache.getCourse();
            doQuestion(questionBank, courseBean);
        });
    }

    public void doQuestion(StudyBean.QuestionBank questionBank, CourseGroupBean.CourseBean courseBean){
        if(questionBank.getHas() != StartFinal.YES) {
            showMessage("题库未上架");
            return;
        }

        if(questionBank.getType() == PracticeHelper.PRACTICE_COURSE){//免费题库
            checkToLogin(courseBean, questionBank);

        }else if(questionBank.getType() == PracticeHelper.PRACTICE_VIP){//VIP题库
            if(!MainAppUtils.checkLogin(StudyActivity.this)) return;

            if(mQuestionBankAdapter.getIsOpenVip() == StudyQuestionBankAdapter.HAS_BUY){//已购买VIP题库则跳转到题目列表
                startPractice(courseBean, questionBank.getTotal(), questionBank.getVersion(), questionBank.getType(), questionBank.getRecord_expire_time());

            }else {//未购买则跳转到商品详情
                GroupGoodsActivity.start(StudyActivity.this, courseBean.getCode(), StartFinal.VIP);

            }

        }else if(questionBank.getType() == PracticeHelper.PRACTICE_CHAPTER){//章节训练
            if(!MainAppUtils.checkLogin(StudyActivity.this)) return;
            ChapterActivity.Start(this, courseBean.getId(), courseBean.getName(), courseBean.getCode());

        }else if(questionBank.getType() == PracticeHelper.PRACTICE_TRUE){//历年真题
            if(!MainAppUtils.checkLogin(StudyActivity.this)) return;
            //TruePaperAllNewActivity.start(StudyActivity.this, UserCache.getCourse());

        }else if(questionBank.getType() == PracticeHelper.PRACTICE_TYPE_HIGH){//高频考点
            if(!MainAppUtils.checkLogin(StudyActivity.this)) return;

            if(mQuestionBankAdapter.getIsOpenHigh() == StudyQuestionBankAdapter.HAS_BUY){//已购买高频考点题库则跳转到题目列表
                startPractice(courseBean, questionBank.getTotal(), questionBank.getVersion(), questionBank.getType(), questionBank.getRecord_expire_time());

            }else {//未购买则跳转到商品详情
                GroupGoodsActivity.start(StudyActivity.this, courseBean.getCode(), StartFinal.HIGH_FREQUENCY);
            }

        }else if(questionBank.getType() == PracticeHelper.PRACTICE_INTERNET){//网络助学
            if(!MainAppUtils.checkLogin(StudyActivity.this)) return;

            InternetStudyGoodsActivity.start(StudyActivity.this);
        }
    }


    public void checkToLogin(CourseGroupBean.CourseBean courseBean, StudyBean.QuestionBank questionBank){
        if (UserCache.getUserCache() == null) {
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent("未登录不会保存答题记录到服务器，确定继续答题吗？")
                    .setNegativeText("继续答题")
                    .setNegativeClickListener(v -> startPractice(courseBean, questionBank.getTotal(), questionBank.getVersion(), questionBank.getType(), questionBank.getRecord_expire_time()))
                    .setPositiveText("去登录")
                    .setPositiveClickListener(v -> MainAppUtils.checkLogin(this))
                    .builder()
                    .show();
        }else {
            startPractice(courseBean, questionBank.getTotal(), questionBank.getVersion(), questionBank.getType(), questionBank.getRecord_expire_time());
        }
    }

    public void startPractice(CourseGroupBean.CourseBean courseBean, int total, long version, int mMainType, long record_expire_time){
        //改版之后，防止用户升级到6.3.5版本出现无法从模拟模式切换到答题模式
        if(UserCache.getPracticeMode() == PracticeHelper.MODE_SIMU){
            UserCache.cachePracticeMode(PracticeHelper.MODE_DEFAULT);
        }

        PracticeManager practiceManager = new PracticeManager();
        practiceManager.mTitle = courseBean.getName();
        practiceManager.mCode = courseBean.getCode();
        practiceManager.courseName = courseBean.getName();
        practiceManager.mMainType = mMainType;
        practiceManager.mMainTypeName = PracticeHelper.getPracticeType(mMainType);
        practiceManager.mPracticeType = mMainType;
        practiceManager.mPracticeMode = UserCache.getPracticeMode();
        practiceManager.mTotalCount = total;
        practiceManager.version = version;
        practiceManager.expire_time = record_expire_time;
        PracticeV3Activity.start(this, practiceManager);
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onRequest() {
        mPresenter.getStudyHome(UserCache.getCourse().getId());
    }

    @Override
    protected void onResume() {
        super.onResume();
        //除初始化以外，每次进入页面都调用这行代码进行刷新
        if(mPresenter.getStudyBean() != null && UserCache.getUserCache() != null) {
            binding.swipeLayout.finishRefresh();
            binding.swipeLayout.autoRefresh();
        }
    }

    @Override
    public void getHomeDataFail() {
        binding.swipeLayout.finishRefresh(false);
    }

    @Override
    public void getHomeDataSuccess(StudyBean studyBean) {
        //设置顶部标题栏右边的文字,剩余天数小于0则表示未设置考试时间
        testDateView.setText(String.format("距考试%s天", studyBean.getTime_remains()));
        testDateView.setVisibility(studyBean.getTime_remains() < 0 ? View.INVISIBLE : View.VISIBLE);
        toolBarView.requestLayout();

        //将后台返回的图片设置进去
        mIconAdapter.setHasYami(studyBean.getHas_yami());
        mIconAdapter.setError_topic_nums(studyBean.getError_topic_nums());
        mIconAdapter.setCollection_topic_nums(studyBean.getCollection_topic_nums());
        mIconAdapter.setServiceIconList(studyBean.getIcons());
        //设置VIP题库的购买状态
        mQuestionBankAdapter.setIsOpenVip(studyBean.getIs_vip());
        //设置高频考点的购买状态
        mQuestionBankAdapter.setIsOpenHigh(studyBean.getIs_high());
        //刷新题库
        mQuestionBankAdapter.setNewData(studyBean.getTikus());

        //今日做题数量
        binding.doQuestionCountView.setText(mPresenter.getDoQuestionCountFormat(studyBean.getToday_answered()+""));
        //答题完成率
        binding.finishRateView.setText(mPresenter.getFinishRateFormat(studyBean.getCompletion_rate()));
        //答题正确率
        binding.correctRateView.setText(mPresenter.getCorrectRateFormat(studyBean.getCorrect_rate()));
        //已学时间
        binding.studyTimeView.setText(mPresenter.getStudyTimeFormat(studyBean.getTake_time()));
        //超级vip和资料有奖征集
        bannerList.clear();
        if(studyBean.getIs_super_vip() == 1){
            bannerList.add(R.drawable.study_super_vip);
        }
        for (StudyBean.QuestionBank bank : studyBean.getTikus()){
            if(bank.getHas() == StartFinal.NO){
                bannerList.add(R.drawable.study_coll_topic);
                break;
            }
        }
        if(!AppUtil.isEmpty(bannerList)){
            binding.bannerView.setVisibility(View.VISIBLE);
            binding.bannerView.update(bannerList);
        }

        binding.swipeLayout.finishRefresh(true);
    }


    @Override
    public Context getContext() {
        return this;
    }

    @Subscriber(tag = EventBusTags.GET_USERINFO_SUCCESS)
    private void loginSuccess(UserBean user) {
        binding.swipeLayout.autoRefresh();
    }
}