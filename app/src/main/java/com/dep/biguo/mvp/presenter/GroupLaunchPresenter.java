package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupLaunchBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.RealInfoBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.GroupLaunchContract;
import com.dep.biguo.mvp.ui.adapter.GroupLaunchAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class GroupLaunchPresenter extends BasePresenter<GroupLaunchContract.Model, GroupLaunchContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    GroupLaunchAdapter mLaunchGroupAdapter;

    private List<DiscountBean> discountList;//优惠券列表
    private PayResultListener mResultPayListener;//支付的监听接口

    private String goodsType;
    private String courseCode;
    private Map<String, String> previewContentMap = new HashMap<>();

    @Inject
    public GroupLaunchPresenter(GroupLaunchContract.Model model, GroupLaunchContract.View rootView) {
        super(model, rootView);
    }

    public void init(Intent intent){
        goodsType = intent.getStringExtra(StartFinal.GOODS_TYPE);
        courseCode = intent.getStringExtra(StartFinal.COURSE_CODE);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
                mRootView.payCancel();
            }
        };
        getData();
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void getData() {
        mModel.getData(UserCache.getProfession().getId(), goodsType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<GroupLaunchBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<GroupLaunchBean>> s) {
                        if (s.isSuccess()) {
                            mLaunchGroupAdapter.clearCheckBean();
                            //选中与传递过来的课程代码相符的课程
                            GroupLaunchBean toppingBean = null;
                            for(GroupLaunchBean groupBean : s.getData()){
                                if(groupBean.getCode().equals(courseCode)){
                                    toppingBean = groupBean;
                                    break;
                                }
                            }
                            //将选中的置顶
                            if(toppingBean != null) {
                                s.getData().remove(toppingBean);
                                s.getData().add(0,toppingBean);
                                mLaunchGroupAdapter.setCheckBean(toppingBean);
                            }
                            //设置列表数据
                            mLaunchGroupAdapter.setNewData(s.getData());
                            //刷新成功，设置刷新控件停止刷新动画
                            mRootView.getDataSuccess();

                            if(AppUtil.isEmpty(mLaunchGroupAdapter.getData())){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.hideLoading();
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }


    /**获取优惠券
     *
     */
    public void getDiscountCard(){
        if(discountList != null) {
            mRootView.showPayDialog(discountList);
            return;
        }

        mModel.getDiscountCard(1, goodsType, "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(discountList);
                        }
                    }
                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(new ArrayList<>());
                    }
                });
    }


    /**获取预览内容
     * @param code
     */
    public void getPreviewContent(String code) {
        if(previewContentMap.containsKey(code)){
            mRootView.showPreviewContent(previewContentMap.get(code));
            return;
        }

        mModel.getPreviewContent(code)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            Gson gson = new Gson();
                            Map<String, String> map = gson.fromJson(gson.toJson(s.getData()), new TypeToken<HashMap<String, String>>(){}.getType());
                            String previewContent = map.get("A");
                            previewContentMap.put(code, previewContent);
                            mRootView.showPreviewContent(previewContent);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    /**查询是否已实名认证
     */
    public void getRealInfo() {
        mModel.getRealInfo()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RealInfoBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RealInfoBean> response) {
                        if (response.isSuccess()) {
                            mRootView.realInfoSuccess(response.getData());
                        }
                    }
                });
    }

    public void pay(String courseCode, String payType, int productId, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(courseCode, payType, productId, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        mModel.payGroupOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }
                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mRootView.paySuccess();
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }

    /**创建一个创建订单需要的参数集合
     * @param courseCode 课程代码，支持多选，以"_"连接
     * @param payType 支付方式{@link PayUtils#PAY_TYPE_WEXIN,PayUtils#PAY_TYPE_ALIPAY,PayUtils#PAY_TYPE_INTEGRAL,PayUtils#PAY_TYPE_COIN}
     * @return
     */
    private PayParamsBean getParams(String courseCode, String payType, int productId, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        paramsBean.put(PayParamsBean.CODE,courseCode);
        paramsBean.put(PayParamsBean.TYPE, goodsType);
        paramsBean.put(PayParamsBean.R_ID, "0");
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());

        //视频需要传递product_id
        if(StartFinal.VIDEO1.equals(goodsType) || StartFinal.VIDEO2.equals(goodsType)){
            paramsBean.put(PayParamsBean.PRODUCT_ID, productId);
        }
        return paramsBean;
    }


    /**返回支付弹窗里需要显示的提示语
     *
     */
    public String getPayDialogHint(){
        String hint = UserCache.isMemberShip() ? "您已开通笔果折扣卡，当前享受折扣卡优惠\n" : "";

        if(PayUtils.YAMI.equals(goodsType)){
            hint += "您将购买虚拟商品，购买后不支持立即退款\n如需退款，请联系客服";
        }
        return hint;
    }

    public String getGoodsTypeName(){
        if(StartFinal.VIP.equals(goodsType)){
            return "VIP题库";

        }else if(StartFinal.YAMI.equals(goodsType)){
            return "考前押密";

        }else if(StartFinal.VIDEO1.equals(goodsType)){
            return "精讲视频";
        }else if(StartFinal.VIDEO2.equals(goodsType)){
            return "串讲视频";
        }
        return "";
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }

}
