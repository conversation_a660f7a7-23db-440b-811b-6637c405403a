package com.dep.biguo.mvp.ui.adapter;

import android.widget.ImageView;
import android.text.TextUtils;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ActivityEvaluation;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.http.imageloader.glide.ImageConfigImpl;
import com.jess.arms.utils.ArmsUtils;

import java.util.List;

/**
 * 活动评价适配器
 */
public class ActivityEvaluationAdapter extends BaseQuickAdapter<ActivityEvaluation, BaseViewHolder> {
    
    private ImageLoader mImageLoader;
    private OnEvaluationItemClickListener mOnItemClickListener;
    
    public interface OnEvaluationItemClickListener {
        void onLikeClick(ActivityEvaluation evaluation, int position);
        void onImageClick(String imageUrl, int position);
    }
    
    public void setOnEvaluationItemClickListener(OnEvaluationItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }
    
    public ActivityEvaluationAdapter() {
        super(R.layout.item_activity_evaluation);
    }
    
    @Override
    protected void convert(BaseViewHolder helper, ActivityEvaluation evaluation) {
        if (evaluation == null) return;
        
        // 设置用户头像
        ImageView avatarView = helper.getView(R.id.iv_user_avatar);
        if (!TextUtils.isEmpty(evaluation.getUserAvatar()) && getImageLoader() != null) {
            getImageLoader().loadImage(mContext,
                    ImageConfigImpl.builder()
                            .url(evaluation.getUserAvatar())
                            .imageView(avatarView)
                            .placeholder(R.drawable.ic_default_avatar)
                            .errorPic(R.drawable.ic_default_avatar)
                            .build());
        }
        
        // 设置用户名
        helper.setText(R.id.tv_user_name, evaluation.getUserName() != null ? evaluation.getUserName() : "");
        
        // 设置评分星星
        setRatingStars(helper, evaluation.getRating());
        
        // 设置评价内容
        helper.setText(R.id.tv_evaluation_content, evaluation.getContent() != null ? evaluation.getContent() : "");
        
        // 设置评价时间
//        helper.setText(R.id.tv_evaluation_time, evaluation.getTime() != null ? evaluation.getTime() : "");
        
        // 设置点赞数量
        helper.setText(R.id.tv_like_count, String.valueOf(evaluation.getLikeCount()));
        
        // 设置点赞状态
        ImageView likeView = helper.getView(R.id.iv_like);
        if (evaluation.isLiked()) {
            likeView.setImageResource(R.drawable.thumbs_filled);

        } else {
            likeView.setImageResource(R.drawable.thumbs);
        }
        
        // 设置评价图片
        setupEvaluationImages(helper, evaluation.getImages());
        
        // 设置点赞点击事件
        helper.getView(R.id.ll_like).setOnClickListener(v -> {
            if (mOnItemClickListener != null) {
                mOnItemClickListener.onLikeClick(evaluation, helper.getLayoutPosition());
            }
        });
    }
    
    private void setupEvaluationImages(BaseViewHolder helper, List<String> images) {
        LinearLayout imageGrid = helper.getView(R.id.ll_images_grid);
        RecyclerView imageRv = helper.getView(R.id.rv_evaluation_images);
        
        if (images != null && !images.isEmpty()) {
            if (images.size() <= 3) {
                // 使用固定的3张图片布局
                imageGrid.setVisibility(android.view.View.VISIBLE);
                imageRv.setVisibility(android.view.View.GONE);
                
                // 设置3张图片
                ImageView[] imageViews = {
                    helper.getView(R.id.iv_image_1),
                    helper.getView(R.id.iv_image_2),
                    helper.getView(R.id.iv_image_3)
                };
                
                for (int i = 0; i < imageViews.length; i++) {
                    if (i < images.size()) {
                        imageViews[i].setVisibility(android.view.View.VISIBLE);
                        loadEvaluationImage(imageViews[i], images.get(i));
                    } else {
                        imageViews[i].setVisibility(android.view.View.INVISIBLE);
                    }
                }
            } else {
                // 使用RecyclerView显示更多图片
                imageGrid.setVisibility(android.view.View.GONE);
                imageRv.setVisibility(android.view.View.VISIBLE);
                // TODO: 设置RecyclerView适配器
            }
        } else {
            imageGrid.setVisibility(android.view.View.GONE);
            imageRv.setVisibility(android.view.View.GONE);
        }
    }
    
    private void loadEvaluationImage(ImageView imageView, String imageUrl) {
        if (!TextUtils.isEmpty(imageUrl) && getImageLoader() != null) {
            getImageLoader().loadImage(mContext,
                    ImageConfigImpl.builder()
                            .url(imageUrl)
                            .imageView(imageView)
                            .placeholder(R.color.bgc)
                            .errorPic(R.color.bgc)
                            .build());
        }
    }
    
    private void setRatingStars(BaseViewHolder helper, int rating) {
        // 设置5个星星的显示状态
        for (int i = 1; i <= 5; i++) {
            int starId = getStarViewId(i);
            if (starId != -1) {
                if (i <= rating) {
                    helper.setImageResource(starId, R.drawable.start);
                } else {
                    helper.setImageResource(starId, R.drawable.start_empty);
                }
            }   
        }
    }
    
    private int getStarViewId(int position) {
        switch (position) {
            case 1: return R.id.iv_star_1;
            case 2: return R.id.iv_star_2;
            case 3: return R.id.iv_star_3;
            case 4: return R.id.iv_star_4;
            case 5: return R.id.iv_star_5;
            default: return -1;
        }
    }
    
    private ImageLoader getImageLoader() {
        if (mImageLoader == null && mContext != null) {
            try {
                mImageLoader = ArmsUtils.obtainAppComponentFromContext(mContext).imageLoader();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return mImageLoader;
    }
}