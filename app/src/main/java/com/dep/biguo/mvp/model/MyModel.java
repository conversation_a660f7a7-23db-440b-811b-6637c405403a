package com.dep.biguo.mvp.model;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.mvp.model.api.service.PracticeService;
import com.dep.biguo.mvp.model.api.service.UserService;
import com.google.gson.Gson;
import com.jess.arms.integration.IRepositoryManager;
import com.jess.arms.mvp.BaseModel;

import com.jess.arms.di.scope.ActivityScope;

import javax.inject.Inject;

import com.dep.biguo.mvp.contract.MyContract;

import java.util.Map;

import io.reactivex.Observable;


@ActivityScope
public class MyModel extends BaseModel implements MyContract.Model {
    @Inject
    Gson mGson;
    @Inject
    Application mApplication;

    @Inject
    public MyModel(IRepositoryManager repositoryManager) {
        super(repositoryManager);
    }

    @Override
    public Observable<BaseResponse<Map<String, String>>> exchangeCode(String exchangeCode) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(PracticeService.class)
                        .exchangeCode(exchangeCode))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mGson = null;
        this.mApplication = null;
    }
}