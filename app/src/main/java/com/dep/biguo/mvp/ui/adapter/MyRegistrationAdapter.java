package com.dep.biguo.mvp.ui.adapter;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.MyRegistrationBean;
import com.dep.biguo.common.CommonAdapter;
import com.google.android.material.imageview.ShapeableImageView;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.utils.ArmsUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

import java.util.List;
import java.util.Random;
import android.util.Log;

/**
 * 我的报名列表适配器
 */
public class MyRegistrationAdapter extends CommonAdapter<MyRegistrationBean> {

    private static final String TAG = "MyRegistrationAdapter";
    private OnItemActionListener mOnItemActionListener;
    private ImageLoader mImageLoader;
    private boolean mUsePicsumPhotos = true; // 是否使用picsum.photos测试图片
    private Random mRandom = new Random();

    public interface OnItemActionListener {
        void onItemClick(MyRegistrationBean item, int position);
        void onActionClick(MyRegistrationBean item, int position);
    }

    public MyRegistrationAdapter(@Nullable List<MyRegistrationBean> data) {
        super(R.layout.item_my_registration, data);
        // mImageLoader将在convert方法中初始化，因为此时mContext还为null
    }

    public void setOnItemActionListener(OnItemActionListener listener) {
        this.mOnItemActionListener = listener;
    }

    /**
     * 启用或禁用picsum.photos测试图片
     * @param usePicsumPhotos true表示使用picsum.photos随机图片，false表示使用原始图片URL
     */
    public void setUsePicsumPhotos(boolean usePicsumPhotos) {
        this.mUsePicsumPhotos = usePicsumPhotos;
        notifyDataSetChanged(); // 刷新列表以应用新的图片设置
    }

    /**
     * 获取picsum.photos测试图片URL
     * @param position 列表位置，用于生成不同的图片
     * @return picsum.photos图片URL
     */
    private String getPicsumPhotoUrl(int position) {
        // 优先使用简单的测试图片（更稳定）
        if (position < 5) {
            return getTestImageUrl(position);
        }

        // 使用position作为种子，确保同一位置总是显示相同的图片
        int imageId = 100 + (position % 900); // 生成100-999之间的图片ID
        int width = 400; // 图片宽度
        int height = 300; // 图片高度

        // 使用更稳定的 picsum.photos API 格式
        String url = "https://picsum.photos/id/" + imageId + "/" + width + "/" + height;
        Log.d(TAG, "Generated picsum URL for position " + position + ": " + url);
        return url;
    }

    @Override
    protected void convert(BaseViewHolder holder, MyRegistrationBean item) {
        // 初始化ImageLoader
        if (mImageLoader == null && mContext != null) {
            mImageLoader = ArmsUtils.obtainAppComponentFromContext(mContext).imageLoader();
        }

        // 使用findViewById获取视图
        FrameLayout imageContainer = holder.getView(R.id.imageContainer);
        TextView tvTitle = holder.getView(R.id.tvTitle);
        TextView tvTime = holder.getView(R.id.tvTime);
        TextView tvLocation = holder.getView(R.id.tvLocation);

        // 设置活动图片
        String imageUrl = item.getImageUrl();

        // 如果启用了picsum.photos测试，使用测试图片URL
        if (mUsePicsumPhotos) {
            imageUrl = getPicsumPhotoUrl(holder.getAdapterPosition());
        }

        Log.d(TAG, "Loading image for position " + holder.getAdapterPosition() +
              ", usePicsum: " + mUsePicsumPhotos + ", URL: " + imageUrl);


        // 添加状态标签到图片右上角
        addStatusLabelToImage(imageContainer, item);

        // 设置标题
        tvTitle.setText(item.getTitle());

        // 设置时间
        String timeText = item.getStartTime();
        if (!TextUtils.isEmpty(item.getEndTime()) && !item.getEndTime().equals(item.getStartTime())) {
            timeText += "-" + item.getEndTime().substring(item.getEndTime().lastIndexOf(" ") + 1);
        }
        tvTime.setText(timeText);

        // 设置地点
        tvLocation.setText(item.getLocation());

        // 设置状态/操作按钮 (使用单一的btnStatus视图)
        TextView btnStatus = holder.getView(R.id.btnStatus);

        // 对于已结束和已取消状态，强制显示状态文本而不是操作文本
        if (item.getStatus() == MyRegistrationBean.STATUS_FINISHED ||
            item.getStatus() == MyRegistrationBean.STATUS_CANCELLED) {
            btnStatus.setVisibility(View.VISIBLE);
            btnStatus.setText(item.getStatusText());
            setStatusStyle(btnStatus, item.getStatus());
        } else if (item.getActionType() != MyRegistrationBean.ACTION_NONE && !TextUtils.isEmpty(item.getActionText())) {
            // 其他状态优先显示操作按钮
            btnStatus.setVisibility(View.VISIBLE);
            btnStatus.setText(item.getActionText());
            setActionButtonStyle(btnStatus, item.getActionType());
        } else {
            btnStatus.setVisibility(View.VISIBLE);
            btnStatus.setText(item.getStatusText());
            setStatusStyle(btnStatus, item.getStatus());
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (mOnItemActionListener != null) {
                mOnItemActionListener.onItemClick(item, holder.getAdapterPosition());
            }
        });

        btnStatus.setOnClickListener(v -> {
            if (mOnItemActionListener != null) {
                // 如果有操作按钮功能，则触发操作点击；否则触发普通点击
                if (item.getActionType() != MyRegistrationBean.ACTION_NONE && !TextUtils.isEmpty(item.getActionText())) {
                    mOnItemActionListener.onActionClick(item, holder.getAdapterPosition());
                } else {
                    mOnItemActionListener.onItemClick(item, holder.getAdapterPosition());
                }
            }
        });
    }

    /**
     * 设置状态标签样式
     */
    private void setStatusStyle(TextView tvStatus, int status) {
        switch (status) {
            case MyRegistrationBean.STATUS_PENDING:
                tvStatus.setBackgroundResource(R.drawable.bg_status_attending);
                tvStatus.setTextColor(Color.WHITE);
                break;
            case MyRegistrationBean.STATUS_FINISHED:
                tvStatus.setBackgroundResource(R.drawable.bg_status_finished);
                tvStatus.setTextColor(Color.parseColor("#999999"));
                break;
            case MyRegistrationBean.STATUS_CANCELLED:
                tvStatus.setBackgroundResource(R.drawable.bg_status_cancelled);
                tvStatus.setTextColor(Color.parseColor("#979797"));
                break;
            case MyRegistrationBean.STATUS_REVIEWING:
                tvStatus.setBackgroundResource(R.drawable.bg_round_12_orange);
                tvStatus.setTextColor(Color.WHITE);
                break;
            case MyRegistrationBean.STATUS_APPROVED:
                tvStatus.setBackgroundResource(R.drawable.bg_round_12_green);
                tvStatus.setTextColor(Color.WHITE);
                break;
            case MyRegistrationBean.STATUS_REJECTED:
                tvStatus.setBackgroundResource(R.drawable.bg_round_12_red);
                tvStatus.setTextColor(Color.WHITE);
                break;
            default:
                tvStatus.setBackgroundResource(R.drawable.bg_round_12_gray);
                tvStatus.setTextColor(Color.WHITE);
                break;
        }
    }

    /**
     * 设置操作按钮样式
     */
    private void setActionButtonStyle(TextView btnAction, int actionType) {
        switch (actionType) {
            case MyRegistrationBean.ACTION_CANCEL:
                btnAction.setBackgroundResource(R.drawable.bg_round_16_stroke_red);
                btnAction.setTextColor(mContext.getResources().getColor(R.color.theme));
                break;
            case MyRegistrationBean.ACTION_EVALUATE:
                btnAction.setBackgroundResource(R.drawable.bg_round_16_stroke_theme);
                btnAction.setTextColor(mContext.getResources().getColor(R.color.theme));
                break;
            case MyRegistrationBean.ACTION_VIEW:
            default:
                btnAction.setBackgroundResource(R.drawable.bg_round_16_stroke_theme);
                btnAction.setTextColor(mContext.getResources().getColor(R.color.theme));
                break;
        }
    }

    /**
     * 更新指定位置的数据
     */
    public void updateItem(int position, MyRegistrationBean item) {
        if (position >= 0 && position < getData().size()) {
            getData().set(position, item);
            notifyItemChanged(position);
        }
    }

    /**
     * 使用预定义的测试图片URL（用于调试）
     * @param position 列表位置
     * @return 测试图片URL
     */
    private String getTestImageUrl(int position) {
        // 使用一些已知可用的测试图片
        String[] testUrls = {
            "https://picsum.photos/id/1/400/300",
            "https://picsum.photos/id/10/400/300",
            "https://picsum.photos/id/100/400/300",
            "https://picsum.photos/id/200/400/300",
            "https://picsum.photos/id/300/400/300"
        };

        String url = testUrls[position % testUrls.length];
        Log.d(TAG, "Using test image URL for position " + position + ": " + url);
        return url;
    }

    /**
     * 启用简单测试模式（使用预定义的测试图片）
     */
    public void enableSimpleTestMode() {
        mUsePicsumPhotos = true;
        notifyDataSetChanged();
        Log.d(TAG, "Simple test mode enabled");
    }

    /**
     * 检查当前是否使用picsum.photos测试图片
     * @return true表示正在使用测试图片
     */
    public boolean isUsingPicsumPhotos() {
        return mUsePicsumPhotos;
    }

    /**
     * 使用 Glide 直接加载图片（用于测试图片和备用方案）
     * @param imageView 目标 ImageView
     * @param imageUrl 图片 URL
     * @param position 列表位置（用于日志）
     */
    private void loadImageWithGlide(ShapeableImageView imageView, String imageUrl, int position) {
        Log.d(TAG, "Loading image with Glide for position " + position + ": " + imageUrl);

        try {
            Glide.with(mContext)
                    .load(imageUrl)
                    .apply(new RequestOptions()
                            .placeholder(R.drawable.bg_round_8_gray)
                            .error(R.drawable.bg_round_8_gray)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .centerCrop()
                            .timeout(10000)) // 10秒超时
                    .listener(new com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable>() {
                        @Override
                        public boolean onLoadFailed(com.bumptech.glide.load.engine.GlideException e, Object model,
                                                  com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target,
                                                  boolean isFirstResource) {
                            Log.e(TAG, "Glide load failed for position " + position + ", URL: " + imageUrl, e);
                            // 返回false让Glide处理错误，显示错误占位符
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(android.graphics.drawable.Drawable resource, Object model,
                                                     com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target,
                                                     com.bumptech.glide.load.DataSource dataSource, boolean isFirstResource) {
                            Log.d(TAG, "Glide load success for position " + position + ", source: " + dataSource);
                            return false; // 让Glide继续处理
                        }
                    })
                    .into(imageView);
        } catch (Exception e) {
            Log.e(TAG, "Error loading image with Glide: " + e.getMessage(), e);
            // 异常时手动设置占位符
            imageView.setImageResource(R.drawable.bg_round_8_gray);
        }
    }

    /**
     * 获取带有特定主题的picsum.photos图片URL
     * @param position 列表位置
     * @param category 图片类别 (可选: nature, city, technology, people等)
     * @return 特定主题的picsum.photos图片URL
     */
    private String getPicsumPhotoUrlWithCategory(int position, String category) {
        int imageId = 100 + (position % 900);
        int width = 400;
        int height = 300;

        // 根据不同类别选择不同的图片ID范围
        switch (category.toLowerCase()) {
            case "nature":
                imageId = 100 + (position % 200); // 100-299
                break;
            case "city":
                imageId = 300 + (position % 200); // 300-499
                break;
            case "technology":
                imageId = 500 + (position % 200); // 500-699
                break;
            case "people":
                imageId = 700 + (position % 200); // 700-899
                break;
            default:
                imageId = 100 + (position % 900); // 100-999
                break;
        }

        return "https://picsum.photos/id/" + imageId + "/" + width + "/" + height;
    }

    /**
     * 在图片右上角添加状态标签
     * 关键改动：参数类型改为 FrameLayout
     */
    private void addStatusLabelToImage(FrameLayout imageContainer, MyRegistrationBean item) {
        // 1. 每次都先移除旧的标签，防止复用时出错
        View existingLabel = imageContainer.findViewWithTag("status_label");
        if (existingLabel != null) {
            imageContainer.removeView(existingLabel);
        }

        // 2. 唯一的判断条件：只有当操作类型是“取消报名”时，才添加标签
        if (item.getActionType() == MyRegistrationBean.ACTION_CANCEL) {

            // 3. 创建和设置“待参加”标签
            TextView statusLabel = new TextView(mContext);
            statusLabel.setTag("status_label");
            statusLabel.setText("待参加");
            statusLabel.setTextSize(10);
            statusLabel.setTextColor(Color.WHITE);
            statusLabel.setBackgroundResource(R.drawable.bg_status_attending); // “待参加”状态的背景
            statusLabel.setGravity(Gravity.CENTER);
            statusLabel.setPadding(8, 4, 8, 4);

            // 4. 设置布局参数，将标签定位到容器的左上角
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.WRAP_CONTENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT
            );
            params.gravity = Gravity.TOP | Gravity.START;
            statusLabel.setLayoutParams(params);

            // 5. 将标签添加到图片容器中
            imageContainer.addView(statusLabel);
        }
        // 如果不满足 if 条件，则什么也不做，图片上不会有任何标签
    }
}
