package com.dep.biguo.mvp.model;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.bean.HomeBean;
import com.dep.biguo.bean.SvipBean;
import com.dep.biguo.mvp.contract.JZSHomeContract;
import com.dep.biguo.mvp.contract.YYDJHomeContract;
import com.dep.biguo.mvp.model.api.cache.CommonCache;
import com.dep.biguo.mvp.model.api.service.PracticeService;
import com.dep.biguo.mvp.model.api.service.SkillApi;
import com.google.gson.Gson;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.integration.IRepositoryManager;
import com.jess.arms.mvp.BaseModel;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.rx_cache2.DynamicKey;
import io.rx_cache2.EvictProvider;


@FragmentScope
public class YYDJHomeModel extends BaseModel implements YYDJHomeContract.Model {
    @Inject
    Gson mGson;
    @Inject
    Application mApplication;

    @Inject
    public YYDJHomeModel(IRepositoryManager repositoryManager) {
        super(repositoryManager);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mGson = null;
        this.mApplication = null;
    }

    @Override
    public Observable<BaseResponse<HomeBean>> getHomeData(String code, boolean update, int province_id) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(SkillApi.class)
                .home_data(code, 118, province_id))//写死，
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

    @Override
    public Observable<BaseResponse<HomeBean>> getHomeData(String code, boolean update) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(SkillApi.class)
                .home_data(code, 118))//写死，
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

    @Override
    public Observable<CourseGroupBean> getCourse(int professions_id, boolean update) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(SkillApi.class)
                .course_data(professions_id))
                .flatMap(observable -> mRepositoryManager.obtainCacheService(CommonCache.class)
                        .getCourse(observable, new DynamicKey(professions_id), new EvictProvider(update))
                        .map(baseResponseReply -> baseResponseReply.getData()));
    }

    @Override
    public Observable<BaseResponse<SvipBean>> getSvipDetail(int professions_id) {
        return null;
    }

    @Override
    public Observable<BaseResponse> editCourse(int courses_id) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(PracticeService.class)
                .course_edit(courses_id))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

    @Override
    public Observable<BaseResponse> editScore(String code, String score) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(PracticeService.class)
                .course_score_edit(code, score))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }
}