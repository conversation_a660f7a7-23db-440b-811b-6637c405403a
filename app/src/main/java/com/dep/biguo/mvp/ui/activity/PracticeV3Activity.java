package com.dep.biguo.mvp.ui.activity;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.AdBean;
import com.dep.biguo.bean.CardBean;
import com.dep.biguo.bean.CommentBean;
import com.dep.biguo.bean.PracticeCommentOperateBean;
import com.dep.biguo.bean.PracticeRecordBean;
import com.dep.biguo.bean.PracticeResultBean;
import com.dep.biguo.bean.PracticeVideoBean;
import com.dep.biguo.bean.QuestionBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.PracticeV3ActivityBinding;
import com.dep.biguo.di.component.DaggerPracticeV3Component;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.mvp.contract.PracticeV3Contract;
import com.dep.biguo.mvp.presenter.PracticeV3Presenter;
import com.dep.biguo.mvp.ui.adapter.practice.PracticeV2Adapter;
import com.dep.biguo.mvp.ui.adapter.practice.PracticeV2ItemAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.utils.TimeFormatUtils;
import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.UrlAddParamUtil;
import com.dep.biguo.utils.database2.RealmHelper;
import com.dep.biguo.utils.database2.ResultListener;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.socket.WebSocketConnect;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.dialog.PracticeCardDialog;
import com.dep.biguo.dialog.PracticeErrorDialog;
import com.dep.biguo.dialog.PracticeInputDialog;
import com.dep.biguo.dialog.PracticeSettingDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.widget.QuestionView;
import com.dep.biguo.widget.VideoPlayerView;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.plv.thirdpart.blankj.utilcode.util.AppUtils;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

public class PracticeV3Activity extends BaseLoadSirActivity<PracticeV3Presenter> implements PracticeV3Contract.View, View.OnClickListener {
    public static final String PRACTICE_MANAGER = "practice_manager";
    public static final int INSERT_COMMENT = 1;//插入一条评论（回复）
    public static final int DELETE_COMMENT = -1;//删除一条评论（回复）
    public static final int MAX_RECORD_COUNT = 30; //每30题上传一次答题记录
    public static final int UPLOAD_TYPE_NORMAL = 1; //正常上传
    public static final int UPLOAD_TYPE_CLOSE = 2; //关闭页面上传
    public static final int UPLOAD_TYPE_COMMIT = 3; //交卷上传

    public static final int LOAD_MORE_CURRENT = 2; //加载当前页

    private PracticeV3ActivityBinding binding;

    @Inject PracticeManager mPracticeManager;
    @Inject List<CardBean.Topic> mCardData;//答题卡
    @Inject List<QuestionBean> mQuestionData;//存储所有获取到的题目
    @Inject List<PracticeRecordBean.RecordBean> mCacheRecordData;//已作答的题目
    @Inject List<CardBean.TopicHead> topicHeadList;//题型

    private PracticeV2Adapter questionAdapter;//题目适配器
    private PracticeCardDialog mPracticeCardDialog; //答题卡
    private PopupDialog<PracticeCommentOperateBean> commentPopupWindow;//长按评论弹出的弹窗
    private PracticeInputDialog inputDialog;//输入评论或回复的弹窗
    private PracticeErrorDialog mPracticeErrorDialog; //答题反馈

    private boolean isUploadSaveData;//是否正在上传数据，用于保存退出或手动交卷时，避免onPause()方法也调用一次保存
    private WebSocketConnect socketConnect;//长链接统计学习时间
    private PopupDialog typePopupDialog;
    private long enterThisActivityDate;//进入这个页面时的系统时间
    private int mDoCount = 0;

    public static void start(Context context, PracticeManager practiceManager){
        Intent intent = new Intent(context, PracticeV3Activity.class);
        intent.putExtra(PRACTICE_MANAGER, practiceManager);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerPracticeV3Component
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    private void initIntent() {
        enterThisActivityDate = System.currentTimeMillis();

        Intent intent = getIntent();

        PracticeManager practiceManager = (PracticeManager) intent.getSerializableExtra(PRACTICE_MANAGER);
        if (practiceManager == null) return;

        mPracticeManager.mTitle = practiceManager.mTitle;
        mPracticeManager.mCode = practiceManager.mCode;
        mPracticeManager.courseName = practiceManager.courseName;
        mPracticeManager.mMainType = practiceManager.mMainType;
        mPracticeManager.mMainTypeName = practiceManager.mMainTypeName;
        mPracticeManager.mTopicType = practiceManager.mTopicType;
        mPracticeManager.mTopicTypeName = practiceManager.mTopicTypeName;
        mPracticeManager.mTotalCount = practiceManager.mTotalCount;
        mPracticeManager.mPracticeMode = practiceManager.mPracticeMode;//UserHelper.getPracticeMode();
        mPracticeManager.mPracticeType = practiceManager.mPracticeType;
        mPracticeManager.mCurrentPosition = practiceManager.mCurrentPosition;
        mPracticeManager.mSubTitle = practiceManager.mSubTitle;
        mPracticeManager.mPaperId = practiceManager.mPaperId;
        mPracticeManager.mPaperName = practiceManager.mPaperName;
        mPracticeManager.mChapterId = practiceManager.mChapterId;
        mPracticeManager.mChapterName= practiceManager.mChapterName;
        mPracticeManager.mChapterSectionId = practiceManager.mChapterSectionId;
        mPracticeManager.mChapterSectionName = practiceManager.mChapterSectionName;
        mPracticeManager.mTestPaperId = practiceManager.mTestPaperId;
        mPracticeManager.mTestPaperName = practiceManager.mTestPaperName;
        mPracticeManager.mTrueShareLock = practiceManager.mTrueShareLock;
        mPracticeManager.dayCardPrize = practiceManager.dayCardPrize;
        mPracticeManager.version = practiceManager.version;
        if(practiceManager.endId > 0){//自带ID
            mPracticeManager.endId = practiceManager.endId;
        }else if((!mPracticeManager.isDefaultMode() && !mPracticeManager.isShowMode()) //非答题模式，不需要答题记录
                || UserCache.getUserCache() == null){//未登录的，也不需要答题记录
            mPracticeManager.endId = 0;
        }else {//需要从数据库取
            mPracticeManager.endId = RealmHelper.queryLastId(practiceManager);
        }

        mPracticeManager.mIsShowTimer = PracticeHelper.isSupportShowTimer(mPracticeManager.mPracticeMode);
        mPracticeManager.mIsShowCorrectErrorCount = PracticeHelper.isSupportShowNumber(mPracticeManager.mPracticeMode, mPracticeManager.mPracticeType, mPracticeManager.mTopicType);
        mPracticeManager.mIsShowCommit = PracticeHelper.isSupportShowCommit(mPracticeManager.mPracticeMode);
        mPracticeManager.mIsSupportCommit = PracticeHelper.isSupportCommit(mPracticeManager.mPracticeMode, mPracticeManager.mPracticeType, mPracticeManager.mTopicType);
        mPracticeManager.mIsSupportPractice = PracticeHelper.isSupportPractice(mPracticeManager.mPracticeMode, mPracticeManager.mTopicType);
        mPracticeManager.mIsOnlySelect = PracticeHelper.isOptionSelect(mPracticeManager.mPracticeMode);
        mPracticeManager.mRecordType = PracticeHelper.getRecordType(mPracticeManager.mPracticeMode, mPracticeManager.mPracticeType, mPracticeManager.mTopicType);

        mPracticeManager.showContinue = PracticeHelper.getIsContinue(mPracticeManager.mPracticeMode);

        mPracticeManager.autoNext = UserCache.getPracticeAuto();
        mPracticeManager.autoCorrectNext = UserCache.getPracticeCorrectAuto();
        mPracticeManager.autoRemoveError = UserCache.getPracticeErrorRemove();

        mPracticeManager.showLoadNext = PracticeHelper.isShowLoadMore(mPracticeManager.mPracticeMode, mPracticeManager.mPracticeType);

        //查询未提交的答题记录
        RealmHelper.queryUnCommitHistory(mPracticeManager)
                .subscribe(new ResultListener<List<PracticeRecordBean.RecordBean>>() {
                    @Override
                    public void onNext(List<PracticeRecordBean.RecordBean> recordBeans) {
                        mCacheRecordData.addAll(recordBeans);
                    }
                });
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.practice_v3_activity);
        binding.setOnClickListener(this);
        new UmengEventUtils(getContext())
                .addParams("user_type", UserCache.getUserCache() == null ? "未登录" : "已登录")
                .addParams("enter_time", enterThisActivityDate)
                .addParams("entry_path_type", mPracticeManager.mPracticeType)
                .pushEvent(UmengEventUtils.ENTER_QUESTION_PAGE);

        //软键盘打开的时候，布局不要顶出屏幕
        KeyboardUtils.setShowKeyboardScroll(this, binding.getRoot(), true);


        initIntent();
        initToolbar();
        initUI();
        initQuestionRecyclerView();
        initQuestionAdapter();
        initCustomerServiceView();
        initToTopView();

        return 0;
    }

    //初始化标题栏
    public void initToolbar(){
        int topPadding = DisplayHelper.isFillHeightScreen(this) ? DisplayHelper.getStatusBarHeight(this) : 0;
        binding.toolBarLayout.setPadding(0, topPadding, 0, 0);
        binding.leftView.setOnClickListener(v -> showLeaveDialog());
        binding.rightView.setOnClickListener(v -> showRealQuestionShareDialog());
        //做题模式或背题模式
        binding.modeLayout.setVisibility(!mPracticeManager.isSimuMode() && !mPracticeManager.isAllMode() ? View.VISIBLE : View.GONE);
        //模拟模式下（每日打卡或模拟试卷）,不展示切换模式的控件，改为展示计时器控件展示
        binding.timeLayout.setVisibility(mPracticeManager.isSimuMode() ? View.VISIBLE : View.GONE);
        //解析模式
        binding.titleView.setVisibility(mPracticeManager.isAllMode() ? View.VISIBLE : View.GONE);

        //设置倒计时的图片颜色，适配夜间模式
        TintDrawableUtil.StartTintDrawable(binding.timeLabelView, AppUtil.getColorRes(this, R.color.tblack));

        //根据模式展示UI
        checkMode();
        View.OnClickListener modeCheckListener = view -> {
            mPracticeManager.mPracticeMode = binding.answerModeView == view ? PracticeHelper.MODE_DEFAULT : PracticeHelper.MODE_SHOW;
            UserCache.cachePracticeMode(mPracticeManager.mPracticeMode);
            //加个判断是防止重复选同一个模式触发动画
            if(mPracticeManager.mPracticeMode != Integer.parseInt(binding.slideLayout.getTag().toString())) {
                //切换做题的模式
                checkMode();
                //底部一排按钮根据答题模式变化
                initUI();
                questionAdapter.notifyDataSetChanged();
            }
        };
        binding.answerModeView.setOnClickListener(modeCheckListener);
        binding.rememberModeView.setOnClickListener(modeCheckListener);

    }

    //切换到答题模式或切换到背题模式
    public void checkMode(){
        if(binding == null) return;

        //埋点统计答题模式的使用时长
        uploadPoint();

        //记录模式滑块处于哪个模式按钮下
        binding.slideLayout.setTag(mPracticeManager.mPracticeMode);
        //是否是答题模式
        boolean isAnswerMode = mPracticeManager.mPracticeMode == PracticeHelper.MODE_DEFAULT;
        //是否可作答，用于设置单选、多选、判断是否可以点击选项
        mPracticeManager.mIsSupportPractice = isAnswerMode || mPracticeManager.mPracticeMode == PracticeHelper.PRACTICE_SIMU;
        //设置模式按钮的文字颜色
        binding.answerModeView.setTextColor(getResources().getColor(isAnswerMode ? R.color.white : R.color.tblack));
        binding.rememberModeView.setTextColor(getResources().getColor(isAnswerMode ? R.color.tblack : R.color.white));
        //刷新做题情况
        mPracticeManager.mIsShowCorrectErrorCount = isAnswerMode;
        refreshCorrectErrorCount();
        //滑块的动画
        int dp90 = DisplayHelper.dp2px(this, 90);
        float start = isAnswerMode  ? dp90 : 0;
        float end = isAnswerMode  ? 0 : dp90;
        ObjectAnimator animator = ObjectAnimator.ofFloat(binding.slideLayout, "translationX", start, end);
        animator.setDuration(200);
        animator.start();
    }

    public void initUI(){
        //课程编号和课程名称
        binding.courseNameView.setText(String.format("[%s]%s", mPracticeManager.mCode, mPracticeManager.mTitle));
        //头部的当前题目与总题数
        refreshCurrentPosition();

        //设置答对数量和答错数量的显示和隐藏
        int showPracticeCountViewVisibility = mPracticeManager.isDefaultMode()
                ? View.VISIBLE
                : View.GONE;
        binding.practiceCountView.setVisibility(showPracticeCountViewVisibility);

        //设置交卷按钮的显示和隐藏
        int showCommitViewVisibility = mPracticeManager.isSimuMode()
                ? View.VISIBLE
                : View.GONE;
        binding.commitView.setVisibility(showCommitViewVisibility);

        //设置清除记录按钮的显示和隐藏
        int showClearViewVisibility = mPracticeManager.isDefaultMode()
                ? View.VISIBLE
                : View.GONE;
        binding.clearView.setVisibility(showClearViewVisibility);

        if(!PracticeHelper.isSupportPractice(mPracticeManager.mPracticeMode, mPracticeManager.mTopicType)){//不可作答
            binding.practiceCountView.setVisibility(View.GONE);//隐藏作答数量（答对数量和答错数量）
        }

        if(mPracticeManager.isErrorPractice()){//错题
            binding.clearView.setVisibility(View.GONE);//隐藏清除答题记录按钮
            binding.deleteView.setVisibility(View.VISIBLE);//显示删除按钮

        }else if(mPracticeManager.isCollPractice()){
            binding.clearView.setVisibility(View.GONE);//隐藏清除答题记录按钮
        }

        int drawableTint = ResourcesCompat.getColor(getResources(), R.color.tblack, getTheme());
        TextDrawableLoader.loadTop(this, binding.collectionView, R.drawable.practice_icon_coll, drawableTint);
        TextDrawableLoader.loadTop(this, binding.practiceCountView, R.drawable.practice_icon_count, drawableTint);
        TextDrawableLoader.loadTop(this, binding.cardView, R.drawable.practice_icon_card, drawableTint);
        TextDrawableLoader.loadTop(this, binding.commitView, R.drawable.practice_icon_hand, drawableTint);
        TextDrawableLoader.loadTop(this, binding.clearView, R.drawable.practice_icon_clear, drawableTint);
        TextDrawableLoader.loadTop(this, binding.deleteView, R.drawable.practice_icon_delete, drawableTint);
        TextDrawableLoader.loadTop(this, binding.settingView, R.drawable.practice_icon_setting, drawableTint);

        //刷新收藏状态
        refreshCollStatus();
    }

    /**初始化题目列表，监听翻页
     *
     */
    private void initQuestionRecyclerView() {
        PagerSnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(binding.questionRecyclerView);
        binding.questionRecyclerView.getItemAnimator().setChangeDuration(0);//设置局部刷新时的动画时间为0
        binding.questionRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                //清除输入框的焦点，触发保存作答
                clearInputAnswerViewFocus();
                //滑动切换题目的时候，取消长按的评论或长按回复的弹窗
                if(commentPopupWindow != null && dx != 0) commentPopupWindow.dismiss();


                pauseAudio();

                LinearLayoutManager mLinearLayoutManager = (LinearLayoutManager) binding.questionRecyclerView.getLayoutManager();
                assert mLinearLayoutManager != null;
                //相等，则认为翻页成功
                if (mLinearLayoutManager.findLastVisibleItemPosition() == mLinearLayoutManager.findFirstVisibleItemPosition()) {
                    //当翻页后，清空保存的已输入的评论
                    if(inputDialog != null) inputDialog.clearHistoryInputText();

                    //获取当前题目的位置
                    mPracticeManager.mCurrentPosition = mLinearLayoutManager.findFirstVisibleItemPosition();
                    QuestionBean questionBean = getQuestionByPosition(mPracticeManager.mCurrentPosition);
                    LogUtil.d("dddd", questionBean);

                    //设置当前题目的题目类型（单选、判断、问答等）
                    binding.questionTypeView.setText(questionBean.getTopic_type_name());
                    //刷新当前的作答进度
                    refreshCurrentPosition();
                    //刷新当前题目的收藏状态
                    refreshCollStatus();
                    //可能AI解析还在请求中，滑到这里的时候检测一下是否需要重新执行动画
                    if(questionBean.getAiStatus() == QuestionBean.AI_LOADING){
                        lookAISuccess(mPracticeManager.mCurrentPosition);
                    }

                    if(binding.questionRecyclerView.getChildCount() > 0) {
                        RecyclerView itemRecyclerView = (RecyclerView) binding.questionRecyclerView.getChildAt(0);
                        if(!(itemRecyclerView.getChildAt(0) instanceof QuestionView)){
                            binding.toTopView.setVisibility(View.VISIBLE);
                        }else {
                            binding.toTopView.setVisibility(View.GONE);
                        }
                    }
                }
            }
        });
    }

    //初始化客服按钮
    public void initCustomerServiceView(){
        binding.customerServiceView.setHideScale(0.65f);
        //用一个动画吸引用户的注意力，这里有个客服按钮
        binding.customerServiceView.initAnimation();
        binding.customerServiceView.setOnClickListener(v -> {
            if (!MainAppUtils.checkLogin(PracticeV3Activity.this)) return;

            WxMinApplication.StartWechat(PracticeV3Activity.this);
        });
        //关闭客服按钮
        binding.closeCustomerView.setOnClickListener(v -> binding.customerServiceView.setVisibility(View.GONE));
    }


    public void initToTopView(){
        //用一个动画吸引用户的注意力，这里有个客服按钮
        binding.toTopView.setOnClickListener(v -> {
            if(binding.questionRecyclerView.getChildCount() > 0) {
                RecyclerView recyclerView = (RecyclerView) binding.questionRecyclerView.getChildAt(0);
                recyclerView.scrollToPosition(0);
                binding.toTopView.setVisibility(View.GONE);
            }
        });
    }

    /**初始化题目列表适配器
     *
     */
    private void initQuestionAdapter() {
        //题目列表点击事件
        PracticeV2ItemAdapter.OnResultListener onResultListener = new PracticeV2ItemAdapter.OnResultListener() {
            @Override
            public void onAnswerResult(CardBean.Topic topicBean, boolean isDoingQuestion) {
                //缓存已做的题
                cacheDoQuestion(topicBean);
                //登录状态下，答题数大于等于30时，上传题目，将已答题目加入到已答题集合中
                if (mCacheRecordData.size() >= MAX_RECORD_COUNT && UserCache.getUserCache() != null) {
                    mPresenter.uploadRecord(mCacheRecordData, MAX_RECORD_COUNT);
                }
                //跳转下一题
                nextQuestion(topicBean, isDoingQuestion);
                //最后一道多选题，点击确认答案自动提交
                voluntarilyCommit(topicBean, isDoingQuestion);
                //当前题库是错题集
                doErrorQuestion(topicBean);
            }

            @Override
            public void onDoCount(CardBean.Topic topicBean, int doCount) {
                if (topicBean.getIs_correct() == Constant.ANSWER_CORRECT)//判断答题是否正确
                    mPracticeManager.mCorrectCount += doCount;//正确数量+1
                else if (topicBean.getIs_correct() == Constant.ANSWER_ERROR)//判断答题是否错误
                    mPracticeManager.mErrorCount += doCount;//错误数量+1
                else if(topicBean.getIs_correct() == Constant.ANSWER_WRITE_HIDE || topicBean.getIs_correct() == Constant.ANSWER_WRITE_SHOW)
                    mPracticeManager.mDoCount += doCount;//已作答但未判断正误的题目数量+1
                else if(topicBean.getIs_correct() == Constant.ANSWER_NONE)
                    mPracticeManager.mDoCount += doCount;//已作答但未判断正误的题目数量-1
                //刷新对错数量
                refreshCorrectErrorCount();
                mDoCount += 1;
                if (mDoCount >= 50){
                    if (MainAppUtils.isFirstLaunchToday(getApplicationContext())){
                        PracticeAdActivity.start(PracticeV3Activity.this);
                    }
                }
            }

            @Override
            public void onReportError(int questionPosition) {
                //显示我要纠错弹窗
                showPracticeErrorDialog(questionPosition);
            }

            @Override
            public void onLookVideoAnalyze(int questionPosition) {
                if(!MainAppUtils.checkLogin(PracticeV3Activity.this)) return;
                mPresenter.lookVideo(questionPosition);
            }

            @Override
            public void onAddComment(int questionPosition) {
                //显示添加评论或恢复的弹窗
                CardBean.Topic topicBean = mCardData.get(mPracticeManager.mCurrentPosition);
                int exams_id = topicBean.getId();
                String mainType = topicBean.getMainType()+"";
                String code = mPracticeManager.mCode;
                showAddCommentDialog(null, -1,"我来解析",exams_id, mainType, code, 0);
            }

            @Override
            public void onGood(int position) {
                //点赞
                mPresenter.goodComment(position);
            }

            @Override
            public void onLoadComment(int questionPosition) {
                //加载更多评论
                mPresenter.getComment(questionPosition);
            }

            @Override
            public void onLoadReply(int ceiling_id, int commentPosition, int page) {
                //查看某条评论下的更多回复
                mPresenter.getChildComment(ceiling_id, commentPosition,page);
            }

            @Override
            public void onReply(TextView commentView, int commentPosition) {
                //举报某个评论或某个回复
                CommentBean.Parses bean = (CommentBean.Parses) getQuestionByPosition(mPracticeManager.mCurrentPosition).getCommentList().get(commentPosition);
                int exams_id = bean.getExams_id();
                String mainType = bean.getMainType();
                String code = mPracticeManager.mCode;
                int parent_id = bean.getId();
                showAddCommentDialog(commentView, commentPosition,String.format("回复 %s", bean.getName()),exams_id, mainType, code, parent_id);
            }

            @Override
            public void onCommentShowMoreOperate(TextView commentView, int touchX, int commentPosition) {
                //长按某个评论或回复，弹出更多操作
                showCommentMoreOperateDialog(commentView, touchX, commentPosition);
            }

            @Override
            public void onAdShowMoreOperate(TextView commentView, int touchX, int commentPosition) {
                //点击信息流广告，弹出更多操作
                showAdMoreOperateDialog(commentView, touchX, commentPosition);
            }

            @Override
            public void onClickAd(int commentPosition) {
                //点击了信息流广告
                AdBean bean = (AdBean) getQuestionByPosition(mPracticeManager.mCurrentPosition).getCommentList().get(commentPosition);
                clickAd(bean);
            }

            @Override
            public void onShowToTop(int visibility) {
                binding.toTopView.setVisibility(visibility);
            }
        };

        RecyclerView.OnScrollListener onScrollListener = new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                //解决长按弹出的评论或长按回复的弹窗，在不松手的情况下继续滑动，导致弹窗没有及时消失的问题
                if(commentPopupWindow != null && dy != 0) {
                    commentPopupWindow.dismiss();
                }
            }
        };

        questionAdapter = new PracticeV2Adapter(mPracticeManager,onResultListener, onScrollListener);
        questionAdapter.setPresenter(mPresenter);
        questionAdapter.setQuestionList(mQuestionData);
        questionAdapter.setCardList(mCardData);
        questionAdapter.refreshData();

        binding.questionRecyclerView.setAdapter(questionAdapter);
    }

    private void showGuideLayout(){
        //显示引导
        if (!KVHelper.getBoolean(UserCache.PRACTICE_SCROLL_GUIDE)) {
            binding.guideLayout.setVisibility(View.VISIBLE);
            binding.guideClose.setOnClickListener(v -> {
                KVHelper.putValue(UserCache.PRACTICE_SCROLL_GUIDE, true);
                binding.guideLayout.setVisibility(View.GONE);
            });
        }
    }

    @Override
    public View initLoadSir() {
        return binding.questionRecyclerView;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        //未登录状态，不使用长链接
        if(UserCache.getUserCache() != null && mPracticeManager.mPracticeType != PracticeHelper.PRACTICE_DAYCARD) {
            socketConnect = new WebSocketConnect();
            socketConnect.create(mPresenter.getCode(), mPracticeManager.mMainType, mPracticeManager.mTopicType);
        }
    }

    @Override
    public void onRequest() {
        //免费题库、历年真题、章节训练、VIP题库可以请求广告
        if(Constant.ZK .equals(UserCache.getAppType())
                && mPresenter.getWxGroupBean() == null
                && mPracticeManager.mPracticeType != PracticeHelper.PRACTICE_DAYCARD
                && !TimeFormatUtils.isToday(KVHelper.getLong(mPracticeManager.mCode+mPracticeManager.mPracticeType))) {
            mPresenter.getTopWxGroup(mPresenter.getCode(), mPracticeManager.mMainType);
        }

        checkHasUpdateMainType();
    }

    public void checkHasUpdateMainType(){
        long version = RealmHelper.queryVersion(mPracticeManager);
        if(!AppUtil.isEmpty(PracticeManager.mCardData) && !AppUtil.isEmpty(PracticeManager.mTotalData)){
            //从前一个页面有传递数据，则使用传递过来的，如：查看答题解析或搜题
            mCardData.addAll(PracticeManager.mCardData);
            mPresenter.cardGroup();
            getQuestionSuccess(PracticeManager.mTotalData);

        }else if(version == -1){//-1没有获取过
            showLoadingView(getClass().getName());
            if(mPracticeManager.isErrorPractice() || mPracticeManager.isCollPractice()){
                mPresenter.getErrorOrCollQuestion();

            }else {
                mPresenter.getAllQuestion();
            }

        }else if(mPracticeManager.version > version && !mPracticeManager.isErrorPractice() && !mPracticeManager.isCollPractice()) {
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent("是否更新题库？")
                    .setNegativeText("继续答题")
                    .setNegativeClickListener(v -> mPresenter.getAnswerHistory(false))
                    .setPositiveText("更新")
                    .setPositiveClickListener(v -> mPresenter.updateQuestion(version))
                    .builder()
                    .show();
        }else {
            //mPresenter.getLocalCardHistory();
            mPresenter.getAnswerHistory(false);
        }
    }


    //通过隐藏控件的操作使得问答题的输入框失去焦点，从而触发焦点监听事件，实现保存的效果
    public void clearInputAnswerViewFocus(){
        if(questionAdapter.getItemCount() > 0 && binding.questionRecyclerView.getLayoutManager().getChildCount() > 0) {
            binding.questionRecyclerView.getLayoutManager().getChildAt(0).setVisibility(View.GONE);
            binding.questionRecyclerView.getLayoutManager().getChildAt(0).setVisibility(View.VISIBLE);
        }
    }

    //跳转下一题
    public void nextQuestion(CardBean.Topic topicBean, boolean isSelected){
        //不是仅选中（仅选中是指：例如模拟模式的单选、判断、多选题，例如答题模式的多选），再判断是否跳转下一题
        if (!isSelected && mPresenter.isCanNext(topicBean)) {
            //模拟模式不需要太长的延迟时间，因为不需要看对错，答题模式要给用户400毫秒查看是否答对
            int delay = mPracticeManager.isSumi() ? 100 : 400;
            new Handler().postDelayed(() -> {
                //因为400毫秒内，用户可能返回上一个页面，所以要判空
                if(binding.questionRecyclerView != null) {
                    binding.questionRecyclerView.smoothScrollToPosition(mPracticeManager.mCurrentPosition + 1);
                }
            }, delay);
        }
    }

    //最后一道多选题，点击确认答案自动提交
    public void voluntarilyCommit(CardBean.Topic topicBean, boolean isSelected){
        if(mPracticeManager.isSumi()//是模拟模式
                && mPracticeManager.mTotalCount == (1 + mPracticeManager.mCurrentPosition)//是最后一道题
                && !isSelected//不是仅选中
                && topicBean.getTopic_type() == PracticeHelper.TYPE_MULTI){//是多选题
            showCommitDialog();
        }
    }

    //错题集
    public void doErrorQuestion(CardBean.Topic topicBean){
        if (mPracticeManager.mPracticeType == PracticeHelper.PRACTICE_ERROR) {
            //错题自动移除设置提示
            if (!KVHelper.getBoolean(UserCache.PRACTICE_ERROR_REMOVE_HINT)) {
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent("自动移除回答正确的题目功能已开启，下次进入错题时将自动移除")
                        .setNegativeText("去关闭")
                        .setNegativeClickListener(v -> {
                            KVHelper.putValue(UserCache.PRACTICE_ERROR_REMOVE_HINT, true);
                            showPracticeSettingDialog();
                        })
                        .setPositiveText("好的")
                        .setPositiveClickListener(v -> KVHelper.putValue(UserCache.PRACTICE_ERROR_REMOVE_HINT, true))
                        .builder()
                        .show();
            }

            //自动移除错题
            if (mPracticeManager.autoRemoveError && topicBean.getIs_correct() == Constant.ANSWER_CORRECT) {
                mPresenter.deleteError(false);
                RealmHelper.deleteErrorQuestion(mPracticeManager, topicBean.getId())
                        .subscribe(new ResultListener<Boolean>() {
                            @Override
                            public void onNext(Boolean aBoolean) {
                                LogUtil.d("dddd", "移除错题"+aBoolean);
                            }
                        });
            }
        }
    }

    public void pauseAudio(){
        //将所有音频都停掉，防止有遗漏
        for (int i = 0; i < binding.questionRecyclerView.getChildCount(); i++){
            View view = binding.questionRecyclerView.getChildAt(i);
            PracticeV2Adapter.Holder holder = (PracticeV2Adapter.Holder) binding.questionRecyclerView.getChildViewHolder(view);
            questionAdapter.notifyPauseAudio(holder);
        }
    }
    /**缓存已做的题目
     * @param topicBean  当前题目对应答题卡中的对象
     */
    public void cacheDoQuestion(CardBean.Topic topicBean){
        //答题模式下，没有展示过答案的，都认为是不需要提交答题记录的
        if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_DEFAULT){
            if(topicBean.getIs_correct() == Constant.ANSWER_WRITE_HIDE){
                return;
            }
        }

        //如果已经存在这样一个题目，则直接修改这个题目，修改完后直接结束方法，防止多次添加到mCacheRecordData集合中
        for(PracticeRecordBean.RecordBean recordBean : mCacheRecordData) {
            if(recordBean.getTopic_id() == topicBean.getId()){
                recordBean.setIs_correct(topicBean.getIs_correct());
                recordBean.setSelect_answer(topicBean.getSelect_answer());
                return;
            }
        }
        mCacheRecordData.add(new PracticeRecordBean.RecordBean(topicBean.getId(), topicBean.getTopic_type(), topicBean.getIs_correct(), topicBean.getSelect_answer(), topicBean.getCorrectOption(), topicBean.getMainType()));

        if(!mPracticeManager.isErrorPractice() && !mPracticeManager.isCollPractice()) {
            RealmHelper.insertHistory(mPracticeManager, mCacheRecordData, topicBean.getId(), 0);

        }
    }

    /**点击了顶部的广告，或者评论中的广告
     * @param bean
     */
    public void clickAd(AdBean bean){
        if(TextUtils.isEmpty(bean.getXcx_path())){
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(this)) return;
            HtmlActivity.start(PracticeV3Activity.this, bean.getTarget_url());

        }else {
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(this)) return;
            new UmengEventUtils(this)
                    .addParams("jump_time", TimeFormatUtils.formatChinese(System.currentTimeMillis()/1000))
                    .addParams("source_page", "做题顶部广告 - 微信群引导页")
                    .pushEvent(UmengEventUtils.ENTER_JUMP_TO_WX_MINIPROGRAM);
            WxMinApplication.StartWechat(PracticeV3Activity.this, bean.getXcx_path(), bean.getTarget_url(), false);
        }
    }

    @Override
    public void onClick(View view) {
        //清除输入框的焦点，触发保存作答
        clearInputAnswerViewFocus();
        //没有题目的时候，底部的按钮不响应事件
        if(view != binding.advertisementView && view != binding.closeView && AppUtil.isEmpty(mCardData)){
            return;
        }

        if(view == binding.advertisementView){//点击顶部加入微信群的广告
            if(!MainAppUtils.checkLogin(this)) return;
            clickAd(mPresenter.getWxGroupBean());

        }else if(view == binding.closeView){//关闭顶部加入微信群的广告
            binding.advertisementView.setVisibility(View.GONE);
            binding.closeView.setVisibility(View.GONE);
            KVHelper.putValue(mPracticeManager.mCode+mPracticeManager.mPracticeType, System.currentTimeMillis());

        }else if(view == binding.questionTypeView){//切换题型
            //埋点统计切换题型的使用次数
            new UmengEventUtils(this)
                    .pushEvent(UmengEventUtils.CLICK_TOPIC_SELECT);

            selectQuestionType();

        }else if(view == binding.collectionView){//收藏当前题目
            if(!MainAppUtils.checkLogin(this)) return;
            if(mQuestionData.size() <= mPracticeManager.mCurrentPosition) return;

            QuestionBean questionBean = getQuestionByPosition(mPracticeManager.mCurrentPosition);
            int collStatus = questionBean.getIsCollection();
            questionBean.setIsCollection(collStatus == 1 ? 0 : 1);
            refreshCollStatus();
            if(questionBean.getIsCollection() == 1) {
                mPresenter.collectionQuestion(1);
                RealmHelper.insertCollQuestion(mPracticeManager, Collections.singletonList(questionBean), false)
                        .subscribe(new ResultListener<Boolean>() {
                            @Override
                            public void onNext(Boolean aBoolean) {
                                LogUtil.d("dddd", "加入收藏"+aBoolean);
                            }
                        });

            }else {
                mPresenter.collectionQuestion(0);
                RealmHelper.cancelCollQuestion(mPracticeManager, questionBean)
                        .subscribe(new ResultListener<Boolean>() {
                            @Override
                            public void onNext(Boolean aBoolean) {
                                LogUtil.d("dddd", "取消收藏"+aBoolean);
                            }
                        });
            }

        }else if(view == binding.cardView){//显示答题卡
            showCardDialog();

        }else if(view == binding.deleteView){//从错题集中删除当前题目
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent("是否删除该错题？")
                    .setNegativeText("否")
                    .setPositiveText("是")
                    .setPositiveClickListener(v -> {
                        mPresenter.deleteError(true);
                        QuestionBean bean = getQuestionByPosition(mPracticeManager.mCurrentPosition);
                        RealmHelper.deleteErrorQuestion(mPracticeManager, bean.getId())
                                .subscribe(new ResultListener<Boolean>() {
                                    @Override
                                    public void onNext(Boolean aBoolean) {
                                        showMessage("移除错题成功");
                                    }
                                });
                    })
                    .builder()
                    .show();

        }else if(view == binding.commitView){//交卷
            showCommitDialog();

        }else if(view == binding.clearView){//清除答题记录
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent("是否清除答题记录？")
                    .setNegativeText("取消")
                    .setPositiveText("清除")
                    .setPositiveClickListener(v -> clearHistory())
                    .builder()
                    .show();

        }else if(view == binding.settingView){//设置
            showPracticeSettingDialog();
        }
    }

    //替换题型
    private void selectQuestionType(){
        if(AppUtil.isEmpty(topicHeadList)) return;
        if(typePopupDialog == null) {
            typePopupDialog = new PopupDialog.Builder<>(this, topicHeadList)
                    .setForeachData((itemView, topicHead, position) -> {
                        String itemContent = String.format(String.format("%s(%s题)", topicHead.getTopic_type_name(), topicHead.getCount()));
                        int start = topicHead.getTopic_type_name().length();
                        int end = itemContent.length();
                        int color = ResourcesCompat.getColor(getResources(), R.color.tblack3, getTheme());
                        ((TextView)itemView).setText(SpannableUtil.setColorString(itemContent, start, end, color));
                    })
                    .setPopupMaxHeight(DisplayHelper.dp2px(this, 32) * 8)//弹窗的每项高度默认为32dp
                    .setOnItemClickListener((itemView, topicHead, position) -> {
                        int currentPosition = 0;
                        for (int i = 0; i < position; i++) {
                            currentPosition += topicHeadList.get(i).getCount();
                        }
                        binding.questionRecyclerView.scrollToPosition(currentPosition);
                    })
                    .build();
        }
        typePopupDialog.showAsDropDown(binding.questionTypeView);
    }


    @Override
    public void lookAISuccess(int position) {
        View view = binding.questionRecyclerView.getChildAt(0);
        PracticeV2Adapter.Holder holder = (PracticeV2Adapter.Holder) binding.questionRecyclerView.getChildViewHolder(view);
        if(holder.getAbsoluteAdapterPosition() != position) {
            questionAdapter.notifyItemChanged(position);
        }else {
            questionAdapter.notifyAIAnalyze(holder);
        }
    }

    @Override
    public void lookVideoSuccess(int position, PracticeVideoBean videoBean) {
        View view = binding.questionRecyclerView.getChildAt(0);
        PracticeV2Adapter.Holder holder = (PracticeV2Adapter.Holder) binding.questionRecyclerView.getChildViewHolder(view);
        if(holder.getAbsoluteAdapterPosition() != position) {
            questionAdapter.notifyItemChanged(position);
        }else {
            questionAdapter.notifyVideoAnalyze(holder, videoBean);
        }
    }

    @Override
    public void showOpenBiguoVipDialog() {
        new MessageDialog.Builder(getSupportFragmentManager())
                .setContent("很抱歉，您今日的AI解析次数已用完，立即开通笔果折扣卡，畅享AI解析服务！")
                .setNegativeText("取消")
                .setPositiveText("开通")
                .setPositiveClickListener(v -> ArmsUtils.startActivity(BiguoVipOpenActivity.class))
                .builder()
                .show();
    }

    @Override
    public void showAi() {
        mPracticeManager.isShowAI = true;
        questionAdapter.notifyItemChanged(mPracticeManager.mCurrentPosition);
    }

    @Override
    public QuestionBean getQuestionByPosition(int position) {
        //获取指定下标的题目
        return questionAdapter.getItemBean(position);
    }

    @Override
    public void getQuestionSuccess(List<QuestionBean> data) {
        //加载成功
        showSuccessView();
        //显示手势引导图
        showGuideLayout();

        //获取数据成功，则保存到属性中
        if(mQuestionData != data) {
            mQuestionData.clear();
            mQuestionData.addAll(data);
        }

        //设置当前题目的题目类型（单选、判断、问答等）
        if(!AppUtil.isEmpty(mQuestionData)) {
            binding.questionTypeView.setText(mQuestionData.get(0).getTopic_type_name());
        }

        //设置题目总数量
        mPracticeManager.mTotalCount = data.size();
        refreshCurrentPosition();

        //统计做题结果，并返回上次答题的位置
        int lastAnswerIndex = statisticsAndReturnLastAnswerIndex();

        //跳转到上次答题的位置
        if(mPracticeManager.isDefaultMode()){
            //当没有指定endId的时候，则跳到指定题型的第一题，主要是成考、教资还保留题型列表，进入答题页面获取的不是指定题型的题库，而是所有题型的题库
            int topicTypeIndex = 0;//记录要跳过多少道题
            for(CardBean.TopicHead head : topicHeadList){
                if(head.getTopic_type() == mPracticeManager.mTopicType) break;

                topicTypeIndex += head.getCount();
            }
            //当要跳过所有题目时，认为没有匹配的题型，则默认跳到第一题
            if(topicTypeIndex == mCardData.size()){
                topicTypeIndex = 0;
            }

            if(lastAnswerIndex != -1
                    && lastAnswerIndex != mPracticeManager.mCurrentPosition
                    && mCardData.get(lastAnswerIndex).getTopic_type() == mCardData.get(topicTypeIndex).getTopic_type()){
                //答题模式或背题模式需要询问之后，才决定是否直接跳到上次答题的位置
                showRestoreHistoryDialog(lastAnswerIndex);
            }else {
                //没有答题记录，则默认滚动到指定题型的第一题
                scrollToPosition(topicTypeIndex);
            }

        }else if (lastAnswerIndex != -1 && (mPracticeManager.isDefaultMode() || mPracticeManager.isShowMode())) {
            //答题模式或背题模式需要询问之后，才决定是否直接跳到上次答题的位置
            showRestoreHistoryDialog(lastAnswerIndex);

        }else if(lastAnswerIndex != -1 && (mPracticeManager.isAllMode())){
            //解析模式是交卷之后的才能进入的页面，可以指定跳转到第几题，在进入该页面时使用mPracticeManager.endId变量传递的
            scrollToPosition(lastAnswerIndex);
        }
        //刷新当前题目的UI和数据
        questionAdapter.refreshData();
        //刷新作答数量
        refreshCorrectErrorCount();
        //设置收藏状态
        refreshCollStatus();
        //模拟模式开始计时
        if (mPracticeManager.isSumi() || mPracticeManager.isDayCard()) {
            mPresenter.startTimer();
        }

    }

    @Override
    public void checkToUpdateErrorOrColl(List<QuestionBean> data){
        getQuestionSuccess(data);
    }

    @Override
    public void getErrorOrCollQuestionFail() {
        mPracticeManager.mTotalCount = questionAdapter.getItemCount();
    }

    /**滚动到指定题目
     * @param lastAnswerIndex
     */
    public void scrollToPosition(int lastAnswerIndex){
        mPracticeManager.mCurrentPosition = lastAnswerIndex;
        refreshCurrentPosition();
        binding.questionRecyclerView.scrollToPosition(mPracticeManager.mCurrentPosition);
    }

    /**显示恢复答题记录的弹窗
     *
     */
    public void showRestoreHistoryDialog(int lastAnswerIndex){
        //如果需要跳转的位置就是当前的题目，则不提示恢复答题
        if(lastAnswerIndex == mPracticeManager.mCurrentPosition){
            return;
        }
        scrollToPosition(lastAnswerIndex);
        showMessage("已为您跳转到上次答题位置");
    }


    /**
     * 统计做题结果，并返回上次答题的位置
     */
    public int statisticsAndReturnLastAnswerIndex(){
        mPracticeManager.mCorrectCount = 0;//答对数量
        mPracticeManager.mErrorCount = 0;//答错数量
        mPracticeManager.mDoCount = 0;//已作答但未判断正误的题目数量

        int lastAnswerIndex = -1;
        for (int i = 0; i < mCardData.size(); i++) {
            CardBean.Topic bean = mCardData.get(i);
            //重新统计作答数量
            if (bean.getIs_correct() == Constant.ANSWER_CORRECT) {
                mPracticeManager.mCorrectCount += 1;
            } else if (bean.getIs_correct() == Constant.ANSWER_ERROR) {
                mPracticeManager.mErrorCount += 1;
            }else if(bean.getIs_correct() == Constant.ANSWER_WRITE_HIDE || bean.getIs_correct() == Constant.ANSWER_WRITE_SHOW){
                mPracticeManager.mDoCount += 1;
            }

            //有做题记录，并且已找到这道题
            //解析模式是交卷之后的才能进入的页面，可以指定跳转到第几题，在进入该页面时使用mPracticeManager.endId变量传递的
            if(mPracticeManager.endId > 0 && mPracticeManager.endId == bean.getId()){
                lastAnswerIndex = i;
                //清空记录，防止加载更多题目时又跳转回来
                mPracticeManager.endId = -1;
            }
        }
        //记录到上次是在第一题退出，并且没有作答题目，则不显示恢复答题
        if((lastAnswerIndex + mPracticeManager.mCorrectCount + mPracticeManager.mErrorCount + mPracticeManager.mDoCount) == 0){
            lastAnswerIndex = -1;
        }

        return lastAnswerIndex;
    }

    /**
     * 清除答题记录
     */
    public void clearHistory(){
        try {
            //清空答题卡的答题记录
            for(CardBean.Topic topic : mCardData){
                topic.setSelect_answer("");
                topic.setIs_correct(Constant.ANSWER_NONE);
            }
            //清除作答，避免退出页面时弹出保存的弹窗
            mCacheRecordData.clear();
            questionAdapter.refreshData();
            //滚动到第一题
            binding.questionRecyclerView.scrollToPosition(0);
            //登录状态下才可以清除服务器和本地数据库的记录
            if (UserCache.getUserCache() != null && !mPracticeManager.isDayCard()
                    && !mPracticeManager.isCollPractice()
                    && !mPracticeManager.isErrorPractice()) {
                //修改题库的LastID
                RealmHelper.clearHistory(mPracticeManager)
                        .subscribe(new ResultListener<Boolean>() {
                            @Override
                            public void onNext(Boolean aBoolean) {
                                LogUtil.d("dddd", "清除答题记录"+aBoolean);
                            }
                        });
                //向服务器发起清除答题卡数据的请求
                mPresenter.clearCardHistory();
                //重置对错数量
                mPracticeManager.mCorrectCount = 0;
                mPracticeManager.mErrorCount = 0;
                mPracticeManager.mDoCount = 0;
                refreshCorrectErrorCount();
            }
        }catch (Exception e){
            showMessage("清除答题失败");
        }
    }

    /**
     * 显示提交提示弹窗
     */
    private void showCommitDialog() {
        if (PracticeHelper.isCheckFinish(mPracticeManager.mPracticeType) && !mPresenter.checkAnswerFinish(false)) {
            showMessage("请完成答题后交卷");
            return;
        }

        //答对数量+答错数量+已写数量 等于题目的总数量，则认为所有题目都已作答，没有更改答案的可能，所以直接提交。
        //模拟模式还能做修改，因此加个询问弹窗
        if(mPracticeManager.mCorrectCount + mPracticeManager.mErrorCount + mPracticeManager.mDoCount < mCardData.size()) {
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent(mPresenter.checkAnswerFinish(true) ? "确认交卷吗？" : "您仍有题目未作答，确认交卷吗？")
                    .setNegativeText("点错了")
                    .setPositiveText("交卷")
                    .setPositiveClickListener(v -> {
                        isUploadSaveData = true;
                        mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_COMMIT);
                    })
                    .builder()
                    .show();
        }else {
            isUploadSaveData = true;
            mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_COMMIT);
        }
    }

    //显示答题卡
    private void showCardDialog() {
        if (mPracticeCardDialog == null) {
            //设置交卷按钮的显示和隐藏
            int showCommitViewVisibility = mPracticeManager.isDefaultMode() || mPracticeManager.isSumi()
                    ? View.VISIBLE
                    : View.GONE;

            mPracticeCardDialog = new PracticeCardDialog(this, mCardData, mPracticeManager);
            mPracticeCardDialog.setEnableCommit(showCommitViewVisibility);
            mPracticeCardDialog.setOnCommitListener(new PracticeCardDialog.OnOperateListener() {
                @Override
                public void onItemClick(int position) {
                    binding.questionRecyclerView.scrollToPosition(position);
                }

                @Override
                public void onCommit() {
                    //清除输入框的焦点，触发保存作答
                    clearInputAnswerViewFocus();
                    if (PracticeHelper.isCheckFinish(mPracticeManager.mPracticeType) && !mPresenter.checkAnswerFinish(false)) {
                        showMessage("请完成答题后交卷");
                        return;
                    }
                    isUploadSaveData = true;
                    mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_COMMIT);
                }

                @Override
                public void onSynchronize() {
                    mPracticeCardDialog.dismiss();
                    if(mPracticeManager.isCollPractice() || mPracticeManager.isErrorPractice()){
                        mPresenter.getErrorOrCollQuestion();
                    }else {
                        mPresenter.getAnswerHistory(true);
                    }
                }

                @Override
                public void onClearErrorQuestion() {
                    //TODO 该功能待开发
                }
            });
        }

        mPracticeCardDialog.group();
        mPracticeCardDialog.show(mPracticeManager.mCurrentPosition);
    }

    /**刷新对错数量
     */
    private void refreshCorrectErrorCount() {
        if (!mPracticeManager.mIsShowCorrectErrorCount) return;
        String text = String.format("对%s/错%s", mPracticeManager.mCorrectCount, mPracticeManager.mErrorCount);
        SpannableString spannableString = new SpannableString(text);
        //答对数量
        int correctColor = getResources().getColor(R.color.option_correct_text);
        int correctStart = text.indexOf("/");
        spannableString.setSpan(new ForegroundColorSpan(correctColor), 0, correctStart, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        //答错数量
        int errorColor = getResources().getColor(R.color.option_error_text);
        int errorStart = text.indexOf("/")+1;
        spannableString.setSpan(new ForegroundColorSpan(errorColor), errorStart, text.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        binding.practiceCountView.setText(spannableString);
    }

    /**显示纠错弹窗
     * @param position
     */
    private void showPracticeErrorDialog(int position) {
        if(!MainAppUtils.checkLogin(this)) return;

        if (mPracticeErrorDialog == null) {
            mPracticeErrorDialog = new PracticeErrorDialog(this);
            mPracticeErrorDialog.setPath(mPresenter.getErrorOpinionPath(position, ""));
            mPracticeErrorDialog.setOnErrorSubmitListener((requestPosition,message, detail) -> {
                mPresenter.errorOpinion(requestPosition,message, detail);
            });
        }
        mPracticeErrorDialog.setPosition(position);
        mPracticeErrorDialog.show();
    }

    @Override
    public void uploadSuccess(int uploadType, boolean isCommitSuccess, List<PracticeRecordBean.RecordBean> recordData) {
        EventBus.getDefault().post(new PracticeResultBean(), EventBusTags.PRACTICE_COMMIT);

        if(!AppUtil.isEmpty(recordData)){
            //交卷成功，才去判断答案正确性
            if(isCommitSuccess) {
                mPresenter.contrastAnswer(recordData);
            }

            //将上传的答题记录改为已上传状态
            int lastId = recordData.get(recordData.size() - 1).getTopic_id();
            if (!mPracticeManager.isDayCard()) {
                RealmHelper.insertHistory(mPracticeManager, recordData, lastId, isCommitSuccess ? 1 : 0);
            }
        }

        //正常上传答题记录
        if (uploadType == MAX_RECORD_COUNT || uploadType == UPLOAD_TYPE_NORMAL) {
            if(!AppUtil.isEmpty(mCacheRecordData)) {
                //将已上传的题目移除，在上传过程中，可能用户又有答题，调用mCacheRecordData.clear()可能会导致新做的题目记录丢失
                for(PracticeRecordBean.RecordBean recordBean : recordData) {
                    mCacheRecordData.remove(recordBean);
                }
            }
        }
        //关闭页面
        else if (uploadType == UPLOAD_TYPE_CLOSE) {
            if(mCacheRecordData!=null) mCacheRecordData.clear();
            finish();
        }
        //交卷
        else if (uploadType == UPLOAD_TYPE_COMMIT) {
            //将上传的答题记录改为已上传状态
            if(mCacheRecordData != null) mCacheRecordData.clear();

            PracticeManager.mTotalData = mQuestionData;
            PracticeManager.mCardData = mCardData;
            PracticeManager.questionMap = questionAdapter.getQuestionMap();
            PracticeResultV2Activity.start(this, mPracticeManager);
            finish();
        }
    }

    @Override
    public void errorOpinionSuccess() {
        if (mPracticeErrorDialog != null)
            mPracticeErrorDialog.clear();
    }

    /**分享好友
     *
     */
    private void showRealQuestionShareDialog() {
        if(mCardData.size() <= mPracticeManager.mCurrentPosition) return;
        CardBean.Topic topic = mCardData.get(mPracticeManager.mCurrentPosition);
        String params = "mainType=" + topic.getMainType()
                + "&topic_id=" + topic.getId()
                + "&version=" + mPracticeManager.version;

        new ShareDialog.Builder(getContext())
                .setShareTitle("求助！这道题挺难的，你能帮我看看吗？")
                .setShareContent("笔果题库海量试题在线学习，高效提分，点击进入体验一下吧")
                .setShareIcon(R.drawable.app_icon)
                .setShareUrl(Constant.SHARE_QUESTION + "?" + UrlAddParamUtil.encode(params))
                .setOnShareListener((type) -> {
                    new UmengEventUtils(PracticeV3Activity.this)
                            .addParams("path", "题目详情：做题-》右上角分享")
                            .addParams("platform", type)
                            .pushEvent(UmengEventUtils.CLICK_SHARE_APP);

                })
                .builder()
                .show();

    }

    /**长按评论或长按回复的弹窗
     * @param commentView 评论或回复的所在的控件
     * @param touchX 手指在屏幕范围内的横坐标
     * @param commentPosition 评论的下标
     */
    public void showCommentMoreOperateDialog(TextView commentView, int touchX, int commentPosition){
        CommentBean.Parses commentBean = (CommentBean.Parses) questionAdapter.getItemBean(mPracticeManager.mCurrentPosition).getCommentList().get(commentPosition);
        List<PracticeCommentOperateBean> list = new ArrayList<>();
        list.add(new PracticeCommentOperateBean(R.drawable.comment_reply, "回复", "reply"));
        list.add(new PracticeCommentOperateBean(R.drawable.comment_copy, "复制", "copy"));
        if(commentBean.getDelable() == StartFinal.YES){
            list.add(new PracticeCommentOperateBean(R.drawable.comment_delete, "删除", "delete"));
        }else {
            list.add(new PracticeCommentOperateBean(R.drawable.comment_report, "举报", "report"));
        }

        int[] commentLocation = new int[2];
        commentView.getLocationOnScreen(commentLocation);

        int arrowOffsetX = Math.min(touchX - commentLocation[0], commentView.getWidth()) - DisplayHelper.dp2px(this, 11);
        int dialogOffsetY = -(DisplayHelper.dp2px(this, 68) + commentView.getHeight());

        commentPopupWindow = new PopupDialog.Builder<>(this, list)
                .setForeachData((itemView, data, position) -> {
                    TextView view = (TextView)itemView;
                    view.setText(data.getText());
                    int iconColor = ResourcesCompat.getColor(getResources(), R.color.tblack, getTheme());
                    TextDrawableLoader.loadTop(itemView.getContext(), view, data.getIcon(), iconColor);
                })
                .setOrientation(LinearLayoutCompat.HORIZONTAL)
                .setArrowOffsetX(arrowOffsetX)
                .setOnItemClickListener((itemView, data, position) -> {
                    if(TextUtils.equals("reply", data.getType())){//回复
                        replyComment(commentView, commentPosition);

                    }else if(TextUtils.equals("copy", data.getType())){//复制
                        copyComment(commentView, commentBean.getContent());

                    }else if(TextUtils.equals("delete", data.getType())){//删除
                        deleteComment(commentPosition);

                    }else if(TextUtils.equals("report", data.getType())){//举报
                        reportComment(commentBean.getId());
                    }
                })
                .build();
        commentPopupWindow.setOnDismissListener(() -> commentView.setBackground(null));
        commentPopupWindow.showAsDropDown(commentView, 0, dialogOffsetY);
        commentView.setBackgroundResource(R.color.theme_alpha_10);
    }

    //回复评论
    public void replyComment(TextView commentView, int commentPosition){
        CommentBean.Parses bean = (CommentBean.Parses) getQuestionByPosition(mPracticeManager.mCurrentPosition).getCommentList().get(commentPosition);
        int exams_id = bean.getExams_id();
        String mainType = bean.getMainType();
        String code = mPracticeManager.mCode;
        int parent_id = bean.getId();
        showAddCommentDialog(commentView, commentPosition,String.format("回复 %s", bean.getName()),exams_id, mainType, code, parent_id);
    }

    //删除自己发布的评论
    public void deleteComment(int commentPosition){
        mPresenter.deleteReplyOrComment(commentPosition);
    }

    //复制评论
    public void copyComment(TextView commentView, String content){
        AppUtil.copyText(commentView.getContext(), content);
        showMessage("复制成功");
    }

    //举报评论
    public void reportComment(int commentId){
        if(!MainAppUtils.checkLogin(PracticeV3Activity.this)) return;
        CommentReportActivity.start(PracticeV3Activity.this, commentId, mPracticeManager.mCode);
    }

    /**点击信息流广告的下拉按钮更多操作的弹窗
     * @param downView 信息流广告的下拉按钮
     * @param touchX 手指在屏幕范围内的横坐标
     * @param adPosition 评论的下标
     */
    public void showAdMoreOperateDialog(TextView downView, int touchX, int adPosition){
        int[] commentLocation = new int[2];
        downView.getLocationOnScreen(commentLocation);
        int arrowOffsetX = Math.min(touchX - commentLocation[0], downView.getWidth()) - DisplayHelper.dp2px(this, 11);
        int dialogOffsetY = -(DisplayHelper.dp2px(this, 68) + downView.getHeight());

        PopupDialog<String> popupDialog = new PopupDialog.Builder<>(this, "关闭此条广告")
                .setForeachData((itemView, data, position) -> {
                    TextView view = (TextView)itemView;
                    view.setText(data);
                    TextDrawableLoader.loadLeft(itemView.getContext(), view, R.drawable.close_ad);
                })
                .setArrowOffsetX(arrowOffsetX)
                .setOnItemClickListener((itemView, data, position) -> {
                    QuestionBean questionBean = getQuestionByPosition(mPracticeManager.mCurrentPosition);
                    List<Object> comment = questionBean.getCommentList();
                    comment.remove(adPosition);
                    refreshItemComment(mPracticeManager.mCurrentPosition, adPosition, 1, DELETE_COMMENT);
                    //本次答题不再显示信息流广告，但下一次进入答题页面还会再显示
                    mPresenter.setCommentAdBean(null);
                })
                .build();
        popupDialog.showAsDropDown(downView, -DisplayHelper.dp2px(this, 6), dialogOffsetY);
    }

    /**添加解析或回复的弹窗
     * @param commentView 评论或回复的所在的控件，解析传递null
     * @param commentPosition 评论的下标，解析传递-1
     * @param hint 输入框中的提示
     * @param exams_id 题目的ID
     * @param mainType 题目类型
     * @param code 题目代码
     * @param parent_id 回复的目标ID，解析传递0
     */
    public void showAddCommentDialog(TextView commentView, int commentPosition, String hint, int exams_id, String mainType, String code, int parent_id){
        if(inputDialog == null) {
            inputDialog = new PracticeInputDialog(PracticeV3Activity.this, (content, commentPosition1, exams_id1, parent_id1) -> {
                if (!MainAppUtils.checkLogin(PracticeV3Activity.this)) return;

                mPresenter.addReplyOrComment(commentPosition1, exams_id1, content, mainType, code, parent_id1);
            });
        }
        inputDialog.setExams_id(exams_id);
        inputDialog.setCommentPosition(commentPosition);
        inputDialog.setParent_id(parent_id);
        inputDialog.setTargetView(commentView);
        inputDialog.setHint(hint);
        inputDialog.show();
    }

    /**刷新收藏状态
     *
     */
    private void refreshCollStatus() {
        if(AppUtil.isEmpty(mCardData)) return;

        QuestionBean questionBean = getQuestionByPosition(mPracticeManager.mCurrentPosition);
        boolean isCollection = questionBean != null && questionBean.getIsCollection() == 1;
        if(isCollection){
            TextDrawableLoader.loadTop(this, binding.collectionView, R.drawable.practice_icon_coll_red);
        }else {
            int drawableTint = ResourcesCompat.getColor(getResources(), R.color.tblack, getTheme());
            TextDrawableLoader.loadTop(this, binding.collectionView, R.drawable.practice_icon_coll, drawableTint);
        }
    }

    //显示答题设置
    private void showPracticeSettingDialog() {
        PracticeSettingDialog mPracticeSettingDialog = new PracticeSettingDialog(this, mPracticeManager);
        mPracticeSettingDialog.setOnChangeSizeListener(() -> questionAdapter.refreshData());
        mPracticeSettingDialog.show();
    }

    //刷新当前位置 和 总题数
    private void refreshCurrentPosition() {
        binding.cardView.setText(String.format("%s/%s", (1 + mPracticeManager.mCurrentPosition), mPracticeManager.mTotalCount));
    }

    @Override
    public void refreshItemComment(int questionPosition, int index, int count, int operate) {
        View view = binding.questionRecyclerView.getChildAt(0);
        PracticeV2Adapter.Holder holder = (PracticeV2Adapter.Holder) binding.questionRecyclerView.getChildViewHolder(view);
        if(holder.getAbsoluteAdapterPosition() != questionPosition) {
            questionAdapter.notifyItemChanged(questionPosition);
        }else {
            if (operate == INSERT_COMMENT) {
                //评论或回复插入成功，从输入弹窗的历史记录中移除该条记录
                Object insertObject = getQuestionByPosition(questionPosition).getCommentList().get(index);
                if(inputDialog != null && insertObject instanceof CommentBean.Parses) {
                    CommentBean.Parses insertQuestionBean = (CommentBean.Parses) insertObject;
                    inputDialog.removeHistoryInputText(insertQuestionBean.getParent_id());
                }

                questionAdapter.notifyCommentItemInserted(holder, count, index);
            } else if (operate == DELETE_COMMENT) {
                questionAdapter.notifyCommentItemRemove(holder, count, index);
            }
        }
    }

    @Override
    public void refreshComment(int questionPosition) {
        View view = binding.questionRecyclerView.getChildAt(0);
        PracticeV2Adapter.Holder holder = (PracticeV2Adapter.Holder) binding.questionRecyclerView.getChildViewHolder(view);
        if(holder.getAbsoluteAdapterPosition() != questionPosition) {
            questionAdapter.notifyItemChanged(questionPosition);
        }else {
            questionAdapter.notifyCommentItem(holder);
        }
    }

    @Override
    public void getTopWxGroupSuccess(String url) {
        binding.closeView.setVisibility(View.VISIBLE);
        ImageLoader.loadImageNoPlaceholder(binding.advertisementView, url);
    }

    @Override
    public void downTimer(String time) {
        if(binding != null) {
            binding.downTimeView.setText(time);
        }
    }

    public void showLeaveDialog(){
        //清除输入框的焦点，触发保存作答
        clearInputAnswerViewFocus();
        //已上传答题记录、解析模式、背题模式、错题、收藏、没有题目都直接返回上一个页面
        if(isUploadSaveData
                || mPracticeManager.isAllMode()
                || mPracticeManager.isShowMode()
                || mPracticeManager.isErrorPractice()
                || mPracticeManager.isCollPractice()
                || AppUtil.isEmpty(mCardData)) {
            finish();
            return;
        }

        //每日打卡未答完题，直接离开
        if(mPracticeManager.isDayCard() && !mPresenter.checkAnswerFinish(true)){
            finish();
            return;
        }

        isUploadSaveData = true;
        mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_COMMIT);
        //挽回弹窗,错题、收藏不弹出，因为不可交卷
        /*new OutPracticeDialog(this)
                .setOnLeaveListener(() -> {
                    isUploadSaveData = true;
                    mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_COMMIT);
                }).show();*/
        if (MainAppUtils.isFirstLaunchToday(getApplicationContext())){
            PracticeAdActivity.start(PracticeV3Activity.this);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(KeyEvent.KEYCODE_BACK == keyCode){
            if(!VideoPlayerView.quitFullscreen(this)){
                showLeaveDialog();
            }
            return true;
        }else {
            return super.onKeyDown(keyCode, event);
        }
    }

    long startStudyTime;
    @Override
    protected void onResume() {
        super.onResume();
        startStudyTime = System.currentTimeMillis();

        if(socketConnect != null){
            socketConnect.resume();
        }

    }

    @Override
    protected void onPause() {
        pauseAudio();
        //错题或收藏，不做缓存
        if(!mPracticeManager.isErrorPractice() && !mPracticeManager.isCollPractice()){
            //答模式下或背题模式，记录最后一道题的ID
            if((mPracticeManager.isDefaultMode() || mPracticeManager.isShowMode())
                    && UserCache.getUserCache() != null
                    && mCardData.size() > 0) {
                CardBean.Topic topicBean = mCardData.get(mPracticeManager.mCurrentPosition);
                RealmHelper.insertLastId(mPracticeManager, topicBean.getId());
            }
        }

        if(socketConnect != null) {
            socketConnect.pause();
        }

        long studyDuration = (System.currentTimeMillis() - startStudyTime) / 1000;
        if(studyDuration > 60) {
            Map<String, Object> map = new HashMap<>();
            map.put("answer_time", studyDuration);
            map.put("mainType", mPracticeManager.mMainType);
            map.put("topic_type", mPracticeManager.mTopicType);
            if(mPracticeManager.mPracticeType == PracticeHelper.PRACTICE_TRUE){
                map.put("code", mPracticeManager.mPaperId);
            }else if(mPracticeManager.mPracticeType == PracticeHelper.PRACTICE_CHAPTER){
                map.put("code", mPracticeManager.mChapterSectionId);
            }else if(mPracticeManager.mPracticeType == PracticeHelper.PRACTICE_SIMU){
                map.put("code", mPracticeManager.mTestPaperId);
            }else {
                map.put("code", mPracticeManager.mCode);
            }
            map.put("cert_type", Constant.ZK.equals(UserCache.getAppType()) ? 0 : (Constant.CK.equals(UserCache.getAppType()) ? 1 : 2));
            map.put("professions_id", UserCache.getProfession().getId());
            mPresenter.commitStudyTime(map);
        }

        //防止按HOME键，导致当APP被杀死时候没把数据保存
        if(!isUploadSaveData) {
            mPresenter.uploadRecord(mCacheRecordData, UPLOAD_TYPE_NORMAL);
        }
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if(socketConnect != null) {
            socketConnect.destroy();
        }
        PracticeManager.mCardData = null;
        PracticeManager.mTotalData = null;

        //埋点统计答题模式的使用时长
        uploadPoint();

    }

    /**埋点统计答题模式的使用时长
     *
     */
    public void uploadPoint(){
        //答题模式或背题模式上传使用时长
        if(UserCache.getPracticeMode() == PracticeHelper.MODE_DEFAULT) {
            new UmengEventUtils(this)
                    .addParams("答题模式", System.currentTimeMillis() - enterThisActivityDate)
                    .pushEvent(UmengEventUtils.CLICK_ANSWER_MODE);

        }else if(UserCache.getPracticeMode() == PracticeHelper.MODE_SHOW){
            new UmengEventUtils(this)
                    .addParams("背题模式", System.currentTimeMillis() - enterThisActivityDate)
                    .pushEvent(UmengEventUtils.CLICK_ANSWER_MODE);
        }
        new UmengEventUtils(this)
                .addParams("leave_time", System.currentTimeMillis())
                .addParams("stay_duration", System.currentTimeMillis() - enterThisActivityDate)
                .addParams("entry_path_type", mPracticeManager.mPracticeType)
                .pushEvent(UmengEventUtils.LEAVE_QUESTION_PAGE);

    }

    @Override
    public void killMyself() {
        finish();
    }

    @Override
    public Context getContext() {
        return this;
    }
}