package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.FreeTopicBean;
import com.dep.biguo.bean.TopicBean;
import com.dep.biguo.mvp.contract.FreeTopicContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Action;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class FreeTopicPresenter extends BasePresenter<FreeTopicContract.Model, FreeTopicContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public FreeTopicPresenter(FreeTopicContract.Model model, FreeTopicContract.View rootView) {
        super(model, rootView);
    }

    public void getFreeTopic() {
        mModel.getFreeTopic(1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<FreeTopicBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<FreeTopicBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getFreeTopicSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getFreeTopicSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
