package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.R;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.RewardCollectBean;
import com.dep.biguo.databinding.RewardCollectActivityBinding;
import com.dep.biguo.di.component.DaggerRewardCollectComponent;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.mvp.contract.RewardCollectContract;
import com.dep.biguo.mvp.presenter.RewardCollectPresenter;
import com.dep.biguo.utils.FileUtil;
import com.dep.biguo.utils.imp.TextWatcherImp;
import com.dep.biguo.widget.LayoutUpScrollEditView;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.component.AppComponent;

import java.io.File;
import java.util.List;

public class RewardCollectActivity extends BaseLoadSirActivity<RewardCollectPresenter> implements RewardCollectContract.View, View.OnClickListener {
    private RewardCollectActivityBinding binding;

    private RewardCollectBean rewardCollectBean;
    private ProvinceBean provinceBean;
    private String filePath;

    private ActivityResultLauncher<Intent> launcher;

    public static void start(Context context){
        Intent intent = new Intent(context, RewardCollectActivity.class);
        context.startActivity(intent);
    }
    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerRewardCollectComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.reward_collect_activity);
        binding.setOnClickListener(this);
        new NormalToolbarUtil(this)
                .setCenterText("资料有奖征集")
                .setRightText("上传记录")
                .setRightOnClickListener(v -> RewardCollectHistoryActivity.start(RewardCollectActivity.this));

        //软键盘打开的时候，布局不要顶出屏幕
        KeyboardUtils.setShowKeyboardScroll(this, binding.getRoot(), true);
        //当焦点在备注输入框上，且软键盘打开导致布局有变动
        LayoutUpScrollEditView.OnTouchClickListener onTouchClickListener = () -> {
            //延迟100毫秒等待软键盘打开，才能使得需要执行的代码生效
            new Handler().postDelayed(() -> {
                View view = binding.inputDescView.hasFocus() ? binding.descTitleView : binding.uploadTitleView;
                binding.rootView.smoothScrollTo(0, view.getTop());
            }, 200);
        };
        binding.inputDescView.setOnTouchClickListener(onTouchClickListener);
        binding.inputUploadContentView.setOnTouchClickListener(onTouchClickListener);

        //监听描述输入的字数
        binding.inputDescView.addTextChangedListener(new TextWatcherImp() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                binding.inputCountView.setText(String.format("%s/100", binding.inputDescView.length()));
            }
        });

        //切换上传内容的类型
        binding.uploadTypeLayout.setOnCheckedChangeListener((group, checkedId) -> {
            showUploadContentLayout();
        });
        //xml中默认选择的是文件上传，因此可以直接调用这个方法
        showUploadContentLayout();

        //选择文件的回调
        launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if(result.getData() != null) {
                Uri uri = result.getData().getData();

                filePath = FileUtil.getFileAbsolutePath(RewardCollectActivity.this, uri);
                if(AppUtil.isEmpty(filePath)){
                    showMessage("获取失败，文件不存在");
                    return;
                }

                if(AppUtil.isEmpty(filePath)) return;

                //用选中文件路径创建一个文件对象
                File file = new File(filePath);
                //显示已选中的文件名称
                binding.inputUploadContentView.setText(file.getName());
                //隐藏 显示上传文件的按钮
                binding.uploadFileView.setVisibility(View.GONE);
                //显示 展示文件名称的控件
                binding.uploadContentLayout.setVisibility(View.VISIBLE);
            }
        });

        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void onRequest() {
        mPresenter.getRewardCollectData();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    public void showUploadContentLayout(){
        //删除选中的文件
        deleteSelectFile();
        //清空内容
        binding.inputUploadContentView.setText("");

        if(binding.uploadTypeLayout.getCheckedRadioButtonId() == binding.fileTypeView.getId()){
            //清空内容
            filePath = "";
            //如果文件未上传，则显示上传文件的按钮
            binding.uploadFileView.setVisibility(View.VISIBLE);
            //显示 展示文件名称的控件
            binding.uploadContentLayout.setVisibility(View.GONE);
            //展示文件名称的控件禁止编辑
            binding.inputUploadContentView.setEnabled(false);
            //给 展示文件名称的控件 清空提示语
            binding.inputUploadContentView.setHint("");

        }else {
            //如果文件未上传，则显示上传文件的按钮
            binding.uploadFileView.setVisibility(View.GONE);
            //显示 展示文件名称的控件
            binding.uploadContentLayout.setVisibility(View.VISIBLE);
            //展示文件名称的控件需要可以编辑
            binding.inputUploadContentView.setEnabled(true);
            //给 展示文件名称的控件 设置提示语
            binding.inputUploadContentView.setHint("请输入题库资料链接");
        }
    }

    /**删除已选中的文件，因为Android 10的适配是要复制一份到沙盒里，要保证复制的文件在使用后能删除
     *
     */
    public void deleteSelectFile(){
        //低于Android 10的是直接获取原文件的，不要删除
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            File file = new File(AppUtil.isEmpty(filePath, ""));
            file.delete();
        }
    }

    @Override
    public void onClick(View view) {
        //点击事件需要收起软键盘
        KeyboardUtils.hideKeyboard(binding.inputTitleView);
        KeyboardUtils.hideKeyboard(binding.inputDescView);
        KeyboardUtils.hideKeyboard(binding.inputUploadContentView);

        if(view == binding.inputProvinceView){//选择省份
            mPresenter.getProvince();

        }else if(view == binding.clearView){//删除已选的文件或清空输入的链接
            showUploadContentLayout();

        }else if(view == binding.uploadFileView){//选择文件
            selectFile();

        }else if(view == binding.commitView){//提交
            commit();
        }
    }

    @Override
    public void getRewardCollectDataSuccess(RewardCollectBean bean) {
        rewardCollectBean = bean;

        //规则说明
        StringBuilder builder = new StringBuilder("规则说明\n");
        for(String rule : bean.getRule()){
            builder.append(rule).append("\n");
        }
        builder.delete(builder.length() - 1, builder.length());
        binding.ruleView.setText(builder);
    }


    @Override
    public void showSelectProvinceDialog(List<ProvinceBean> list){
        PopupDialog<ProvinceBean> popupDialog = new PopupDialog.Builder<>(this, list)
                .setPopupMaxHeight(DisplayHelper.dp2px(this, 32) * 10)
                .setForeachData((itemView, data, position) -> {
                    TextView textView = (TextView) itemView;
                    textView.setText(data.getName());
                })
                .setOnItemClickListener((itemView, data, position) -> {
                    provinceBean = data;
                    binding.inputProvinceView.setText(provinceBean.getName());
                })
                .build();
        popupDialog.setWidth(binding.inputProvinceView.getWidth());
        popupDialog.showAsDropDown(binding.inputProvinceView);
    }

    public void selectFile(){
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.putExtra(Intent.EXTRA_MIME_TYPES, rewardCollectBean.getMime_type());
        intent.setType("*/*");
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        launcher.launch(intent);
    }

    public void commit(){
        if(binding.inputTitleView.length() == 0){
            showMessage(binding.inputTitleView.getHint().toString());
            return;
        }

        if(provinceBean == null){
            showMessage(binding.inputProvinceView.getHint().toString());
            return;
        }

        if(binding.inputDescView.length() == 0){
            showMessage(binding.inputTitleView.getHint().toString());
            return;
        }

        if(AppUtil.isEmpty(filePath) && binding.fileTypeView.isChecked()){
            showMessage("请选择题库资料文件");
            return;
        }

        if(binding.inputUploadContentView.length() == 0 && binding.urlTypeView.isChecked()){
            showMessage("请输入题库资料链接");
            return;
        }

        String title = binding.inputTitleView.getText().toString();
        int provinceId = provinceBean.getId();
        String desc = binding.inputDescView.getText().toString();
        String filePath = this.filePath;
        String url = binding.inputUploadContentView.getText().toString();
        int url_type = binding.fileTypeView.isChecked() ? 1 : 2;//1=文件上传 2=连接上传
        mPresenter.commit(title, provinceId, desc, filePath, url, url_type);
    }

    @Override
    public void commitSuccess() {
        RewardCollectHistoryActivity.start(RewardCollectActivity.this);
        finish();
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //返回上一个页面，删除选中的文件
        deleteSelectFile();
    }
}