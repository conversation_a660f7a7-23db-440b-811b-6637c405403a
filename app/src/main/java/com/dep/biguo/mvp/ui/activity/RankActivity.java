package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.databinding.RankActivityBinding;
import com.dep.biguo.mvp.ui.adapter.ViewPager2Adapter;

import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.dep.biguo.di.component.DaggerRankComponent;
import com.dep.biguo.mvp.contract.RankContract;
import com.dep.biguo.mvp.presenter.RankPresenter;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.google.android.material.tabs.TabLayoutMediator;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;

import java.util.List;

import javax.inject.Inject;

public class RankActivity extends BaseActivity<RankPresenter> implements RankContract.View {
    private static final String POSITION = "position";

    private RankActivityBinding binding;
    @Inject List<BaseFragment> fragmentList;

    private String[] mRankTitles = new String[]{"打卡排行榜", "答题排行榜"};

    public static void start(Context context, int position){
        Intent intent = new Intent(context, RankActivity.class);
        intent.putExtra(POSITION, position);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerRankComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.rank_activity);
        binding.backView.setOnClickListener(v -> finish());
        AppUtil.clearTabClickColor(this, binding.tabLayout);

        if(UserCache.isDayNight()){
            Drawable drawable = binding.backgroundView.getBackground();
            int color = ResourcesCompat.getColor(getResources(), R.color.white, getTheme());
            binding.backgroundView.setBackground(TintDrawableUtil.TintDrawable(drawable, color));
        }else {
            Drawable drawable = AppUtil.getDrawableRes(this,R.drawable.error_coll_background);
            binding.backgroundView.setBackground(drawable);
        }

        binding.viewpager.setAdapter(new ViewPager2Adapter<>(getSupportFragmentManager(), getLifecycle(), fragmentList));
        //设置fragment的缓存个数
        binding.viewpager.setOffscreenPageLimit(fragmentList.size() - 1);
        //禁止拖拽滑动
        binding.viewpager.setUserInputEnabled(false);
        //初始化tabLayout
        AppUtil.clearTabClickColor(this, binding.tabLayout);
        TabLayoutMediator tabLayoutMediator = new TabLayoutMediator(binding.tabLayout, binding.viewpager, false, true, (tab, position) -> {
            tab.setText(mRankTitles[position]);
        });
        //这句话很重要，viewPager与tabLayout绑定
        tabLayoutMediator.attach();
        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        int position = getIntent().getIntExtra(POSITION, 0);
        binding.viewpager.setCurrentItem(position);
    }

    @Override
    public void showMessage(@NonNull String message) {

    }
}
