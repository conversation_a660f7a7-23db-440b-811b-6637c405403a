package com.dep.biguo.mvp.ui.adapter.practice;

import androidx.core.content.ContextCompat;

import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.CardBean;
import com.dep.biguo.bean.QuestionBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.common.Constant;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.html.HtmlUtil;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.PracticeManager;


public class OptionAdapter extends CommonAdapter<String> {
    private QuestionBean mQuestionBean;
    private CardBean.Topic topicBean;
    private PracticeManager mPracticeManager;

    private static final String[] OPTIONS = {"A", "B", "C", "D", "E", "F", "G"};

    public OptionAdapter(PracticeManager mPracticeManager) {
        super(R.layout.practice_single_option_v2_item, null);
        this.mPracticeManager = mPracticeManager;
    }

    @Override
    protected void convert(BaseViewHolder holder, String item) {
        TextView tvOption = holder.getView(R.id.tvOption);
        TextView tvContent = holder.getView(R.id.tvContent);
        //字体大小
        PracticeHelper.changeSize(tvOption, 14);
        PracticeHelper.changeSize(tvContent, 16);
        //让选项控件的尺寸始终比字体大4dp
        int size = DisplayHelper.dp2px(mContext, PracticeHelper.getTextSize(16) + 8);
        tvOption.getLayoutParams().width = size;
        tvOption.getLayoutParams().height = size;

        //当前选项
        String option = OPTIONS[holder.getAbsoluteAdapterPosition()];
        //设置选项文字
        holder.setText(R.id.tvOption, option);
        //设置选项内容
        int viewWidth = DisplayHelper.getWindowWidth(mContext) - DisplayHelper.dp2px(mContext, 62);

        HtmlUtil.form(String.format("%s", item), viewWidth).setTargetView(tvContent);

        String selectAnswer;
        int is_correct;
        if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_DEFAULT){//做题模式
            selectAnswer = topicBean.getSelect_answer();
            is_correct = topicBean.getIs_correct();

        }else if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_SHOW){//背题模式
            selectAnswer = topicBean.getCorrectOption();
            is_correct = Constant.ANSWER_CORRECT;

        }else if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU){//模拟模式
            selectAnswer = topicBean.getSelect_answer();
            is_correct = Constant.ANSWER_NONE;

        }else {//查看解析
            selectAnswer = topicBean.getSelect_answer();
            is_correct = topicBean.getIs_correct();
        }

        if(TextUtils.isEmpty(selectAnswer)){
            selectAnswer = "";
        }

        //判断 做题模式、模拟模式 下是否是含有未选择答案的状态
        boolean isUnSelectStatus = mPracticeManager.mPracticeMode == PracticeHelper.MODE_DEFAULT
                || mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU;

        if (isUnSelectStatus && is_correct == Constant.ANSWER_NONE) {//未检测答案是否正确
            if (selectAnswer.contains(option)) {//选中
                holder.setBackgroundRes(R.id.tvOption, R.drawable.practice_circle_select_v2);
                holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_select_text));
            } else {//未选中
                holder.setBackgroundRes(R.id.tvOption, R.drawable.bg_practice_single_option);
                holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_normal_text));
            }
            holder.setTextColor(R.id.tvContent, ContextCompat.getColor(mContext, R.color.option_normal_text));

        } else if (selectAnswer.contains(option) && topicBean.getCorrectOption().contains(option)) {//答案被选中，且存在于正确答案之中
            holder.setBackgroundRes(R.id.tvOption, R.drawable.practice_circle_correct);
            holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_white_text));
            holder.setTextColor(R.id.tvContent, ContextCompat.getColor(mContext, R.color.option_correct_text));

        } else if (selectAnswer.contains(option) && !topicBean.getCorrectOption().contains(option)) {//答案被选中，但并不存在于正确答案之中
            holder.setBackgroundRes(R.id.tvOption, R.drawable.practice_circle_error);
            holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_white_text));
            holder.setTextColor(R.id.tvContent, ContextCompat.getColor(mContext, R.color.option_error_text));

        } else if (!selectAnswer.contains(option) && topicBean.getCorrectOption().contains(option)) {//答案未被选中，但存在于正确答案之中
            holder.setBackgroundRes(R.id.tvOption, R.drawable.practice_circle_correct);
            holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_white_text));
            holder.setTextColor(R.id.tvContent, ContextCompat.getColor(mContext, R.color.option_normal_text));

        } else {//答案未被选中，也不存在于正确答案之中
            holder.setBackgroundRes(R.id.tvOption, R.drawable.bg_practice_single_option);
            holder.setTextColor(R.id.tvOption, ContextCompat.getColor(mContext, R.color.option_normal_text));
            holder.setTextColor(R.id.tvContent, ContextCompat.getColor(mContext, R.color.option_normal_text));
        }

        //是否可点击
        if (!mPracticeManager.mIsSupportPractice) {
            holder.itemView.setClickable(false);
        } else if (mPracticeManager.mIsOnlySelect) {
            holder.itemView.setClickable(true);
        } else {
            holder.itemView.setClickable((is_correct == Constant.ANSWER_NONE));
        }
    }

    public void setSelectAnswer(int selectOptionIndex){
        String selectAnswer = AppUtil.isEmpty(topicBean.getSelect_answer(), "");
        //若是单选或判断，则只能选择其中一项
        if(mPracticeManager.mPracticeMode == PracticeHelper.MODE_SIMU
                && (topicBean.getTopic_type() == PracticeHelper.TYPE_SINGLE || topicBean.getTopic_type() == PracticeHelper.TYPE_JUDGE)){
            topicBean.setSelect_answer(OPTIONS[selectOptionIndex]);
            return;
        }
        //若所选选项存在于已选字符串中，则移除所选选项
        LogUtil.d("dddd", OPTIONS[selectOptionIndex]);
        if(selectAnswer.contains(OPTIONS[selectOptionIndex])){
            topicBean.setSelect_answer(selectAnswer.replace(OPTIONS[selectOptionIndex], ""));
            return;
        }

        StringBuilder builder = new StringBuilder();
        for(String option : OPTIONS){
            if(selectAnswer.contains(option) || OPTIONS[selectOptionIndex].equals(option)){
                builder.append(option);
            }
        }
        topicBean.setSelect_answer(builder.toString());
    }

    public void setTopicBean(CardBean.Topic topicBean) {
        this.topicBean = topicBean;
    }

    public void setQuestionBean(QuestionBean questionBean) {
        this.mQuestionBean = questionBean;

        getData().clear();
        if (!TextUtils.isEmpty(mQuestionBean.getA()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getA()));
        if (!TextUtils.isEmpty(mQuestionBean.getB()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getB()));
        if (!TextUtils.isEmpty(mQuestionBean.getC()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getC()));
        if (!TextUtils.isEmpty(mQuestionBean.getD()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getD()));
        if (!TextUtils.isEmpty(mQuestionBean.getE()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getE()));
        if (!TextUtils.isEmpty(mQuestionBean.getF()))
            addData(PracticeHelper.filterLabel(mQuestionBean.getF()));

        //当题目丢失
        if(getData().size() == 0){
            notifyDataSetChanged();
        }
    }

}
