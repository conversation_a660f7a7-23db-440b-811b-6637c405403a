package com.dep.biguo.mvp.ui.fragment;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.KeyboardUtils;
import com.biguo.utils.widget.FloatingImageView;
import com.dep.biguo.R;
import com.dep.biguo.bean.IconBean;
import com.dep.biguo.bean.OrganizationRecommendBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.OrganizationRecommendActivityBinding;
import com.dep.biguo.databinding.OrganizationRecommendSearchLayoutBinding;
import com.dep.biguo.di.component.DaggerOrganizationRecommendComponent;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.mvp.contract.OrganizationRecommendContract;
import com.dep.biguo.mvp.presenter.OrganizationRecommendPresenter;
import com.dep.biguo.mvp.ui.activity.BiguoVipOpenActivity;
import com.dep.biguo.mvp.ui.activity.CityActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.OrganizationDetailActivity;
import com.dep.biguo.mvp.ui.activity.OrganizationReservationHistoryActivity;
import com.dep.biguo.mvp.ui.adapter.OrganizationRecommendAdapter;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.IconView;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.wxapi.WxMinApplication;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.youth.banner.BannerConfig;

import java.util.ArrayList;
import java.util.List;

public class OrganizationRecommendFragment extends BaseLoadSirFragment<OrganizationRecommendPresenter> implements OrganizationRecommendContract.View, View.OnClickListener {
    private OrganizationRecommendActivityBinding binding;
    private OrganizationRecommendSearchLayoutBinding searchBinding;
    private OrganizationRecommendAdapter recommendAdapter;
    private ToolBar toolBar;
    private ActivityResultLauncher<Intent> openGPSLauncher;

    private PopupDialog<String> popupDialog;

    private String provinceName;
    private String cityName;
    private String cityCode;
    private String longitude;
    private String latitude;
    private String distance = "全部";

    private boolean isRunOnRequest;

    public static OrganizationRecommendFragment newInstance() {
        return new OrganizationRecommendFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerOrganizationRecommendComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        openGPSLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            //判断是否开启位置服务
            if (!checkGPSIsOpen()) {
                binding.swipeLayout.autoRefresh();
            } else {
                new Handler().postDelayed(() -> mPresenter.requestLocationPermission(true), 100);
            }
        });
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View rootView = inflater.inflate(R.layout.organization_recommend_activity, null);
        binding = DataBindingUtil.bind(rootView);
        binding.setOnClickListener(this);
        View searchView = inflater.inflate(R.layout.organization_recommend_search_layout, null);
        searchBinding = DataBindingUtil.bind(searchView);
        searchBinding.setOnClickListener(this);
        toolBar = new ToolBar.Builder(getContext(), (ViewGroup) binding.getRoot())
                .setTarget(ToolBar.Builder.LEFT)
                .setVisibility(View.GONE)
                .setTarget(ToolBar.Builder.TITLE)
                .setViewLayout(searchBinding.getRoot())
                .setTarget(ToolBar.Builder.RIGHT)
                .setVisibility(View.GONE)
                .build();

        initSearch();

        List<String> images = new ArrayList<>();
        //去掉左右边距，根据比例计算banner图的高度
        binding.headBannerView.getLayoutParams().height = (int) ((DisplayHelper.getWindowWidth(getContext()) - DisplayHelper.dp2px(getContext(), 20)) * Float.parseFloat(binding.headBannerView.getTag().toString()));
        binding.headBannerView.setImages(images);
        binding.headBannerView.setImageLoader(new BannerRoundImageLoader());
        binding.headBannerView.setBannerStyle(BannerConfig.NOT_INDICATOR);
        binding.headBannerView.setOnBannerListener(position -> {
            if(mPresenter.getRecommendBean() != null){
                if(!AppUtil.isEmpty(mPresenter.getRecommendBean().getAds2())){
                    if(mPresenter.getRecommendBean().getAds2().size() > position) {
                        new UmengEventUtils(getActivity())
                                .pushEvent(UmengEventUtils.CLICK_ORGANIZATION_BANNER);

                        OrganizationRecommendBean.Banner banner = mPresenter.getRecommendBean().getAds2().get(position);
                        if(TextUtils.isEmpty(banner.getXcx_path())){
                            if(banner.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;
                            HtmlActivity.start(getContext(), banner.getTarget_url());

                        }else {
                            if(banner.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;
                            WxMinApplication.StartWechat(getContext(), banner.getXcx_path(), banner.getTarget_url());
                        }
                    }
                }
            }
        });

        //搜索结果列表
        recommendAdapter = new OrganizationRecommendAdapter();
        recommendAdapter.setOnItemClickListener((adapter, view, position) -> {
            OrganizationRecommendBean.Organization organization = recommendAdapter.getItem(position);
            if(TextUtils.isEmpty(organization.getName())){
                binding.swipeLayout.autoRefresh();
            }else {
                new UmengEventUtils(getContext())
                        .addParams("position", "list")
                        .addParams("name", organization.getName())
                        .pushEvent(UmengEventUtils.ENTER_ORGANIZATION_DETAIL);
                OrganizationDetailActivity.start(getContext(), organization.getId(), organization.getName(), provinceName, cityName, cityCode, longitude, latitude);
            }
        });
        recommendAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            OrganizationRecommendBean.Organization organization = recommendAdapter.getItem(position);
            new UmengEventUtils(getContext())
                    .addParams("position", "list")
                    .addParams("name", organization.getName())
                    .pushEvent(UmengEventUtils.ENTER_ORGANIZATION_APPLY);

            if(!TextUtils.isEmpty(organization.getXcx_path())) {//跳转小程序
                new UmengEventUtils(mContext)
                        .addParams("jump_time", TimeFormatUtils.formatChinese(System.currentTimeMillis()/1000))
                        .addParams("source_page", "机构推荐 - 微信群引导页")
                        .pushEvent(UmengEventUtils.ENTER_JUMP_TO_WX_MINIPROGRAM);
                WxMinApplication.StartEncoderUrlToMinAppOrApp(getContext(), organization.getXcx_path(), organization.getTarget_url(), true);
            }else if(!TextUtils.isEmpty(organization.getTarget_url())){//跳转H5页面
                HtmlActivity.start(getActivity(), addPublicParams(organization.getTarget_url(), organization.getId()));
            }else
                HtmlActivity.start(getContext(), addPublicParams(Constant.ORGANIZATION_REPORT, organization.getId()));
        });
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));
        binding.recyclerView.setAdapter(recommendAdapter);
        binding.swipeLayout.bindAdapter(recommendAdapter, binding.recyclerView, page -> {
            clearSearchFocus();
            mPresenter.getHead(provinceName, cityName, cityCode);
            mPresenter.getOrganization(provinceName, cityName, cityCode, longitude, latitude, distance, searchBinding.inputSearchView.getText().toString(), page);
        });

        //监听面板滚动结束时，拖动按钮从屏幕外滑动进入
        binding.recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                moveUpAnimation(binding.drag1View);
            }
        });

        return binding.getRoot();
    }

    public void initSearch(){
        //监听输入框是否有获取焦点，从而显示搜索按钮
        searchBinding.inputSearchView.setOnFocusChangeListener((v, hasFocus) -> {
            searchBinding.searchView.setVisibility(hasFocus ? View.VISIBLE : View.INVISIBLE);

            int animatorRes = hasFocus ? R.animator.scale_show : R.animator.scale_hide;
            Animation animation = AnimationUtils.loadAnimation(getContext(), animatorRes);
            searchBinding.searchView.startAnimation(animation);
        });
        //监听软键盘上的搜索按钮
        searchBinding.inputSearchView.setOnEditorActionListener((v, actionId, event) -> {
            KeyboardUtils.hideKeyboard(searchBinding.inputSearchView);
            onClick(searchBinding.searchView);
            return true;
        });
    }
    @Override
    public View initLoadSir() {
        return binding.recyclerView;
    }

    /**
     * 当屏幕尺寸发生变化时调用（小窗或分屏）
     */
    public void screenSizeChange(){
        if(toolBar == null) return;
        toolBar.screenSizeChange();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        try {
            this.provinceName = UserCache.getCity().getProvince_name();
            this.cityName = UserCache.getCity().getCity_name();
            this.cityCode = UserCache.getCity().getCity_code();
        }catch (Exception e){

        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if(!isRunOnRequest)return;

        if(UserCache.getCity() == null){
            ArmsUtils.startActivity(CityActivity.class);
            return;
        }

        //当省份、城市、城市代码有一个有了变化，则刷新一下数据
        String provinceName = UserCache.getCity().getProvince_name();
        String cityName = UserCache.getCity().getCity_name();
        if(!TextUtils.equals(this.provinceName, provinceName) || !TextUtils.equals(this.cityName, cityName)){
            this.provinceName = UserCache.getCity().getProvince_name();
            this.cityName = UserCache.getCity().getCity_name();
            this.cityCode = UserCache.getCity().getCity_code();
            binding.swipeLayout.autoRefresh();
        }
    }

    @Override
    public void onRequest() {
        mPresenter.requestLocationPermission(true);

    }

    @Override
    public void setData(@Nullable Object data) {

    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void onClick(View view) {
        if(view == binding.rangeView){
            clearSearchFocus();
            if(TextUtils.isEmpty(longitude) || TextUtils.isEmpty(latitude)){

                if(TextUtils.isEmpty(longitude) || TextUtils.isEmpty(latitude)){
                    if (checkGPSIsOpen()) {
                        mPresenter.requestLocationPermission(true);
                    } else {
                        openGPS();
                    }
                    return;
                }

                return;
            }

            if(popupDialog != null) {
                popupDialog.showAsDropDown(binding.rangeView, 0, 0);
            }

        }else if(view == searchBinding.searchView){
            binding.swipeLayout.autoRefresh();

        }else if(view == searchBinding.reservationHistoryView){
            if(!MainAppUtils.checkLogin(getContext())) return;
            OrganizationReservationHistoryActivity.start(getContext());

        }else if(view == binding.drag1View){
            new UmengEventUtils(getContext())
                    .pushEvent(UmengEventUtils.CLICK_ORGANIZATION_FLOAT_BUTTON);

            OrganizationRecommendBean recommendBean = mPresenter.getRecommendBean();
            if(recommendBean != null) {
                if (!TextUtils.isEmpty(recommendBean.getXcx_path())) {//跳转小程序
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    WxMinApplication.StartWechat(getContext(), recommendBean.getXcx_path(), recommendBean.getUrl());

                } else if (!TextUtils.isEmpty(recommendBean.getUrl())) {//跳转H5页面
                    HtmlActivity.start(getActivity(), recommendBean.getUrl());

                }
            }
        }
    }

    public void clearSearchFocus(){
        searchBinding.inputSearchView.clearFocus();
        KeyboardUtils.hideKeyboard(searchBinding.inputSearchView);
    }

    @Override
    public void getLocationFail(){
        //刷新一下
        binding.swipeLayout.autoRefresh();
    }

    @Override
    public void getLocationSuccess(String longitude, String latitude) {
        //定位信息
        this.longitude = longitude;
        this.latitude = latitude;

        recommendAdapter.setYourPoint(latitude, longitude);
        //刷新数据
        binding.swipeLayout.autoRefresh();
    }

    @Override
    public void getHeadSuccess(OrganizationRecommendBean bean) {
        createIconView(bean);

        //banner图
        /*List<String> list = new ArrayList<>();
        if(bean.getAds2() != null) {
            for (OrganizationRecommendBean.Banner banner : bean.getAds2()) {
                list.add(banner.getImg());
            }
        }
        binding.headBannerView.update(list);
        binding.headBannerView.start();
        binding.headBannerView.setVisibility(list.isEmpty() ? View.GONE : View.VISIBLE);*/

        //范围选项弹窗
        popupDialog = new PopupDialog.Builder<>(getContext(), bean.getDist())
                .setArrowOffsetX(DisplayHelper.dp2px(getContext(), 10))
                .setForeachData(PopupDialog.ForeachData.getForeachDataInstance())
                .setOnItemClickListener((itemView, data, position) -> {
                    distance = data;
                    binding.rangeView.setText(distance);
                    binding.swipeLayout.autoRefresh();
                })
                .build();

    }

    private void createIconView(OrganizationRecommendBean bean){
        if(AppUtil.isEmpty(bean.getIcons())) return;

        IconBean helpMe = new IconBean();
        helpMe.setImg("helpMe");
        helpMe.setName("找机构");
        helpMe.setType("");
        helpMe.setXcx_path("");
        helpMe.setNeed_login(1);
        helpMe.setTarget_url("https://h5dev.biguotk.com/findAnAgency");
        bean.getIcons().add(helpMe);

        binding.headIconLayout.removeAllViews();
        for (IconBean iconBean : bean.getIcons()){
            if(TextUtils.equals("institutions", iconBean.getType())) continue;

            if(binding.headIconLayout.getChildCount() == 0) {
                View view = new View(getContext());
                view.setLayoutParams(new LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1));
                view.setMinimumWidth(DisplayHelper.dp2px(getContext(), 10));
                binding.headIconLayout.addView(view);
            }

            IconView iconView = new IconView(getContext());
            iconView.setLayoutParams(new ViewGroup.LayoutParams(DisplayHelper.dp2px(getContext(), 80), ViewGroup.LayoutParams.WRAP_CONTENT));
            if(TextUtils.equals("helpMe", iconBean.getImg())){
                iconView.setData(R.drawable.help_me_check_organization, iconBean.getName());
            }else {
                iconView.setData(iconBean.getImg(), iconBean.getName());
            }
            iconView.setXcx_path(iconBean.getXcx_path());
            iconView.setTarget_url(iconBean.getTarget_url());
            iconView.setShowTipImage(iconBean.getTip_img());
            iconView.setOnClickListener(v -> {
                if(TextUtils.isEmpty(iconBean.getXcx_path())){
                    if(iconBean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;
                    HtmlActivity.start(getContext(), addPublicParams(iconBean.getTarget_url(), -1));

                }else {
                    if(iconBean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(getContext())) return;
                    WxMinApplication.StartWechat(getContext(), iconBean.getXcx_path(), addPublicParams(iconBean.getTarget_url(), -1));
                }
            });
            binding.headIconLayout.addView(iconView);

            View view = new View(getContext());
            view.setLayoutParams(new LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1));
            view.setMinimumWidth(DisplayHelper.dp2px(getContext(), 10));
            binding.headIconLayout.addView(view);
        }
    }

    public void finishRefresh(){
        binding.swipeLayout.finishRefresh();
    }

    @Override
    public void getOrganizationFail() {
        binding.swipeLayout.finishLoadMore(false);
    }

    @Override
    public void getOrganizationSuccess(List<OrganizationRecommendBean.Organization> bean) {
        if(binding.swipeLayout.isRefreshing()){
            binding.swipeLayout.finishRefresh();
            recommendAdapter.setNewData(bean);
        }else {
            recommendAdapter.addData(bean);
            binding.swipeLayout.finishLoadMore(true, AppUtil.isEmpty(bean));
        }
    }

    public String addPublicParams(String url, int institution_id){
        StringBuilder builder = new StringBuilder(url)
                .append(url.contains("?") ? "&" : "?")
                .append("province_name=").append(provinceName)
                .append("&city_name=").append(cityName)
                .append("&cityCode=").append(cityCode)
                .append("&longitude=").append(AppUtil.isEmpty(longitude, ""))
                .append("&latitude=").append(AppUtil.isEmpty(latitude, ""));

        if(institution_id > 0){
            builder.append("&institution_id=").append(institution_id);
        }

        if(UserCache.getUserCache() != null) {
            builder.append("&mobile=").append(UserCache.getUserCache().getRsa_mobile());
        }
        return builder.toString();
    }

    /**页面停止滚动时，拖动按钮从屏幕外滑动进来
     * @param view
     */
    private void moveUpAnimation(FloatingImageView view) {
        float hideWidth = view.getMeasuredWidth()/3f*2;
        float start = view.getOrientation() > 0 ? hideWidth : (-DisplayHelper.getWindowWidth(getContext()) + view.getMeasuredWidth()-hideWidth);
        float end = view.getOrientation() > 0 ? 0 : -view.getLeft();

        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationX", start, end);
        animator.setDuration(400);
        animator.start();
    }

    public boolean checkGPSIsOpen() {
        LocationManager locationManager = (LocationManager) AppManager.getAppManager().getTopActivity().getSystemService(Context.LOCATION_SERVICE);
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    public void openGPS() {
        if (!checkGPSIsOpen()) {
            //没有打开则弹出对话框
            new MessageDialog.Builder(getChildFragmentManager())
                    .setTitle("温馨提示")
                    .setContent("该功能需要打开位置")
                    .setNegativeText("暂不开启")
                    .setNegativeClickListener(v -> getLocationFail())
                    .setPositiveText("去开启")
                    .setPositiveClickListener(v -> {
                        //跳转GPS设置界面
                        openGPSLauncher.launch(new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS));
                    })
                    .builder()
                    .show();
        }
    }

    @Override
    public void showEmptyView() {
        super.showEmptyView();
        isRunOnRequest = true;
    }

    @Override
    public void showErrorView(Throwable e) {
        super.showErrorView(e);
        isRunOnRequest = true;
    }

    @Override
    public void showLoadingView() {
        super.showLoadingView();
        isRunOnRequest = true;
    }
}