package com.dep.biguo.mvp.presenter;


import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.bean.AiAskBean;
import com.dep.biguo.mvp.contract.AiAssistantContract;
import com.dep.biguo.mvp.model.api.Api;
import com.dep.biguo.utils.DeviceHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.umeng.commonsdk.UMConfigure;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import okhttp3.FormBody;
import okhttp3.Request;

@FragmentScope
public class AiAssistantPresenter extends BasePresenter<AiAssistantContract.Model, AiAssistantContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private Map<String, Object> map;
    private long time;
    public void setMap(Map<String, Object> map) {
        this.map = map;
    }

    @Inject
    public AiAssistantPresenter(AiAssistantContract.Model model, AiAssistantContract.View rootView) {
        super(model, rootView);
    }

    private Request createRequest(AiAskBean item, String url){
//        map.put("mainType", Integer.parseInt(map.get("mainType").toString()));
//        map.put("topic_type", Integer.parseInt(map.get("topic_type").toString()));
//        map.put("topic_id", Integer.parseInt(map.get("topic_id").toString()));
//        map.put("users_id", UserCache.getUserCache().getUser_id());
//        map.put("cert_type", Integer.parseInt(UserCache.getAppType()));
        if(!AppUtil.isEmpty(item.getQuestion())) {
            map.put("questioning", item.getQuestion());
        }
        if(item.getOutput() != null && !AppUtil.isEmpty(item.getOutput().getSession_id())){
            map.put("session_id", item.getOutput().getSession_id());
        }
        map.put("type", StartFinal.AI_VIDEO_ASK);

        FormBody.Builder body = new FormBody.Builder();
        for (String key : map.keySet()) {
            body.add(key, map.get(key).toString());
        }

        return new Request.Builder()
                .url(url)
                .method("POST", body.build())
                .header("device", "1")
                .header("channel", UMConfigure.sChannel)
                .header("deviceModel", DeviceHelper.getSystemModel())
                .header("deviceVersion", DeviceHelper.getSystemVersion())
                .header("appVersion", AppUtil.isEmpty(DeviceHelper.getVersionName(mRootView.getContext()), ""))
                .header("token", UserCache.getUserCache().getToken())
                .build();
    }
    public void request(AiAskBean item){
        mModel.read(createRequest(item, Api.BASE_URL + "ai/assistant_question"))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<AiAskBean>(mErrorHandler) {
                    @Override
                    public void onNext(AiAskBean result) {
//                        LogUtil.d("dddd", result);
                        if (item.getOutput() == null) {
                            item.setOutput(new AiAskBean.OutputBean());
                        }
                        String lastText =  AppUtil.isEmpty(item.getOutput().getText(), "");
                        AiAskBean.OutputBean outPut = result.getOutput();
                        if(outPut != null){
                            String text = String.format("%s%s", lastText, AppUtil.isEmpty(outPut.getText(), ""));
                            outPut.setText(text);
                            item.setOutput(outPut);
                        }
                        item.setFree_times(result.getFree_times());
                        item.setOver_times(result.getOver_times());
                        item.setResult_code(result.getResult_code());
                        mRootView.readSuccess(item);
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        item.setStatus(-1);
                        mRootView.readSuccess(item);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        item.setStatus(1);
                        mRootView.readSuccess(item);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
