package com.dep.biguo.mvp.presenter;

import android.app.Activity;
import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.AdBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.TruePaperNewBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.bean.jsz.PracticeAdsBean;
import com.dep.biguo.mvp.contract.TruePaperNewContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class TruePaperNewPresenter extends BasePresenter<TruePaperNewContract.Model, TruePaperNewContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;
    private PayResultListener mResultPayListener;

    @Inject
    public TruePaperNewPresenter(TruePaperNewContract.Model model, TruePaperNewContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void getTruePaper(int courseId, String code, int page) {
        mModel.getTruePaper(courseId, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<TruePaperNewBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<TruePaperNewBean> response) {
                        if (response.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getTruePaperSuccess(response.getData());

                        }else {
                            mRootView.getTruePaperFail();
                            mRootView.showErrorView(new Throwable());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.getTruePaperFail();
                        mRootView.showErrorView(t);
                    }
                });
    }
    public void getAppTerrible(int province_id) {
        mModel.getAppTerrible(province_id, "give_good_review")
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<AdBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<AdBean> response) {
                        if (response.isSuccess()) {
                            startWechat(response.getData());
                            mRootView.startWxSuccess();
                        }
                    }
                });
    }

    public void finishTask(String type){
        mModel.finishTask(type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            UserCache.cacheShowGoodApp(true);
                            mRootView.showMessage("请查收积分奖励");
                        }
                    }
                });
    }

    /**点击了微信群或微信客服
     * @param bean
     */
    public void startWechat(AdBean bean){
        Activity activity = AppManager.getAppManager().getTopActivity();
        if(TextUtils.isEmpty(bean.getXcx_path())){
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(activity)) return;

            HtmlActivity.start(activity, bean.getTarget_url());
        }else {
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(activity)) return;

            WxMinApplication.StartWechat(activity, bean.getXcx_path(), bean.getTarget_url());
        }
    }

    public void getAds(String code) {
        String scene = "study";
        mModel.getAds(code, PracticeHelper.PRACTICE_TRUE, scene)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PracticeAdsBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<PracticeAdsBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getAdsSuccess(response.getData());
                        }
                    }
                });
    }
    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void payOrder(Map<String,Object> paramsMap, String payType) {
        paramsMap.put(PayParamsBean.PAY_TYPE,payType);
        mModel.paySingleOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }

    public void shareUnlock(int exams_real_paper_id) {
        mModel.shareUnlock(exams_real_paper_id, 0)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse result) {
                        if (result.isSuccess()) {
                            mRootView.shareUnlockSuccess(exams_real_paper_id);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
