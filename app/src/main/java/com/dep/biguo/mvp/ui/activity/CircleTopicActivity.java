package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.databinding.DataBindingUtil;

import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.CircleActionBean;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.ActivityCircleTopicBinding;
import com.dep.biguo.di.component.DaggerCircleTopicComponent;
import com.dep.biguo.dialog.CircleCommentDialog;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.CircleTopicContract;
import com.dep.biguo.mvp.presenter.CircleTopicPresenter;
import com.dep.biguo.mvp.ui.adapter.CirclePublishAdapter;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.ShareUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class CircleTopicActivity extends BaseLoadSirActivity<CircleTopicPresenter> implements CircleTopicContract.View, View.OnClickListener {
    private static final String TOPIC = "topic";
    private ActivityCircleTopicBinding binding;
    private NormalToolbarUtil toolbarUtil;

    private CirclePublishAdapter publishAdapter;

    private Map<Integer, String> commentMap = new HashMap<>();

    private CircleBean.Topic topic;

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerCircleTopicComponent //如找不到该类,请编译一下项目
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    public static void start(Context context, CircleBean.Topic topic) {
        Intent intent = new Intent(context, CircleTopicActivity.class);
        intent.putExtra(TOPIC, GsonUtils.toJson(topic));
        context.startActivity(intent);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_circle_topic);
        binding.setOnClickListener(this);

        toolbarUtil = new NormalToolbarUtil(this)
                .setCenterText("话题主页")
                .setRightDrawableRes(R.drawable.user_icon_notify)
                .setRightVisibility(UserCache.getUserCache() == null ? View.INVISIBLE : View.VISIBLE)
                .setRightOnClickListener(v -> {
                    if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;
                    ArmsUtils.startActivity(CircleMessageActivity.class);
                });

        publishAdapter = new CirclePublishAdapter();
        publishAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CircleDetailActivity.start(CircleTopicActivity.this, publishAdapter.getItem(i));
        });
        publishAdapter.setOnItemChildClickListener(new CirclePublishAdapter.OnItemChildClickListener() {
            @Override
            public void share(CircleBean.Moment moment) {
                showShareDialog(moment);
            }

            @Override
            public void comment(CircleBean.Moment moment) {
                if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;

                showCommentDialog(moment);
            }

            @Override
            public void good(CircleBean.Moment moment) {
                if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;

                mPresenter.good(moment, 0);
            }

            @Override
            public void startTopic(CircleBean.Topic topic) {

            }

            @Override
            public void moreOption(View targetView, CircleBean.Moment moment) {
                if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;
                showMoreOptionDialog(targetView, moment);
            }
        });
        binding.momentRecyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));


        binding.swipeView.bindAdapter(publishAdapter, binding.momentRecyclerView, page -> {
            mPresenter.getCircle(topic.getPosts_table_id(), binding.filterView.isChecked() ? 1 : 2, page);
        });

        binding.filterView.setOnClickCheckedListener(() -> {
            binding.filterView.setEnabled(false);
            binding.swipeView.autoRefresh();
        });

        return 0;
    }

    /**显示分享弹窗
     * @param moment 动态
     */
    private void showShareDialog(CircleBean.Moment moment){
        Object loadObj = !AppUtil.isEmpty(moment.getFiles()) ? moment.getFiles().get(0).getCover_url() : R.drawable.app_icon;
        ShareDialog.loadImageShare(this, loadObj, resource -> {
            Activity activity = AppManager.getAppManager().getTopActivity();
            if(activity == null) return;

            new ShareDialog.Builder(activity)
                    .setShareTitle(moment.getContent())
                    .setShareContent(String.format("来自%s的动态", moment.getNickname()))
                    .setShareBitmap(resource, ShareUtil.SHARE_TYPE.LINK)
                    .setOnShareListener(type -> mPresenter.good(moment, 1))
                    .setShareUrl(String.format("%s?posts_id=%s", Constant.CIRCLE_DETAIL, moment.getPosts_id()))
                    .builder()
                    .show();
        });
    }

    /**显示评论弹窗
     * @param publish 动态
     */
    private void showCommentDialog(CircleBean.Moment publish){
        new CircleCommentDialog(this)
                .setOnSendListener((isSend, content) -> {
                    commentMap.put(publish.getPosts_id(), content);
                    if(!isSend) return;
                    mPresenter.comment(publish, content);
                })
                .setHint(String.format("回复 %s", publish.getNickname()))
                .setComment(AppUtil.isEmpty(commentMap.get(publish.getPosts_id()), ""))
                .show();
    }

    /**显示举报弹窗
     * @param post_id 动态ID
     */
    private void showReportCommentDialog(int post_id){
        new CircleCommentDialog(this)
                .setOnSendListener((isSend, content) -> {
                    if(!isSend) return;
                    mPresenter.reportMoment(post_id, content);
                })
                .setHint("请输入您举报原因")
                .show();
    }

    private void showDeleteCommentDialog(CircleBean.Moment moment){
        new MessageDialog.Builder(getSupportFragmentManager())
                .setTitle("温馨提醒")
                .setContent("是否删除动态？")
                .setNegativeText("取消")
                .setPositiveText("删除")
                .setPositiveClickListener(v -> mPresenter.deleteMoment(moment))
                .builder()
                .show();
    }

    public void showMoreOptionDialog(View targetView, CircleBean.Moment moment){
        List<String> optionList = new ArrayList<>();
        if(UserCache.getUserCache() != null && UserCache.getUserCache().getUser_id() == moment.getUsers_id()){
            optionList.add("删除");
        }else {
            optionList.add("举报");
        }
        new PopupDialog.Builder<>(this, optionList)
                .setForeachData(PopupDialog.ForeachData.getForeachDataInstance())
                .setOnItemClickListener((itemView, data, position) -> {
                    if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;

                    if(TextUtils.equals("删除", data)){
                        showDeleteCommentDialog(moment);
                    }else if(TextUtils.equals("举报", data)) {
                        showReportCommentDialog(moment.getPosts_id());
                    }
                })
                .setOrientation(LinearLayoutCompat.HORIZONTAL)
                .setArrowXAnchor(PopupDialog.ARROW_END)
                .setArrowOffsetX(-DisplayHelper.dp2px(this, 9))
                .build()
                .showAsDropDown(targetView, 0, -DisplayHelper.dp2px(this, 12));
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        topic = GsonUtils.fromJson(getIntent().getStringExtra(TOPIC), CircleBean.Topic.class);

        binding.topicNameView.setText(String.format("#%s", topic.getName()));
        binding.lookNumView.setText(String.format("%s人浏览", topic.getBrowse_num()));
        binding.commentCountView.setText(String.format("讨论（%s）", topic.getJoin_people()));
    }

    @Override
    public void onRequest() {
        mPresenter.getCircle(topic.getPosts_table_id(), binding.filterView.isChecked() ? 1 : 2, binding.swipeView.getCurrentPage());
    }

    @Override
    public void onClick(View view) {
        if(view == binding.ivPush){
            if(!MainAppUtils.checkLogin(CircleTopicActivity.this)) return;
            CirclePushActivity.start(this, topic);
        }
    }

    @Override
    public void getCircleDataSuccess(List<CircleBean.Moment> list) {
        //刷新的动画未结束，binding.swipeView.autoRefresh()调用是无法刷新的，所以要延迟一段时间，等属性动画消失
        new Handler().postDelayed(() -> {
            if(binding != null) {
                binding.filterView.setEnabled(true);
            }
        }, 600);

        if(binding.swipeView.isRefreshing()){
            publishAdapter.setNewData(list);
        }else {
            publishAdapter.addData(list);
        }

        binding.swipeView.finishLoadMore(true, AppUtil.isEmpty(list));
    }

    @Override
    public void commentSuccess(CircleBean.Moment publish) {
        commentMap.remove(publish.getPosts_id());
        publish.setComments_count(publish.getComments_count() + 1);
        publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(publish));
    }

    @Override
    public void deleteComment(CircleBean.Moment moment) {
        int itemPosition = publishAdapter.getHolderPositionByItemData(moment);
        publishAdapter.getData().remove(moment);
        publishAdapter.notifyItemRemoved(itemPosition);
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    public void showMyMessage(UserBean userBean){
        toolbarUtil.setRightVisibility(View.VISIBLE);
    }

    @Subscriber(tag = EventBusTags.LOGOUT_SUCCESS)
    public void hideMyMessage(UserBean userBean){
        toolbarUtil.setRightVisibility(View.GONE);
    }

    @Subscriber(tag = EventBusTags.CIRCLE_PUSH_SUCCESS)
    public void addNewPushCircle(CircleBean.Moment moment){
        binding.swipeView.autoRefresh();
    }

    @Subscriber(tag = EventBusTags.CIRCLE_REFRESH_COMMENT_ITEM)
    private void commentChange(CircleActionBean action) {
        try {
            CircleBean.Moment inMoment = null;
            for (CircleBean.Moment moment : publishAdapter.getData()) {
                if (moment.getPosts_id() != action.getMoment().getPosts_id()) continue;

                inMoment = moment;
            }
            if(inMoment == null) return;

            //找出被操作的评论在列表中对应的那条评论
            if(action.getAction() == CircleActionBean.COMMENT){
                //发布评论
                inMoment.setComments_count(inMoment.getComments_count() + 1);
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));

            }else if(action.getAction() == CircleActionBean.COMMENT_DELETE){
                //删除评论
                inMoment.setRelay_count(inMoment.getRelay_count() - 1);
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));
            }

        }catch (Exception e){

        }
    }

    @Subscriber(tag = EventBusTags.CIRCLE_REFRESH_MOMENT_ITEM)
    private void momentChange(CircleActionBean action) {
        try {
            CircleBean.Moment inMoment = null;
            for (CircleBean.Moment moment : publishAdapter.getData()) {
                if (moment.getPosts_id() != action.getMoment().getPosts_id()) continue;

                inMoment = moment;
            }
            if(inMoment == null) return;

            //找出被操作的评论在列表中对应的那条评论
            if(action.getAction() == CircleActionBean.MOMENT_GOOD){
                //点赞动态
                inMoment.setIs_like(1 - inMoment.getIs_like());
                inMoment.setLikes_count(inMoment.getLikes_count() + (inMoment.getIs_like() == 1 ? 1 : -1));
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));

            }else if(action.getAction() == CircleActionBean.SHARE){
                //分享
                inMoment.setRelay_count(inMoment.getRelay_count() + 1);
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));

            }else if(action.getAction() == CircleActionBean.COMMENT){
                //发布评论
                inMoment.setComments_count(inMoment.getComments_count() + 1);
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));

            }else if(action.getAction() == CircleActionBean.COMMENT_DELETE){
                //删除评论
                inMoment.setComments_count(inMoment.getComments_count()  - 1);
                publishAdapter.notifyItemChanged(publishAdapter.getHolderPositionByItemData(inMoment));

            }else if(action.getAction() == CircleActionBean.MOMENT_DELETE){
                //删除动态
                publishAdapter.remove(publishAdapter.getData().indexOf(inMoment));
            }
        }catch (Exception e){

        }
    }
}
