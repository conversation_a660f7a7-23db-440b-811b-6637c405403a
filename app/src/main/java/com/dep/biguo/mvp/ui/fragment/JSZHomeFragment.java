package com.dep.biguo.mvp.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.bean.CourseGroupBean;
import com.dep.biguo.bean.HomeBean;
import com.dep.biguo.bean.SvipBean;
import com.dep.biguo.bean.jsz.JSZProvinceBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.di.component.DaggerJSZHomeFragmentComponent;
import com.dep.biguo.mvp.contract.JSZHomeFragmentContract;
import com.dep.biguo.mvp.presenter.JSZHomeFragmentPresenter;
import com.dep.biguo.mvp.ui.activity.ArticleActivity;
import com.dep.biguo.mvp.ui.activity.ChapterActivity;
import com.dep.biguo.mvp.ui.activity.CourseFeedbackActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.JSZProvinceActivity;
import com.dep.biguo.mvp.ui.activity.SearchUrlActivity;
import com.dep.biguo.mvp.ui.activity.SecretActivity;
import com.dep.biguo.mvp.ui.activity.SimuPaperActivity;
import com.dep.biguo.mvp.ui.activity.SvipActivity;
import com.dep.biguo.mvp.ui.activity.TopicActivity;
import com.dep.biguo.mvp.ui.activity.TruePaperActivity;
import com.dep.biguo.mvp.ui.activity.VideoDetailActivity;
import com.dep.biguo.mvp.ui.activity.VipActivity;
import com.dep.biguo.mvp.ui.adapter.CourseAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeArticleAdapter;
import com.dep.biguo.mvp.ui.adapter.HomeMenuAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.BannerRoundImageLoader;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.AppTypePopupWindow;
import com.dep.biguo.dialog.BottomOptionDialog;
import com.dep.biguo.dialog.CoursePopupWindow;
import com.dep.biguo.dialog.InputDialog;
import com.dep.biguo.widget.MyItemDecoration;
import com.dep.biguo.widget.loadsir.EmptyContentCallBack;
import com.dep.biguo.widget.loadsir.LoadingCallBack;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.kingja.loadsir.callback.Callback;
import com.kingja.loadsir.core.LoadService;
import com.kingja.loadsir.core.LoadSir;
import com.youth.banner.Banner;
import com.youth.banner.BannerConfig;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import butterknife.BindView;
import butterknife.OnClick;

import static com.jess.arms.utils.Preconditions.checkNotNull;


/**
 * ================================================
 * Description:教师资格证
 * ================================================
 */
public class JSZHomeFragment extends BaseFragment<JSZHomeFragmentPresenter> implements JSZHomeFragmentContract.View {
    @BindView(R.id.toolbar)
    ConstraintLayout toolbar;
    @BindView(R.id.banner)
    Banner banner;
    @BindView(R.id.rvArticle)
    RecyclerView rvArticle;
    @BindView(R.id.rvHomeMenu)
    RecyclerView rvHomeMenu;
    @BindView(R.id.tvProvince)
    TextView tvProvince;
    @BindView(R.id.tvCourse)
    TextView tvCourse;
    @BindView(R.id.ivSuperVip)
    ImageView ivSuperVip;
    @BindView(R.id.conlVipSecret)
    ConstraintLayout conlVipSecret;
    @BindView(R.id.ivVipSecret1)
    ImageView ivVipSecret1;
    @BindView(R.id.ivVipSecret2)
    ImageView ivVipSecret2;
    @BindView(R.id.bannerAdvertising)
    Banner bannerAdvertising;
    @BindView(R.id.tvChange)
    TextView tvChange;

    @Inject
    HomeArticleAdapter mArticleAdapter;
    @Inject
    HomeMenuAdapter mHomeMenuAdapter;

    private List<HomeBean.BannerBean> mBannerData;

    private LoadService mLoadService;

    private CoursePopupWindow mCoursePopupWindow;
    private AppTypePopupWindow mAppTypePopupWindow;
    private JSZProvinceBean mJSZProvinceBean;

    private boolean mIsRefreshCourse = true;

    public static JSZHomeFragment newInstance() {
        JSZHomeFragment fragment = new JSZHomeFragment();
        return fragment;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerJSZHomeFragmentComponent //如找不到该类,请编译一下项目
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.jsz_home_fragment, container, false);
        mLoadService = LoadSir.getDefault().register(view, (Callback.OnReloadListener) v -> getHomeData());
        return mLoadService.getLoadLayout();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        StatusBarHelper.setStatusbarColorView(getActivity(), toolbar);
        getHomeData();
        tvChange.setVisibility(Constant.SINGLE_APP ? View.GONE : View.VISIBLE);
        initBanner();
        initArticle();
        initMenu();
    }

    private void initArticle() {
        mArticleAdapter.bindToRecyclerView(rvArticle);
        ArmsUtils.configRecyclerView(rvArticle, new LinearLayoutManager(getActivity()));
    }

    private void getHomeData() {
        if (UserCache.getJSZProvince() == null) {
            mPresenter.getHomeData(false, false, -1);
        } else {
            mPresenter.getHomeData(true, false, UserCache.getJSZProvince().getId());
        }

    }
    private void initMenu() {
        rvHomeMenu.addItemDecoration(new MyItemDecoration(getActivity()));
        mHomeMenuAdapter.bindToRecyclerView(rvHomeMenu);
        ArmsUtils.configRecyclerView(rvHomeMenu, new GridLayoutManager(getActivity(), 3));

        mHomeMenuAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (AppUtil.isEmpty(mHomeMenuAdapter.getData())) return;
            HomeBean.MenuBean bean = mHomeMenuAdapter.getItem(position);

            switch (bean.getType()) {
                case 1:
                    //if (!checkProfession()) return;
                    if (!checkCourse()) return;
                    TopicActivity.start(getActivity(), PracticeHelper.PRACTICE_COURSE);
                    break;
                case 2:
                    //if (!checkProfession()) return;
                    if (!checkCourse()) return;
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    ArmsUtils.startActivity(TruePaperActivity.class);
                    break;
                case 3:
                    //if (!checkProfession()) return;
                    if (!checkCourse()) return;
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    ArmsUtils.startActivity(ChapterActivity.class);
                    break;
                case 4:
                    //if (!checkProfession()) return;
                    if (!checkCourse()) return;
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    SimuPaperActivity.Start(getContext(), UserCache.getCourse().getName(), UserCache.getCourse().getCode());
                    break;
                case 5:
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    if (!checkCourse()) return;
                    TopicActivity.start(getActivity(), UserCache.getCourse().getCode(), UserCache.getCourse().getName(), PracticeHelper.PRACTICE_ERROR, PracticeHelper.PRACTICE_ERROR);
                    break;
                case 6:
                    if (!MainAppUtils.checkLogin(getContext())) return;
                    if (!checkCourse()) return;
                    TopicActivity.start(getActivity(), UserCache.getCourse().getCode(), UserCache.getCourse().getName(), PracticeHelper.PRACTICE_COLL, PracticeHelper.PRACTICE_COLL);
                    break;
                case 7:
                    //if (!checkProfession()) return;
                    ArmsUtils.startActivity(SearchUrlActivity.class);
                    break;
                case 8:
                    //if (!checkProfession()) return;
                    if (!checkCourse()) return;
                    VideoDetailActivity.start(getActivity(), UserCache.getCourse().getCode());
                    break;
                case 10:
                    WxMinApplication.StartWechat(getContext());
                    break;
                case 11:
                case 14:
                    HtmlActivity.start(getActivity(), bean.getTarget_url());
                    break;
            }
        });
    }

    private void initBanner() {
        banner.setImageLoader(new BannerRoundImageLoader());
        banner.setBannerStyle(BannerConfig.NOT_INDICATOR);
        banner.start();
        banner.setOnBannerListener(position -> {
            if (mBannerData == null) return;
            if (AppUtil.isEmpty(mBannerData)) return;
            HtmlActivity.start(getActivity(), mBannerData.get(position).getTarget_url());
        });
    }


    private void showCoursePopupWindow(CourseGroupBean data) {
        if (mCoursePopupWindow == null) {
            mCoursePopupWindow = new CoursePopupWindow(getActivity());
        }
        mCoursePopupWindow.setCourseData(data);
        CourseAdapter courseAdapter = mCoursePopupWindow.getCourseAdapter();
        if (courseAdapter != null) {
            courseAdapter.setOnItemClickListener((adapter, view, position) -> {
                List<CourseGroupBean.CourseBean> courses = courseAdapter.getData();
                if (AppUtil.isEmpty(courses)) return;
                UserCache.cacheCourse(courses.get(position));
                refreshCourse();
                mCoursePopupWindow.dismiss();
                getHomeData();
            });
        }
        courseAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (!MainAppUtils.checkLogin(getContext())) return;
            if (AppUtil.isEmpty(courseAdapter.getData())) return;
            CourseGroupBean.CourseBean bean = courseAdapter.getItem(position);
            if (TextUtils.isEmpty(bean.getCode())) return;
            BottomOptionDialog dialog = new BottomOptionDialog(getActivity(), bean.getJoined() == 1 ? BottomOptionDialog.Type.COURSE_MANAGER__YBK : BottomOptionDialog.Type.COURSE_MANAGER__WBK);
            dialog.setOnItemClickListener((adapter1, view1, position1) -> {
                if (position1 == 0) {
                    mPresenter.editCourse(bean.getId());
                } else if (position1 == 1) {
                    InputDialog inputDialog = InputDialog.newInstance(InputDialog.TYPE_COURSE_SOCRE);
                    inputDialog.setOnInputListener((v, input) -> mPresenter.editScore(bean.getCode(), input));
                    inputDialog.show(getChildFragmentManager());
                } else if (position1 == 2) {
                    CourseFeedbackActivity.start(getActivity(), bean);
                }

                dialog.dismiss();
            });
            dialog.show();
        });

        if (!mCoursePopupWindow.isShowing()) {
            if(UserCache.getJSZTestType() != null) {
                mCoursePopupWindow.setSchoolName("");
                mCoursePopupWindow.setProfessionName(UserCache.getJSZTestType().getName());
            }
            mCoursePopupWindow.showAsDropDown(toolbar, 0, 0);
        }
    }

    private void loadVipSecret(ImageView iv, String url) {
        ImageLoader.loadImageNoPlaceholder(iv, url);
    }

    public boolean checkPopupDismiss() {
        if (mCoursePopupWindow != null && mCoursePopupWindow.isShowing()) {
            mCoursePopupWindow.dismiss();
            return false;
        } else if (mAppTypePopupWindow != null && mAppTypePopupWindow.isShowing()) {
            mAppTypePopupWindow.dismiss();
            return false;
        }
        return true;
    }

    //检查专业
    private boolean checkProfession() {
        /*JSZProvinceBean profession = new Gson().fromJson(KVHelper.getString(UserHelper.JSZ_PROFESSION), JSZProvinceBean.class);
        if (profession == null) {
            showMessage(getString(R.string.no_profession));


            ArmsUtils.startActivity(JSZProvinceActivity.class);
            return false;
        }*/
        return true;
    }

    //检查课程
    private boolean checkCourse() {
        if (UserCache.getCourse() == null) {
            showMessage(getString(R.string.no_course));
            mPresenter.getCourse(mIsRefreshCourse);
            mIsRefreshCourse = false;
            return false;
        }
        return true;
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshProfession();
        refreshCourse();
    }

    private void refreshProfession() {
        JSZProvinceBean bean = UserCache.getJSZProvince();
        if (bean != null)
            tvProvince.setText(bean.getName());
        else
            tvProvince.setText("选择省份");
    }

    private void refreshCourse() {
        CourseGroupBean.CourseBean course = UserCache.getCourse();
        if (course != null)
            tvCourse.setText(course.getName());
        else
            tvCourse.setText("选择课程");
    }

    /**
     * @param data 当不需要参数时 {@code data} 可以为 {@code null}
     */
    @Override
    public void setData(@Nullable Object data) {

    }

    @Override
    public void showLoading() {
        mLoadService.showCallback(LoadingCallBack.class);
    }

    @Override
    public void hideLoading() {
        mLoadService.showSuccess();
    }

    @Override
    public void showEmptyView() {
        mLoadService.showCallback(EmptyContentCallBack.class);
    }

    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ArmsUtils.snackbarText(message);
    }

    @Override
    public void launchActivity(@NonNull Intent intent) {
        checkNotNull(intent);
        ArmsUtils.startActivity(intent);
    }

    @Override
    public void killMyself() {

    }

    @OnClick({R.id.tvCourse,
            R.id.tvArticleMore,
            R.id.tvProvince,
            R.id.ivCustomer,
            R.id.tvChange,
            R.id.ivSuperVip})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tvProvince: //选择省份
                if (!checkPopupDismiss()) return;
                ArmsUtils.startActivity(JSZProvinceActivity.class);
                break;
            case R.id.tvCourse: //选择课程
                if (!checkProfession()) return;
                if (!checkPopupDismiss()) return;
                mPresenter.getCourse(mIsRefreshCourse);
                mIsRefreshCourse = false;
                break;
            case R.id.tvChange: //证书切换
                if (!checkPopupDismiss()) return;
                if (mAppTypePopupWindow == null) {
                    mAppTypePopupWindow = new AppTypePopupWindow(getActivity());
                    mAppTypePopupWindow.setHeight(((ViewGroup)toolbar.getParent()).getHeight() - DisplayHelper.dp2px(getContext(), 34));
                }
                mAppTypePopupWindow.showAsDropDown(toolbar, 0, 0);
                break;
            case R.id.tvArticleMore: //更多干货
                //ArmsUtils.startActivity(ArticleActivity.class);
                ArticleActivity.start(getActivity(), ArticleActivity.HISTORY_NEWS);
                break;
            case R.id.ivCustomer: //客服
                WxMinApplication.StartWechat(getContext());
                break;
            case R.id.ivSuperVip://超级VIP
                if (!checkProfession()) return;
                ArmsUtils.startActivity(SvipActivity.class);
                break;
        }
    }

    @Override
    public void setBannerData(List<HomeBean.BannerBean> data) {
        mBannerData = data;
        List<String> images = new ArrayList<>();
        for (HomeBean.BannerBean bean : data)
            images.add(bean.getImg_url());
        banner.update(images);
    }

    @Override
    public void setAdvertisingData(List<HomeBean.AdvertisingBean> data) {
        bannerAdvertising.setVisibility(ArmsUtils.isEmpty(data) ? View.GONE : View.VISIBLE);

        if (bannerAdvertising.getVisibility() == View.VISIBLE) {
            List<String> images = new ArrayList<>();
            for (HomeBean.AdvertisingBean bean : data)
                images.add(bean.getImg());

            bannerAdvertising.setImages(images);
            bannerAdvertising.setImageLoader(new BannerRoundImageLoader());
            bannerAdvertising.setBannerStyle(BannerConfig.NOT_INDICATOR);
            bannerAdvertising.start();
            bannerAdvertising.setOnBannerListener(position -> {
                HomeBean.AdvertisingBean bean = data.get(position);
                HtmlActivity.start(getActivity(), bean.getUrl());
            });
        }
    }

    @Override
    public void setVipSecretData(String vipImg, String secretImg) {
        if (TextUtils.isEmpty(vipImg) && TextUtils.isEmpty(secretImg)) {
            conlVipSecret.setVisibility(View.GONE);
        } else {
            conlVipSecret.setVisibility(View.VISIBLE);
            //三种情况 有VIP无押密 无VIP有押密 有VIP有押密
            ivVipSecret2.setVisibility(!TextUtils.isEmpty(vipImg) && !TextUtils.isEmpty(secretImg) ? View.VISIBLE : View.INVISIBLE);
            loadVipSecret(ivVipSecret1, !TextUtils.isEmpty(vipImg) ? vipImg : secretImg);
            loadVipSecret(ivVipSecret2, secretImg);

            ivVipSecret1.setOnClickListener(v -> {
                if (!checkProfession()) return;
                if (!checkCourse()) return;
                if (!TextUtils.isEmpty(vipImg)) {
                    ArmsUtils.startActivity(VipActivity.class);
                } else {
                    ArmsUtils.startActivity(SecretActivity.class);
                }
            });

            ivVipSecret2.setOnClickListener(v -> {
                if (!checkProfession()) return;
                if (!checkCourse()) return;
                ArmsUtils.startActivity(SecretActivity.class);
            });
        }
    }

    @Override
    public void setCourseData(CourseGroupBean data) {
        showCoursePopupWindow(data);
    }

    @Override
    public void setSvipData(SvipBean data) {
        ivSuperVip.setVisibility(data.getIs_shelves() == 0 ? View.GONE : View.VISIBLE);
        if (ivSuperVip.getVisibility() == View.VISIBLE) {
            ImageLoader.loadImageNoPlaceholder(ivSuperVip, data.getIs_super_vip() == 0 ? R.drawable.home_icon_supervip_n : R.drawable.home_icon_supervip_s);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mArticleAdapter = null;
    }
}
