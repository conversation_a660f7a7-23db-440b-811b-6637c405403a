package com.dep.biguo.mvp.model;

import android.app.Application;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.GroupCommentBean;
import com.dep.biguo.bean.OssSignatureBean;
import com.dep.biguo.mvp.contract.AssessOrderContract;
import com.dep.biguo.mvp.contract.CirclePushContract;
import com.dep.biguo.mvp.model.api.service.CircleService;
import com.dep.biguo.mvp.model.api.service.GroupService;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.integration.IRepositoryManager;
import com.jess.arms.mvp.BaseModel;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;

@ActivityScope
public class AssessOrderModel extends BaseModel implements AssessOrderContract.Model {
    @Inject
    Gson mGson;
    @Inject
    Application mApplication;

    @Inject
    public AssessOrderModel(IRepositoryManager repositoryManager) {
        super(repositoryManager);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mGson = null;
        this.mApplication = null;
    }

    @Override
    public Observable<BaseResponse<OssSignatureBean>> getOssSignature() {
        return Observable.just(mRepositoryManager.obtainRetrofitService(CircleService.class)
                        .getOssSignature())
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

    @Override
    public Observable<BaseResponse> addAssess(String orderType, int skill_id, String value, String assess, int source_type, String orderId, int cert_type, List<GroupCommentBean.Media> files) {
        return Observable.just(mRepositoryManager.obtainRetrofitService(GroupService.class)
                        .addAssess(orderType, skill_id, value, assess, orderId, source_type, cert_type, GsonUtils.toJson(files)))
                .flatMap(baseResponseObservable -> baseResponseObservable);
    }

}