package com.dep.biguo.mvp.presenter;

import android.Manifest;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.LocationBean;
import com.dep.biguo.bean.OrganizationRecommendBean;
import com.dep.biguo.mvp.contract.OrganizationRecommendContract;
import com.biguo.utils.widget.LoadingDialog;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.RequestPermissions;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class OrganizationRecommendPresenter extends BasePresenter<OrganizationRecommendContract.Model, OrganizationRecommendContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    private OrganizationRecommendBean recommendBean;

    public OrganizationRecommendBean getRecommendBean() {
        return recommendBean;
    }

    @Inject
    public OrganizationRecommendPresenter(OrganizationRecommendContract.Model model, OrganizationRecommendContract.View rootView) {
        super(model, rootView);
    }

    public void getHead(String provinceName, String cityName, String cityCode) {
        mModel.getHead(provinceName, cityName, cityCode)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrganizationRecommendBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<OrganizationRecommendBean> s) {
                        if (s.isSuccess()) {
                            recommendBean = s.getData();
                            mRootView.getHeadSuccess(s.getData());
                        }
                    }
                });

    }

    public void getOrganization(String provinceName, String cityName, String cityCode, String longitude, String latitude, String distance, String search, int page) {
        mModel.getOrganization(provinceName, cityName, cityCode, longitude, latitude, distance, search, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationRecommendBean.Organization>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationRecommendBean.Organization>> s) {
                        if (s.isSuccess()) {
                            if (AppUtil.isEmpty(s.getData())) {
                                mRootView.showEmptyView();
                                mRootView.getOrganizationSuccess(new ArrayList<>());
                            } else {
                                mRootView.showSuccessView();
                                mRootView.getOrganizationSuccess(s.getData());
                            }
                        } else {
                            mRootView.showErrorView(null);
                            mRootView.getOrganizationFail();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                        mRootView.getOrganizationFail();
                    }
                });

    }

    public void requestLocationPermission(boolean isAgainAsk) {
        new RequestPermissions.Builder((AppCompatActivity) mRootView.getActivity())
                .setRequestContentText("我们将申请定位权限，获取您当前所在城市，用于搜索附近的机构，是否允许?")
                .setPermissions(Manifest.permission.ACCESS_FINE_LOCATION, isAgainAsk)
                .setOnRequestResultListener(new RequestPermissions.OnRequestResultListener() {
                    @Override
                    public void onSuccess() {
                        Activity activity = mRootView.getActivity();
                        LocationManager locationManager = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
                        if (ActivityCompat.checkSelfPermission(mRootView.getActivity(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                            locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0, new LocationListener() {
                                @Override
                                public void onLocationChanged(@NonNull Location location) {
                                    locationManager.removeUpdates(this);
                                    mRootView.getLocationSuccess(location.getLongitude() + "", location.getLatitude() + "");
                                }
                            });

                        }else {
                            mRootView.getLocationFail();
                        }
                    }

                    @Override
                    public void onFailure(boolean isShowRequestContentDialog) {
                        if (mRootView == null) return;
                        mRootView.getLocationFail();
                    }

                    @Override
                    public void onNotAsk(boolean isShowRequestContentDialog) {
                        if (mRootView == null) return;
                        mRootView.getLocationFail();
                    }
                }).build();
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
