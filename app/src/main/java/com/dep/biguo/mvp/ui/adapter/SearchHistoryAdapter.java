package com.dep.biguo.mvp.ui.adapter;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.FrameLayout;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.mmkv.UserCache;

import java.util.List;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/9/7
 * @Description:
 */
public class SearchHistoryAdapter extends CommonAdapter<String> {

    public SearchHistoryAdapter(@Nullable List<String> data) {
        super(R.layout.search_history_item, data);
    }

    @Override
    protected void convert(BaseViewHolder holder, String item) {
        holder.setText(R.id.tvKey, item);
        holder.getView(R.id.ivDelete).setOnClickListener(v -> deleteKey(holder.getLayoutPosition()));
    }

    /**
     * 删除搜索
     *
     * @param position
     */
    public void deleteKey(int position) {
        remove(position);

        StringBuilder history = new StringBuilder();
        for (String key : getData()) {
            history.append(key);
            history.append(",");
        }
        UserCache.cacheSearchKey(history.toString(), true);
    }

}
