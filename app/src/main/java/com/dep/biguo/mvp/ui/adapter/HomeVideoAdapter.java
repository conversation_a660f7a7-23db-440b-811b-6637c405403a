package com.dep.biguo.mvp.ui.adapter;

import android.graphics.Color;
import android.graphics.drawable.Drawable;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.AllVideoBean;
import com.dep.biguo.common.CommonAdapter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.StyleTextView;

import java.util.List;

public class HomeVideoAdapter extends CommonAdapter<AllVideoBean> {

    public HomeVideoAdapter(@Nullable List<AllVideoBean> data) {
        super(R.layout.home_video_item, data);

    }

    @Override
    protected void convert(BaseViewHolder holder, AllVideoBean item) {
        int imageRes;
        if(StartFinal.VIDEO2.equals(item.getType())){
            imageRes = R.drawable.bg_top_group_video_chuanjiang;
        }else if(StartFinal.VIDEO4.equals(item.getType())) {
            imageRes = R.drawable.bg_top_group_video_texunban;
        } else {
            imageRes = R.drawable.bg_top_group_video_jingjiang;
        }
        Drawable drawable = ResourcesCompat.getDrawable(mContext.getResources(), imageRes, mContext.getTheme());
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        holder.setImageDrawable(R.id.bgView, drawable);

        holder.setText(R.id.bgNameView, item.getName());
        holder.setText(R.id.bgCodeView, String.format("课程代码：%s", item.getCode()));
        holder.setVisible(R.id.groupPriceView, item.getStatus() == 0);
        holder.setVisible(R.id.priceView, item.getStatus() == 0);
        holder.setVisible(R.id.statusView, item.getStatus() != 0);
        holder.setText(R.id.statusView, item.getStatus() == 1 ? "拼团中" : "已开通课程");
        holder.setText(R.id.buyCountView, String.format("%s人已买", item.getTotal_buy_count()));

        StyleTextView bgCodeView = holder.getView(R.id.bgCodeView);
        bgCodeView.getStyleViewAttr().setBgGradientColor(getBgCodeViewBgColor(item.getType()));

        holder.setText(R.id.priceView, String.format("¥%s", item.getPrice()));
        if(item.getIs_newcomers() == StartFinal.YES){
            holder.setText(R.id.groupPriceView, String.format("¥%s", item.getNewcomers_price()));
        }else if (UserCache.getUserCache() == null || !UserCache.isMemberShip()) {
            holder.setText(R.id.groupPriceView, String.format("¥%s", item.getGroup_price()));
        } else if (UserCache.isMemberShip()) {
            holder.setText(R.id.groupPriceView, String.format("¥%s", item.getMember_group_price()));
        }
    }

    private int[] getBgCodeViewBgColor(String type){
        if(StartFinal.VIDEO2.equals(type)){
            return new int[]{Color.parseColor("#18C869"), Color.parseColor("#09DF86")};
        }else if(StartFinal.VIDEO4.equals(type)) {
            return new int[]{Color.parseColor("#008CFE"), Color.parseColor("#17AFFD")};
        } else {
            return new int[]{Color.parseColor("#FC3D3E"), Color.parseColor("#FE5A5A")};
        }
    }
}