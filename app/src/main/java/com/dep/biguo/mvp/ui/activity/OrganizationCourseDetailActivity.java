package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.SpannableUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrganizationCourseDetailBean;
import com.dep.biguo.databinding.OrganizationCourseDetailActivityBinding;
import com.dep.biguo.di.component.DaggerOrganizationCourseDetailComponent;
import com.dep.biguo.mvp.contract.OrganizationCourseDetailContract;
import com.dep.biguo.mvp.presenter.OrganizationCourseDetailPresenter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.BannerImageLoader;
import com.dep.biguo.utils.html.HtmlUtil;
import com.dep.biguo.utils.OpenOtherMapUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.ToolBar;
import com.jess.arms.di.component.AppComponent;
import com.youth.banner.BannerConfig;

import java.util.ArrayList;
import java.util.List;

public class OrganizationCourseDetailActivity extends BaseLoadSirActivity<OrganizationCourseDetailPresenter> implements OrganizationCourseDetailContract.View, View.OnClickListener {
    private static final String ACTIVITIES_ID = "activities_id";
    private OrganizationCourseDetailActivityBinding binding;

    private String longitude;
    private String latitude;
    private String tel;
    private int activities_id;

    public static void start(Context context, int activities_id){
        Intent intent = new Intent(context, OrganizationCourseDetailActivity.class);
        intent.putExtra(ACTIVITIES_ID, activities_id);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerOrganizationCourseDetailComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.organization_course_detail_activity);
        binding.setOnClickListener(this);

        new ToolBar.Builder(this, (ViewGroup) binding.scrollView.getParent())
                .setAlignScreenTop(true)
                .setTarget(ToolBar.Builder.TITLE)
                .setText("课程详情")
                .setTextColor(R.color.tran)
                .build()
                .setFollowScrollBackground(binding.scrollView, (toolBar, effectiveRange, changeRate) -> {
                    int textRgb = UserCache.isDayNight() ? 189 : 51;
                    int textAlpha = Math.max(0, (int)(changeRate * 255));
                    toolBar.getViewByTarget(ToolBar.Builder.TITLE).setTextColor(Color.argb(textAlpha, textRgb, textRgb, textRgb));
                });

        ((LinearLayout.LayoutParams)binding.headBannerView.getLayoutParams()).topMargin = DisplayHelper.getStatusBarHeight(this);

        List<String> images = new ArrayList<>();
        //去掉左右边距，根据比例计算banner图的高度
        binding.headBannerView.getLayoutParams().height = (int) ((DisplayHelper.getWindowWidth(this)) * (9 / 16f));
        binding.headBannerView.setImages(images);
        binding.headBannerView.setImageLoader(new BannerImageLoader());
        binding.headBannerView.setBannerStyle(BannerConfig.NOT_INDICATOR);
        binding.headBannerView.start();
        binding.headBannerView.setOnBannerListener(position -> {

        });
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.scrollView;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Intent intent = getIntent();
        activities_id = intent.getIntExtra(ACTIVITIES_ID, 0);
    }

    @Override
    public void onRequest() {
        mPresenter.getOrganizationCourseDetail(activities_id);
    }

    @Override
    public void onClick(View v) {
        if(v == binding.telView){
            if(TextUtils.isEmpty(tel)) return;

            Intent intent = new Intent(Intent.ACTION_DIAL);
            intent.setData(Uri.parse("tel:"+tel));
            startActivity(intent);

        }else if(v == binding.addressView){
            if(TextUtils.isEmpty(longitude) || TextUtils.isEmpty(latitude))return;
            String address = binding.addressView.getText().toString();
            if(!OpenOtherMapUtil.goToGaodeMap(this, address, latitude, longitude)
                    && !OpenOtherMapUtil.goToBaiduMap(this, address, latitude, longitude)
                    && !OpenOtherMapUtil.goToTencentMap(this, address, latitude, longitude)){
                // 未安装
                AppUtil.copyText(this, binding.addressView.getText().toString());
                showMessage("复制成功");
            }

        }else if(v == binding.reportView){
            if(!MainAppUtils.checkLogin(this)) return;
            mPresenter.courseReservation(activities_id);
        }
    }

    @Override
    public void getOrganizationCourseDetailSuccess(OrganizationCourseDetailBean bean) {
        /*if(!AppUtils.isEmpty(bean.getBanners())) {
            List<String> list = new ArrayList<>();
            list.add(bean.getCourse_cover());
            binding.headBannerView.update(list);
        }*/
        List<String> list = new ArrayList<>();
        list.add(bean.getCourse_cover());
        binding.headBannerView.update(list);

        binding.courseNameView.setText(bean.getCourse_name());
        binding.dateView.setText(String.format("开课时间：%s", bean.getStart_course_time()));
        binding.teacherView.setText(bean.getTeacher());
        binding.teacherInfoView.setText(bean.getTeacher_introduction());
        binding.courseInfoView.setText(bean.getCourse_introduction());
        binding.addressView.setText(bean.getAddress());
        int courseInfoWidth = DisplayHelper.getWindowWidth(this) - DisplayHelper.dp2px(this, 20);
        HtmlUtil.form(bean.getCourse_introduction(), courseInfoWidth, 1000)
                .setOnImageClickListener((position, url) -> {
                    ImageActivity.mPaths.add(url);
                    ImageActivity.start(OrganizationCourseDetailActivity.this, position);
                }).setTargetView(binding.courseInfoView);

        tel = bean.getContact_number();
        latitude = bean.getLatitude();
        longitude = bean.getLongitude();

        if(bean.isIs_reserved()) {
            binding.reportView.setText("已预约");
            binding.reportView.setEnabled(false);

        }else if(bean.getSurplus_count() == 0){
            binding.reportView.setText("约满了");
            binding.reportView.setEnabled(false);

        }else {
            binding.reportView.setEnabled(true);
            String reportText = String.format("免费预约\n剩余名额：%s", bean.getSurplus_count());
            SpannableStringBuilder spannedString = SpannableUtil.setSizeString(reportText, 4, reportText.length(), DisplayHelper.dp2px(this, 2));
            binding.reportView.setText(spannedString);
        }
    }

    @Override
    public void courseReservationSuccess() {
        showMessage("预约成功");
        binding.reportView.setText("已预约");
    }
}