package com.dep.biguo.mvp.contract;

import android.content.Context;

import com.dep.biguo.bean.AliyunAuthBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.VideoDetailBean;
import com.dep.biguo.mvp.contract.view.ICommonView;
import com.jess.arms.mvp.IModel;

import java.util.List;

import io.reactivex.Observable;

public interface GroupGoodsVideoListContract {
    interface View extends ICommonView {
        Context getContext();
    }

    interface Model extends IModel {
        Observable<BaseResponse<VideoDetailBean>> getVideoList(String code, int professions_id, String goodsType, int product_id);

        Observable<BaseResponse<AliyunAuthBean>> getPlayAuth(String video_id);
    }
}