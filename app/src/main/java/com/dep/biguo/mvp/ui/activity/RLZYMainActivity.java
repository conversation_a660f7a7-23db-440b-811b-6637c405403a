package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dep.biguo.mvp.ui.adapter.ViewPager2Adapter;
import com.google.android.material.tabs.TabLayout;

import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.viewpager2.widget.ViewPager2;

import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.di.component.DaggerRLZYMainComponent;
import com.dep.biguo.mvp.contract.RLZYMainContract;
import com.dep.biguo.mvp.presenter.RLZYMainPresenter;
import com.dep.biguo.mvp.ui.fragment.CKHomeFragment;
import com.dep.biguo.mvp.ui.fragment.ExamGuideFragment;
import com.dep.biguo.mvp.ui.fragment.RLZYHomeFragment;
import com.dep.biguo.mvp.ui.fragment.RLZYUserFragment;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.UserAgreementDialog;
import com.google.android.material.tabs.TabLayoutMediator;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.umeng.analytics.MobclickAgent;
import com.umeng.socialize.UMShareAPI;

import org.simple.eventbus.Subscriber;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindArray;
import butterknife.BindView;

import static com.jess.arms.utils.Preconditions.checkNotNull;


/**
 * ================================================
 *
 * @Author: created by biguo
 * @CreatedDate :05/11/2020 15:41
 * @Description: 人力资源资格证承载页
 * ================================================
 */
public class RLZYMainActivity extends BaseActivity<RLZYMainPresenter> implements RLZYMainContract.View {
    @BindArray(R.array.rlzy_main_tab_text)
    String[] mMainTabTitles;
    @BindView(R.id.vpMain)
    ViewPager2 vpMain;
    @BindView(R.id.tbMain)
    TabLayout tbMain;

    private TypedArray mMainTabIcons;

    private List<BaseFragment> mFragments;

    private long clickTime;//连续按两次返回键

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerRLZYMainComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        return R.layout.jnz_main_activity;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        UserCache.setAppType(Constant.RLZY);

        mFragments = new ArrayList<>();
        mFragments.add(RLZYHomeFragment.newInstance());
        mFragments.add(ExamGuideFragment.newInstance());
        mFragments.add(RLZYUserFragment.newInstance());
        mPresenter.getUserInfo();
        mPresenter.bindPushClientId();
        initBottomTab();
        /**
         * 1.单独编译成考APP时候
         * 2.未同意用户协议 -> 弹出协议 -> 弹出引导
         */
        if (UserCache.getAppType().equals(Constant.RLZY) && !KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)) {
            UserAgreementDialog dialog = new UserAgreementDialog();
            dialog.setOnAgreementListener(() -> {
                KVHelper.putValue(UserCache.MAIN_AGREEMENT, true);
            });
            dialog.show(getSupportFragmentManager());
        }
    }

    /**
     * 设置底部
     */
    private void initBottomTab() {
        mMainTabIcons = getResources().obtainTypedArray(R.array.rlzy_main_tab_icon);

        if (mMainTabTitles == null || mMainTabIcons == null) return;

        vpMain.setAdapter(new ViewPager2Adapter<>(getSupportFragmentManager(), getLifecycle(), mFragments));
        //设置fragment的缓存个数
        vpMain.setOffscreenPageLimit(mFragments.size() - 1);
        //禁止viewPager2滑动
        vpMain.setUserInputEnabled(false);
        //初始化tabLayout
        AppUtil.clearTabClickColor(this, tbMain);
        TabLayoutMediator tabLayoutMediator = new TabLayoutMediator(tbMain, vpMain, false, false, (tab, position) -> {
            initTabItem(tab, mMainTabTitles[position], mMainTabIcons.getDrawable(position));
        });
        //这句话很重要，viewPager与tabLayout绑定
        tabLayoutMediator.attach();
    }

    private void initTabItem(TabLayout.Tab tab, String title, Drawable imgRes) {
        View tabView = LayoutInflater.from(this).inflate(R.layout.main_tab_item, null);
        ((ImageView) tabView.findViewById(R.id.ivIcon)).setImageDrawable(imgRes);
        ((TextView) tabView.findViewById(R.id.tvName)).setText(title);
        ((TextView) tabView.findViewById(R.id.tvName)).setTextColor(ContextCompat.getColorStateList(this, R.drawable.rlzy_main_text_color));
        tab.setCustomView(tabView);
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    private void loginSuccess(UserBean user) {
        if (mPresenter == null) return;
        mPresenter.getUserInfo();
        mPresenter.bindPushClientId();
    }

    @Subscriber(tag = EventBusTags.LOGOUT_SUCCESS)
    private void logoutSuccess(UserBean user) {
        if (mPresenter == null) return;
        MobclickAgent.onProfileSignOff();
    }

    @Subscriber(tag = EventBusTags.CHANGE_DAY_NIGHT)
    private void changeDayNight(boolean night) {
        if (night) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            StatusBarHelper.setStatusBarDarkMode(this);
        } else {
            StatusBarHelper.setStatusBarLightMode(this);
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
        recreate();
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ToastUtils.show(message);
    }

    @Override
    public void launchActivity(@NonNull Intent intent) {
        checkNotNull(intent);
        ArmsUtils.startActivity(intent);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (KeyEvent.KEYCODE_BACK == keyCode) {

            if (mFragments.get(0) instanceof CKHomeFragment) {
                boolean flag = ((CKHomeFragment) mFragments.get(0)).checkPopupDismiss();
                if (!flag) return true;
            }

            if ((System.currentTimeMillis() - clickTime) > 2000) {
                showMessage("再按一次退出程序");
                clickTime = System.currentTimeMillis();
            } else {
                finish();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //解决友盟分享内存泄漏
        UMShareAPI.get(this).release();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }
}
