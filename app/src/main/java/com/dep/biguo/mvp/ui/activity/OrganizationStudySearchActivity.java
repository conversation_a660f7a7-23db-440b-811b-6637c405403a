package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.KeyboardUtils;
import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.LayerBean;
import com.dep.biguo.bean.OrganizationStudyBean;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.databinding.OrganizationStudySearchActivityBinding;
import com.dep.biguo.di.component.DaggerOrganizationStudySearchComponent;
import com.dep.biguo.dialog.FilterDownDialog;
import com.dep.biguo.mvp.contract.OrganizationStudySearchContract;
import com.dep.biguo.mvp.presenter.OrganizationStudySearchPresenter;
import com.dep.biguo.mvp.ui.adapter.OrganizationStudyAdapter;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.widget.AlignGridItemDecoration;
import com.dep.biguo.widget.ToolBar;
import com.jess.arms.di.component.AppComponent;

import java.util.ArrayList;
import java.util.List;

public class OrganizationStudySearchActivity extends BaseLoadSirActivity<OrganizationStudySearchPresenter> implements OrganizationStudySearchContract.View, View.OnClickListener {
    private static final String ORGANIZATION_ID = "organizationId";
    private static final String FORM_ID = "form_id";

    private OrganizationStudySearchActivityBinding binding;
    private OrganizationStudyAdapter studyAdapter;

    private int organizationId;
    private int form_id;

    private List<ProvinceBean> provinceList;//省份集合
    private List<SchoolBean> schoolList;//学校集合
    private List<LayerBean> layerList;//层次集合

    private ProvinceBean selectProvince;//选中的省份
    private SchoolBean selectSchool;//选中的学校
    private LayerBean selectLayer;//选中的层次

    private static final int province = 0;
    private static final int school = 1;
    private static final int layer = 2;

    public static void start(Context context, int organizationId, int form_id){
        Intent intent = new Intent(context, OrganizationStudySearchActivity.class);
        intent.putExtra(ORGANIZATION_ID, organizationId);
        intent.putExtra(FORM_ID, form_id);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerOrganizationStudySearchComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.organization_study_search_activity);
        binding.setOnClickListener(this);
        new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText("自考助学班")
                .build();

        studyAdapter = new OrganizationStudyAdapter();
        studyAdapter.setOnItemClickListener((adapter, view, position) -> {
            OrganizationStudyBean.OrganizationItem item = studyAdapter.getItem(position);
            OrganizationStudyGoodsActivity.start(OrganizationStudySearchActivity.this, item.getProduct_id(), false);
        });

        //监听输入框是否有获取焦点，从而显示搜索按钮
        binding.inputSearchView.setOnFocusChangeListener((v, hasFocus) -> {
            binding.searchView.setVisibility(hasFocus ? View.VISIBLE : View.INVISIBLE);

            int animatorRes = hasFocus ? R.animator.scale_show : R.animator.scale_hide;
            Animation animation = AnimationUtils.loadAnimation(OrganizationStudySearchActivity.this, animatorRes);
            binding.searchView.startAnimation(animation);
        });

        //监听软键盘上的搜索按钮
        binding.inputSearchView.setOnEditorActionListener((v, actionId, event) -> {
            KeyboardUtils.hideKeyboard(binding.inputSearchView);
            onClick(binding.searchView);
            return true;
        });

        GridLayoutManager manager = (GridLayoutManager) binding.recyclerView.getLayoutManager();
        manager.setSpanCount(DisplayHelper.getWindowWidth(this) / DisplayHelper.dp2px(this, 165));
        binding.recyclerView.setLayoutManager(manager);
        binding.recyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));

        binding.swipeView.bindAdapter(studyAdapter, binding.recyclerView, page -> {
            KeyboardUtils.hideKeyboard(binding.inputSearchView);

            String search = binding.inputSearchView.getText().toString();
            int province_id = selectProvince == null ? 0 : selectProvince.getId();
            int school_id = selectSchool == null ? 0 : selectSchool.getId();
            int layer_id = selectLayer == null ? 0 : selectLayer.getId();
            mPresenter.getSearch(search, province_id, school_id, layer_id, organizationId, form_id, page);
        });
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.recyclerView;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        organizationId = getIntent().getIntExtra(ORGANIZATION_ID, 0);
        form_id = getIntent().getIntExtra(FORM_ID, 0);
    }

    @Override
    public void onRequest() {
        mPresenter.getSearch("", 0, 0, 0, organizationId, form_id, binding.swipeView.getCurrentPage());
    }

    @Override
    public void onClick(View view) {
        if(view == binding.provinceView){
            //点击了省份
            if(AppUtil.isEmpty(provinceList)){
                mPresenter.getProvince();
            }else {
                getProvince(provinceList);
            }

        }else if(view == binding.schoolView){
            //点击了学校
            TextView[] checkView = {binding.provinceView};
            if(!checkHasInput(checkView))return;

            if(AppUtil.isEmpty(schoolList)){
                mPresenter.getSchool(selectProvince.getId(), organizationId, form_id);
            }else {
                getSchool(schoolList);
            }

        }else if(view == binding.layerView){
            //点击了层次
            TextView[] checkView = {binding.provinceView, binding.schoolView};
            if(!checkHasInput(checkView))return;

            if(AppUtil.isEmpty(layerList)){
                mPresenter.getLayer(selectSchool.getId(), organizationId, form_id);
            }else {
                getLayer(layerList);
            }

        }else if(view == binding.searchView){
            binding.swipeView.autoRefresh();
        }
    }

    public void setSelecting(TextView selectingView){
        TextView[] textViews = {binding.provinceView, binding.schoolView, binding.layerView};
        for(TextView itemView : textViews){
            if(itemView == selectingView){
                int color = ResourcesCompat.getColor(getResources(), R.color.theme, getTheme());
                itemView.setTextColor(color);

                Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.arrow_black_up, getTheme());
                drawable = TintDrawableUtil.TintDrawable(drawable, color);
                itemView.setCompoundDrawablesRelative(null, null, drawable, null);

            }else {
                int color = ResourcesCompat.getColor(getResources(), R.color.tblack, getTheme());
                itemView.setTextColor(color);

                TextDrawableLoader.loadRight(this, itemView, R.drawable.arrow_black_down);
            }
        }
    }

    /**检查是否有选择前置条件
     * @param textView 需要检查的控件
     * @return
     */
    private boolean checkHasInput(TextView[] textView){
        for(TextView itemView : textView) {
            if (itemView == binding.provinceView && selectProvince == null) {
                showMessage("请先选择省份");
                return false;
            }else if (itemView == binding.schoolView && selectSchool == null) {
                showMessage("请先选择院校");
                return false;
            }else if (itemView == binding.layerView && selectLayer == null) {
                showMessage("请先选择层次");
                return false;
            }
        }
        return true;
    }

    /**返回下拉弹窗的某一项内容
     * @param obj 可以传递ProvinceBean，SchoolBean，ProfessionBean，String
     * @return
     */
    private String getPopupItemName(Object obj){
        if(obj instanceof ProvinceBean){
            ProvinceBean bean = (ProvinceBean) obj;
            return bean.getName();

        }else if(obj instanceof SchoolBean){
            SchoolBean bean = (SchoolBean) obj;
            return bean.getName();

        }else if(obj instanceof LayerBean){
            LayerBean bean = (LayerBean) obj;
            return bean.getName();

        }else if(obj instanceof String){
            return obj.toString();
        }else {
            return "";
        }
    }

    /**当逆向选择时，需要清空输入的信息
     * @param selectType
     */
    public void clearInput(int selectType){
        if(selectType == province || selectType == school){
            //将与层次相关的信息删除掉
            layerList = null;
            selectLayer = null;
            binding.layerView.setText("层次");
        }
        if(selectType == province){
            //将与学校相关的信息删除掉
            schoolList = null;
            selectSchool = null;
            binding.schoolView.setText("院校");
        }
    }

    /**选中下拉弹窗的某一项内容
     * @param obj 可以传递ProvinceBean，SchoolBean，ProfessionBean，String
     */
    private void select(Object obj, int selectType){
        if(selectType == province){
            selectProvince = (ProvinceBean) obj;

        }else if(selectType == school){
            selectSchool = (SchoolBean) obj;

        }else if(selectType == layer){
            selectLayer = (LayerBean) obj;
        }

        binding.swipeView.autoRefresh();
    }

    /**返回所选中的那一项
     * @param selectType
     * @return
     */
    private Object getSelect(int selectType){
        if(selectType == province){
            return selectProvince;

        }else if(selectType == school){
            return selectSchool;

        }else if(selectType == layer){
            return selectLayer;
        }
        return null;
    }

    @Override
    public void getSearchFail() {
        binding.swipeView.finishLoadMore(false);
    }

    @Override
    public void getSearchSuccess(List<OrganizationStudyBean.OrganizationItem> list) {
        if(binding.swipeView.isRefreshing()){
            studyAdapter.setNewData(list);
        }else {
            studyAdapter.addData(list);
        }

        binding.swipeView.finishLoadMore(true, AppUtil.isEmpty(list));
    }

    public void showPopupDialog(List<Object> list, TextView view, int selectType){
        if(AppUtil.isEmpty(list)){
            showMessage("暂无可选选项");
            return;
        }

        int delay = 0;
        if(binding.inputSearchView.hasFocus()){
            //当软件盘打开的情况下，延迟100毫秒，给隐藏软键盘留出一段时间，避免下拉弹窗的位置异常
            delay = 100;
            KeyboardUtils.hideKeyboard(binding.inputSearchView);
        }

        new Handler().postDelayed(() -> {
            FilterDownDialog downDialog = new FilterDownDialog(OrganizationStudySearchActivity.this);
            downDialog.setOnCheckListener((obj, position) -> {
                view.setText(getPopupItemName(obj));
                clearInput(selectType);
                select(obj, selectType);
            });
            downDialog.setOnDismissListener(() -> setSelecting(null));
            downDialog.setList(list);
            downDialog.setSelect(getSelect(selectType));
            downDialog.setHeight(binding.recyclerView.getHeight());
            downDialog.showAsDropDown(view);
            setSelecting(view);
        }, delay);
    }

    @Override
    public void getProvince(List<ProvinceBean> list) {
        this.provinceList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.provinceView, province);
    }

    @Override
    public void getSchool(List<SchoolBean> list) {
        this.schoolList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.schoolView, school);
    }

    @Override
    public void getLayer(List<LayerBean> list) {
        this.layerList = list;
        List<Object> tempList = new ArrayList<>(list);
        showPopupDialog(tempList, binding.layerView, layer);
    }
}