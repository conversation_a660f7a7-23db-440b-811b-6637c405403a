package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;

import com.dep.biguo.R;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ExpressBean;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.OrderDetailBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.contract.OrderDetailContract;
import com.dep.biguo.mvp.ui.activity.OrderDetailActivity;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class OrderDetailPresenter extends BasePresenter<OrderDetailContract.Model, OrderDetailContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    private PayResultListener mPayListener;

    private int mOrderId;//订单的order_id
    private int mListType;//订单类型
    private OrderDetailBean mOrderDetailBean;//订单详情数据，只有在获取订单详情数据之后才初始化

    private CountDownTimer mCloseTimer;//代付款状态时的订单关闭倒计时

    @Inject
    public OrderDetailPresenter(OrderDetailContract.Model model, OrderDetailContract.View rootView) {
        super(model, rootView);
        mPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.refresh();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }

    public void init(Intent intent){
        Bundle bun = intent.getExtras();
        if(bun == null) {
            mOrderId = intent.getIntExtra(OrderDetailActivity.ORDER_Id, -1);
            mListType = intent.getIntExtra(OrderDetailActivity.LIST_TYPE, -1);
        }else {
            mOrderId = bun.getInt(OrderDetailActivity.ORDER_Id, -1);
            mListType = bun.getInt(OrderDetailActivity.LIST_TYPE, -1);
        }
    }

    /**返回订单所属的列表类别
     * @return 订单所属的列表类别
     */
    public int getListType() {
        return mListType;
    }

    /**返回订单的详情数据
     * @return 详情数据
     */
    public OrderDetailBean getOrderDetailBean() {
        return mOrderDetailBean;
    }


    /**获取订单状态的文字提示
     *
     * @return 文字提示
     */
    public String getOrderFinishStatus(){
        if(mOrderDetailBean.getRefund_status() == 1){
            return "退款申请受理中";
        }else if(mOrderDetailBean.getRefund_status() == 2){
            return "退款申请已驳回";
        }else if(mOrderDetailBean.getRefund_status() == 3){
            return "订单已退款";
        }else if(mOrderDetailBean.getRefund_status() == 0){
            if(mOrderDetailBean.getState() == OrderDetailActivity.WAIT_PAY){
                //待支付状态启动一个倒计时，通过倒计时设置订单状态的提示文案
                if(mOrderDetailBean.getRemain_time() > 0) {
                    startCloseTimer(mOrderDetailBean.getRemain_time());
                }else {
                    return "等待买家付款";
                }

            }else if(mOrderDetailBean.getState() == OrderDetailActivity.WAIT_SEND){
                if(PayUtils.BOOK.equals(mOrderDetailBean.getType())){
                    //待发货状态根据订单的类型返回提示文案
                    return "等待卖家发货";
                }else{
                    //拼团中的状态
                    return "正在拼团中";
                }

            }else if(mOrderDetailBean.getState() == OrderDetailActivity.WAIT_SIGN){
                //待收货，只有图书订单有这个状态
                return "等待买家收货";

            }else if(mOrderDetailBean.getState() == OrderDetailActivity.ORDER_FINISH){//已完成状态
                if(mOrderDetailBean.getIs_group() == StartFinal.YES){
                    return "拼团已完成";
                }
                //已完成状态
                return "订单已完成";

            }else if(mOrderDetailBean.getState() == OrderDetailActivity.CLOSE){
                //已关闭状态
                return "订单已关闭";
            }else if(mOrderDetailBean.getState() == OrderDetailActivity.CANCEL){
                //已关闭状态
                return "拼团已取消";
            }
        }
        return "";
    }

    /**倒计时
     * @param totalTime 拼团的剩余时间，服务器返回的单位是秒
     */
    public void startCloseTimer(long totalTime){
        if(mCloseTimer == null){
            mCloseTimer = new CountDownTimer(totalTime*1000,1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    long time = millisUntilFinished/1000;
                    long day = time / 86400;
                    long hours = time % 86400 / 3600;
                    long minute = time % 86400 % 3600 / 60;
                    long second = time % 86400 % 3600 % 60;

                    String timeFormat = getFormatText(day+"" , "天") +
                            getFormatText(hours+"" , "小时") +
                            getFormatText(minute+"" , "分钟") +
                            getFormatText(second+"" , "秒");
                    if(mRootView!=null) {
                        mRootView.countDownTimer(timeFormat);
                    }
                }

                @Override
                public void onFinish() {
                    getOrderDetail();
                }
            }.start();
        }
    }

    /**根据订单类型返回对应的图标
     * @return drawable图标
     */
    public Drawable getTypeTitleDrawable(){
        Drawable drawable;
        if(mListType == PayUtils.BOOK_LIST){
            drawable = mRootView.getActivity().getDrawable(R.drawable.order_book_image);
        }else if(mListType == PayUtils.SERVICE_LIST){
            drawable = mRootView.getActivity().getDrawable(R.drawable.order_service_image);
        }else {
            drawable = mRootView.getActivity().getDrawable(R.drawable.order_consumption_image);
        }
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        return drawable;
    }

    /**格式字符串
     * @param label 标题
     * @param text 内容
     * @return 返回信息，当label或text为空时,或label是字符串"0"时，返回空字符串，表示该条消息不应该显示
     */
    public String getFormatText(String label, String text){
        return TextUtils.isEmpty(label) || TextUtils.isEmpty(text) || label.equals("0") ? "" : String.format("%s%s",label,text);
    }

    /**取出物流信息
     *
     */
    public void getTransport(){
        List<ExpressBean> transportInfo = mOrderDetailBean.getTransport_info();

        if (transportInfo == null || transportInfo.isEmpty()) {
            mRootView.showMessage("暂无物流信息");
        } else {
            List<String> beans = new ArrayList<>();
            beans.addAll(ExpressBean.dealExpressBeanToString(transportInfo));

            mRootView.showTransportDialog(beans);
        }
    }

    /**获取订单详情
     *
     */
    public void getOrderDetail() {
        mModel.getOrderDetail(mOrderId)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<OrderDetailBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<OrderDetailBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mOrderDetailBean = s.getData();
                                mRootView.showSuccessView();
                                mRootView.setOrderDetail(s.getData());
                            }
                        }else {
                            mRootView.showEmptyView();
                        }
                    }

                    @Override
                    public void onError(@NotNull Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**取消订单
     *
     */
    public void cancelOrder() {
        Observable<BaseResponse<Object>> observable;
        if(Constant.CK.equals(UserCache.getAppType())){
            observable = mModel.cancelCkOrder(mOrderId, mListType);
        }else {
            observable = mModel.cancelOrder(mOrderId, mListType);
        }
        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            getOrderDetail();
                            mRootView.refresh();
                        }
                    }
                });
    }


    /**取消拼团
     *
     */
    public void cancelGroup() {
        mModel.cancelGroup(mOrderId, mListType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            getOrderDetail();
                            mRootView.refresh();
                        }
                    }
                });
    }

    /**删除订单
     *
     */
    public void deleteOrder() {
        Observable<BaseResponse<Object>> observable;
        if(Constant.CK.equals(UserCache.getAppType())){
            observable = mModel.deleteCkOrder(mOrderId, mListType);
        }else {
            observable = mModel.deleteOrder(mOrderId, mListType);
        }
        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.finish();
                        }
                    }
                });
    }

    /**确认收货
     *
     */
    public void confirmOrder() {
        mModel.confirmOrder(mOrderId, mListType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            getOrderDetail();
                            mRootView.refresh();
                        }
                    }
                });
    }

    public void confirmGraduateOrder() {
        mModel.confirmGraduateOrder(mOrderId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            getOrderDetail();
                            mRootView.refresh();
                        }
                    }
                });
    }
    /**我要评论
     * @param assess
     */
    public void addAssess(String assess) {
        if(mOrderDetailBean.getGoods_data() == null || mOrderDetailBean.getGoods_data().isEmpty()){
            mRootView.showMessage("该订单中没有可评价的商品，请联系客服");
            return;
        }

        ShopBean shopBean = mOrderDetailBean.getGoods_data().get(0);
        String value = "";
        if(PayUtils.VIP.equals(mOrderDetailBean.getType())
                || PayUtils.YAMI.equals(mOrderDetailBean.getType())
                || PayUtils.YAMI_RESERVE.equals(mOrderDetailBean.getType())
                || PayUtils.VIDEO.equals(mOrderDetailBean.getType())){
            value = shopBean.getCode();
        }else if(PayUtils.SKILL_VIDEO.equals(mOrderDetailBean.getType())){
            value = shopBean.getProduct_id() + "";
        }else if(PayUtils.VOCATION_VIDEO.equals(mOrderDetailBean.getType())){
            value = shopBean.getProduct_id() + "";
        }

        mModel.addAssess(mOrderDetailBean.getType(), mOrderDetailBean.getSkill_id(), value, assess, mOrderDetailBean.getSource_type(),  mOrderDetailBean.getOrder_id()+"")
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            getOrderDetail();
                            mRootView.refresh();
                        }
                    }
                });
    }

    /**支付
     * @param payType 支付类型,参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void pay(String payType) {
        Observable<BaseResponse<Object>> observable;
        if(mOrderDetailBean.getIs_group() == 0) {
            //单独购买
            observable = mModel.singlePay(payType, mOrderId);
        }else {
            //拼团
            observable = mModel.groupPay(payType, mOrderId);
        }

        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<Object> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().toString());
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mPayListener.onPaySuccess();
                            }
                        }
                    }
                });
    }

    public String getPayGoodName(){
        if(mOrderDetailBean.getGoods_data() == null || mOrderDetailBean.getGoods_data().size() == 0){
            mRootView.showMessage("没有商品信息，无法调起支付");
            return "";
        }
        ShopBean shopBean =  mOrderDetailBean.getGoods_data().get(0);

        if(PayUtils.CHAPTER.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }else if(PayUtils.REAL_PAPER.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }else if(PayUtils.VIP.equals(mOrderDetailBean.getType())){
            return String.format("【VIP题库】%s", shopBean.getName());

        }else if(PayUtils.YAMI.equals(mOrderDetailBean.getType())){
            return String.format("【考前押密】%s", shopBean.getName());

        }else if(PayUtils.VIDEO.equals(mOrderDetailBean.getType())){
            if(shopBean.getVideo_type() == 2){
                return String.format("【%s】%s", "串讲视频", shopBean.getName());
            }else if(shopBean.getVideo_type() == 3){
                return String.format("【%s】%s", "直播密训班", shopBean.getName());
            }else if(shopBean.getVideo_type() == 4){
                return String.format("【%s】%s", "直播特训班", shopBean.getName());
            }else {
                return String.format("【%s】%s", "精讲视频", shopBean.getName());
            }

        }else if(PayUtils.SUPER_VIP.equals(mOrderDetailBean.getType())){
            return "超级VIP";

        }else if(PayUtils.CLASS_ROOM.equals(mOrderDetailBean.getType())){
            return String.format("【VIP课堂】%s", shopBean.getName());

        }else if(PayUtils.FRUIT_COIN.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }else if(PayUtils.TUITION.equals(mOrderDetailBean.getType())){
            return "缴纳学费";

        }else if(PayUtils.MEMBERSHIP.equals(mOrderDetailBean.getType())){
            return "笔果折扣卡";

        }else if(PayUtils.BOOK.equals(mOrderDetailBean.getType())){
            return "自考图书";

        }else if(PayUtils.HOME_ENGLISH_TWO.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }else if(PayUtils.SKILL_VIDEO.equals(mOrderDetailBean.getType())){
            return String.format("【职业技能】%s", shopBean.getName());

        }else if(PayUtils.VOCATION_VIDEO.equals(mOrderDetailBean.getType())){
            return String.format("【职场提升】%s", shopBean.getName());

        }else if(PayUtils.HIGH_FREQUENCY.equals(mOrderDetailBean.getType())){
            return String.format("【高频考点】%s", shopBean.getName());

        }else if(PayUtils.INTERNET_STUDY.equals(mOrderDetailBean.getType())){
            return String.format("【自考网络助学】%s", shopBean.getName());

        }else if(PayUtils.DEDUCTION_CARD.equals(mOrderDetailBean.getType())){
            return String.format("【自习室抵扣卡】%s", shopBean.getName());

        }else if(PayUtils.COUNSELLING_DAZIKAO.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }else if(PayUtils.COUNSELLING_ZIXUAN.equals(mOrderDetailBean.getType())){
            return shopBean.getName();

        }
        return "";
    }

    /**关闭倒计时
     *
     */
    public void closeTimer(){
        if(mCloseTimer != null){
            mCloseTimer.cancel();
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
        closeTimer();
    }
}
