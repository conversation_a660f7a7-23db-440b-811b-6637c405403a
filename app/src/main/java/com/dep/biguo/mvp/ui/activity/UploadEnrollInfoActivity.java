package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.NestedScrollView;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.picture.CompressEngine;
import com.biguo.utils.picture.CropEngine;
import com.biguo.utils.picture.GlideEngine;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.biguo.utils.util.TintDrawableUtil;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dep.biguo.R;
import com.dep.biguo.bean.UploadEnrollInfoBean;
import com.dep.biguo.bean.UploadEnrollInfoImageBean;
import com.dep.biguo.databinding.UploadEnrollInfoActivityBinding;
import com.dep.biguo.di.component.DaggerUploadEnrollInfoComponent;
import com.dep.biguo.mvp.contract.UploadEnrollInfoContract;
import com.dep.biguo.mvp.presenter.UploadEnrollInfoPresenter;
import com.dep.biguo.mvp.ui.adapter.UploadEnrollInfoImageAdapter;
import com.dep.biguo.utils.AESEncrypt;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.utils.PictureSelectorPermission;
import com.dep.biguo.utils.RSAEncrypt;
import com.dep.biguo.widget.AlignGridItemDecoration;
import com.dep.biguo.widget.ItemDecoration;
import com.dep.biguo.widget.ToolBar;
import com.google.gson.reflect.TypeToken;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;
import com.luck.picture.lib.basic.PictureSelectionModel;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.entity.MediaExtraInfo;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.MediaUtils;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UploadEnrollInfoActivity extends BaseActivity<UploadEnrollInfoPresenter> implements UploadEnrollInfoContract.View, BaseQuickAdapter.OnItemClickListener  {
    public static final String NAME = "name";//姓名
    public static final String IDENTITY_CARD_NUMBER = "identity_card_number";//身份证号
    public static final String PORTRAIT = "portrait";//证件照
    public static final String IDENTITY_CARD_FRONT = "identity_card_front";//身份证正面
    public static final String IDENTITY_CARD_REVERSE = "identity_card_reverse";//身份证反面
    public static final String ACADEMIC_CERTIFICATE = "academic_certificate";//学历
    public static final String ACADEMIC_CERTIFICATE_PROOF = "academic_certificate_proof";//注册备案表/验证报告
    public static final String SOCIAL_SECURITY_CARD_FRONT = "social_security_card_front";//社保卡正面
    public static final String SOCIAL_SECURITY_CARD_REVERSE = "social_security_card_reverse";//社保卡反面
    public static final String RESIDENCE_PERMIT_FRONT = "residence_permit_front";//居住证正面
    public static final String RESIDENCE_PERMIT_REVERSE = "residence_permit_reverse";//居住证反面
    public static final String SOCIAL_SECURITY_PAY_PROOF = "social_security_pay_proof";//社保缴纳证明
    public static final String OTHERS = "others";//其他证件

    private UploadEnrollInfoActivityBinding binding;
    private UploadEnrollInfoImageAdapter IDPhotoAdapter = new UploadEnrollInfoImageAdapter(new ArrayList<>());
    private UploadEnrollInfoImageAdapter IDCardAdapter = new UploadEnrollInfoImageAdapter(new ArrayList<>());
    private UploadEnrollInfoImageAdapter certificateAdapter = new UploadEnrollInfoImageAdapter(new ArrayList<>());
    private UploadEnrollInfoImageAdapter unLocalAdapter = new UploadEnrollInfoImageAdapter(new ArrayList<>());
    private UploadEnrollInfoImageAdapter otherAdapter = new UploadEnrollInfoImageAdapter(new ArrayList<>());

    private String aeskey;

    public static void Start(Context context){
        Intent intent = new Intent(context, UploadEnrollInfoActivity.class);
        context.startActivity(intent);
    }
    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerUploadEnrollInfoComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.upload_enroll_info_activity);

        //适配夜间模式
        int tintColor = ResourcesCompat.getColor(getResources(), R.color.tblack, getTheme());
        TintDrawableUtil.StartTintDrawable(binding.hintView, tintColor);

        RecyclerView[] recyclerViews = new RecyclerView[]{binding.IDPhotoRecyclerView, binding.IDCardRecyclerView
                , binding.certificateRecyclerView, binding.unLocalRecyclerView, binding.otherRecyclerView};
        UploadEnrollInfoImageAdapter[] adapters = new UploadEnrollInfoImageAdapter[]{IDPhotoAdapter, IDCardAdapter
                , certificateAdapter, unLocalAdapter, otherAdapter};

        for(int i=0;i<recyclerViews.length;i++){
            RecyclerView recyclerView = recyclerViews[i];
            UploadEnrollInfoImageAdapter adapter = adapters[i];
            //若屏幕宽度小于三倍item的宽度，则平均分布
            if(DisplayHelper.getWindowWidth(this) <= DisplayHelper.dp2px(this, 155 * 3)) {
                recyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) recyclerView.getLayoutParams();
                layoutParams.width = DisplayHelper.getWindowWidth(this) - layoutParams.getMarginStart() - layoutParams.getMarginEnd();
            }else {
                recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Vertical)
                        .addSkipDraw(2)//第三个item不需要添加分割线
                        .addSkipDraw(4)//第五个item不需要添加分割线
                        .setSize(10)
                        .setColorRes(R.color.tran));
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) recyclerView.getLayoutParams();
                layoutParams.width = LinearLayout.LayoutParams.WRAP_CONTENT;
            }
            //设置适配器
            recyclerView.setAdapter(adapter);
            //添加item的点击事件
            adapter.setOnItemClickListener(this);
        }

        //滚动时，关闭软键盘
        binding.scrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            binding.inputNameView.clearFocus();
            binding.inputIDCardView.clearFocus();
            KeyboardUtils.hideKeyboard(binding.inputNameView);
            KeyboardUtils.hideKeyboard(binding.inputIDCardView);
        });

        binding.commitView.setOnClickListener(v -> {
            Map<String, String> urlMap = new HashMap<>();
            String encryptAesKey;
            try {
                encryptAesKey = URLEncoder.encode(RSAEncrypt.encryptByPublicKey(aeskey), "utf-8");
            } catch (GeneralSecurityException e) {
                showMessage("加密失败");
                return;
            } catch (UnsupportedEncodingException e) {
                showMessage("编码失败");
                return;
            }
            String name = binding.inputNameView.getText().toString();
            String IDCard = binding.inputIDCardView.getText().toString();
            urlMap.put(NAME, TextUtils.isEmpty(name) ? "" : AESEncrypt.encrypt(name, aeskey));
            urlMap.put(IDENTITY_CARD_NUMBER, TextUtils.isEmpty(IDCard) ? "" : AESEncrypt.encrypt(IDCard, aeskey));
            urlMap.putAll(getUploadImgUrl(IDPhotoAdapter));
            urlMap.putAll(getUploadImgUrl(IDCardAdapter));
            urlMap.putAll(getUploadImgUrl(certificateAdapter));
            urlMap.putAll(getUploadImgUrl(unLocalAdapter));
            urlMap.putAll(getUploadImgUrl(otherAdapter));
            LogUtil.d("dddd", urlMap);
            mPresenter.uploadInfo(urlMap, encryptAesKey);
        });
        return 0;
    }

    public Map<String, String> getUploadImgUrl(UploadEnrollInfoImageAdapter imageAdapter){
        Map<String, String> urlMap = new HashMap<>();
        for(UploadEnrollInfoImageBean imageBean : imageAdapter.getData()){
            urlMap.put(imageBean.getName(), imageBean.getEncryptUrl());
        }
        return urlMap;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText("报名资料")
                .build();
        //生成加密上传信息的AES key
        aeskey = AESEncrypt.randomKey();

        IDPhotoAdapter.setNewData(getInitImage(1));
        IDCardAdapter.setNewData(getInitImage(2));
        certificateAdapter.setNewData(getInitImage(3));
        unLocalAdapter.setNewData(getInitImage(4));
        otherAdapter.setNewData(getInitImage(5));

        mPresenter.getData();
    }

    public List<UploadEnrollInfoImageBean> getInitImage(int type){
        int[] defaultImages = null;
        String[] imageName = null;
        if(type == 1){
            //证件照
            defaultImages = new int[]{R.drawable.id_photo};
            imageName = new String[]{PORTRAIT};
        }else if(type == 2){
            //身份证正反面
            defaultImages = new int[]{R.drawable.id_card_front, R.drawable.id_card_back};
            imageName = new String[]{IDENTITY_CARD_FRONT, IDENTITY_CARD_REVERSE};
        }else if(type == 3){
            //学历证书
            defaultImages = new int[]{R.drawable.certificate, R.drawable.record_report};
            imageName = new String[]{ACADEMIC_CERTIFICATE, ACADEMIC_CERTIFICATE_PROOF};
        }else if(type == 4){
            //异地证明
            defaultImages = new int[]{R.drawable.social_security_card_front, R.drawable.social_security_card_back
                    ,R.drawable.residence_permit_front, R.drawable.residence_permit_back
                    , R.drawable.social_security_report};
            imageName = new String[]{SOCIAL_SECURITY_CARD_FRONT, SOCIAL_SECURITY_CARD_REVERSE
                    , RESIDENCE_PERMIT_FRONT, RESIDENCE_PERMIT_REVERSE
                    , SOCIAL_SECURITY_PAY_PROOF};
        }else if(type == 5){
            //其它
            defaultImages = new int[]{R.drawable.other_card};
            imageName = new String[]{OTHERS};
        }
        List<UploadEnrollInfoImageBean> list = new ArrayList<>();
        if(defaultImages != null) {
            for (int defaultImage : defaultImages) {
                UploadEnrollInfoImageBean imageBean = new UploadEnrollInfoImageBean();
                imageBean.setName(imageName[list.size()]);
                imageBean.setDefaultImage(defaultImage);
                imageBean.setEncryptUrl("");
                imageBean.setUrl("");
                list.add(imageBean);
            }
        }
        return list;
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        UploadEnrollInfoImageBean imageBean = (UploadEnrollInfoImageBean) adapter.getItem(position);
        if(!TextUtils.isEmpty(imageBean.getUrl()) && !TextUtils.isEmpty(imageBean.getEncryptUrl())){
            //查看大图
            ImageActivity.start(this, imageBean.getUrl());

        }else if(TextUtils.isEmpty(imageBean.getUrl()) && !TextUtils.isEmpty(imageBean.getEncryptUrl())){
            showMessage("请等待图片加载完成");

        }else {
            selectImage(adapter, position);
        }
    }

    public void selectImage(BaseQuickAdapter adapter, int position){
        //选择图片
        PictureSelectionModel selectionModel = PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setSelectionMode(SelectModeConfig.SINGLE)
                .setImageEngine(GlideEngine.createGlideEngine())
                .setCompressEngine(new CompressEngine())
                .setPermissionsInterceptListener(PictureSelectorPermission.create());
        if (adapter == IDPhotoAdapter) {
            CropEngine cropEngine = new CropEngine()
                    .withAspectRatio(480, 640)
                    .setCropOutputPathDir(getCacheDir().getPath())
                    .setCropOutputFileName(getHandleImgFileName("crop_" + System.currentTimeMillis()));
            selectionModel.setCropEngine(cropEngine);
        }
        selectionModel.forResult(new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(ArrayList<LocalMedia> result) {
                for (LocalMedia media : result) {
                    if (media.getWidth() == 0 || media.getHeight() == 0) {
                        if (PictureMimeType.isHasImage(media.getMimeType())) {
                            MediaExtraInfo imageExtraInfo = MediaUtils.getImageSize(UploadEnrollInfoActivity.this, media.getPath());
                            media.setWidth(imageExtraInfo.getWidth());
                            media.setHeight(imageExtraInfo.getHeight());
                        }
                    }
                    UploadEnrollInfoImageBean imageBean = (UploadEnrollInfoImageBean) adapter.getItem(position);
                    //保存需要展示的图片地址
                    imageBean.setUrl(AppUtil.isEmpty(AppUtil.isEmpty(media.getCompressPath(), media.getCutPath()), media.getRealPath()));
                    //保存加密图片的地址
                    imageBean.setEncryptUrl(encryptFile(imageBean.getUrl()).getPath());
                    //刷新
                    adapter.notifyItemChanged(position);
                }
            }

            @Override
            public void onCancel() {

            }
        });
    }

    /**对本地图片加密
     * @param path 本地图片路径
     * @return 加密后的本地文件路径
     */
    public File encryptFile(String path){
        try {
            String encryptPath = getCacheDir().getPath();
            String encryptName = getHandleImgFileName(path.substring(path.lastIndexOf("/")+1, path.lastIndexOf(".")));
            LogUtil.d("dddd",aeskey);
            File encryptFile = AESEncrypt.encryptFile(path, encryptPath, encryptName, aeskey);
            if(encryptFile == null) {
                showMessage("图片加密失败");
                return null;
            }
            return encryptFile;
        }catch (Exception e){
            showMessage("获取图片出错");
            return null;
        }
    }

    @Override
    public void getDataSuccess(UploadEnrollInfoBean infoBean) {
        binding.inputNameView.setText(infoBean.getName());
        binding.inputIDCardView.setText(infoBean.getIdentity_card_number());
        Map<String, String> map = GsonUtils.fromJson(GsonUtils.toJson(infoBean), new TypeToken<Map<String, String>>(){}.getType());
        insertUrl(IDPhotoAdapter, map, infoBean.getKey());
        insertUrl(IDCardAdapter, map, infoBean.getKey());
        insertUrl(certificateAdapter, map, infoBean.getKey());
        insertUrl(unLocalAdapter, map, infoBean.getKey());
        insertUrl(otherAdapter, map, infoBean.getKey());
    }

    /**将后台返回的数据改变成自定义的数据
     * @param imageAdapter 适配器
     * @param map
     * @param aesKey
     */
    public void insertUrl(UploadEnrollInfoImageAdapter imageAdapter, Map<String, String> map, String aesKey){
        for(int i=0;i<map.size() && i<imageAdapter.getItemCount();i++){
            UploadEnrollInfoImageBean imageBean = imageAdapter.getItem(i);
            imageBean.setEncryptUrl(map.get(imageBean.getName()));
        }
        imageAdapter.setAesKey(aesKey);
        imageAdapter.notifyDataSetChanged();
    }

    @Override
    public Context getContext() {
        return this;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //把加密、解密、裁剪的缓存文件删掉
        File[] files = getCacheDir().listFiles();
        if(files != null) {
            for (File childFile : files) {
                if (childFile.getName().startsWith("upload")) {
                    childFile.delete();
                }
            }
        }
    }

    public static String getHandleImgFileName(String name){
        return "upload_" + name + ".jpg";
    }
}