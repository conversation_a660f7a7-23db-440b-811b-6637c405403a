package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.os.Handler;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.MyGroupBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.di.component.DaggerSkillMyGroupComponent;
import com.dep.biguo.mvp.contract.SkillMyGroupContract;
import com.dep.biguo.mvp.presenter.SkillMyGroupPresenter;
import com.dep.biguo.mvp.ui.activity.GroupDetailActivity;
import com.dep.biguo.mvp.ui.activity.HtmlVideoActivity;
import com.dep.biguo.mvp.ui.adapter.MyGroupAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.UrlAddParamUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.widget.ItemDecoration;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.widget.loadsir.EmptyContentCallBack;
import com.dep.biguo.widget.loadsir.ErrorCallBack;
import com.dep.biguo.widget.loadsir.LoadingCallBack;
import com.dep.biguo.widget.loadsir.NetworkCallBack;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.kingja.loadsir.callback.Callback;
import com.kingja.loadsir.core.LoadService;
import com.kingja.loadsir.core.LoadSir;

import org.simple.eventbus.Subscriber;

import java.net.UnknownHostException;
import java.util.List;

import javax.inject.Inject;

import butterknife.BindView;

public class SkillMyGroupFragment extends BaseFragment<SkillMyGroupPresenter> implements SkillMyGroupContract.View , SwipeRefreshLayout.OnRefreshListener, BaseQuickAdapter.RequestLoadMoreListener {
    @BindView(R.id.swipeLayout) SwipeRefreshLayout swipeLayout;
    @BindView(R.id.recyclerView) RecyclerView recyclerView;

    @Inject MyGroupAdapter mMyGroupAdapter;

    private LoadService mLoadService;
    private LoadingDialog loadingDialog;

    /**跳转到我要拼团页面
     */
    public static SkillMyGroupFragment getInstance(String goodsType, int skill_id){
        Bundle bundle = new Bundle();
        bundle.putString(StartFinal.GOODS_TYPE, goodsType);
        bundle.putInt(StartFinal.SKILL_ID, skill_id);
        SkillMyGroupFragment fragment = new SkillMyGroupFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerSkillMyGroupComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);

    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.skill_my_group_activity, container,false);
        mLoadService = LoadSir.getDefault().register(view, (Callback.OnReloadListener) v -> mPresenter.initRefresh());
        return mLoadService.getLoadLayout();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        mPresenter.init(getArguments());

        swipeLayout.setOnRefreshListener(this);

        recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal).setSize(10));
        mMyGroupAdapter.bindToRecyclerView(recyclerView);
        mMyGroupAdapter.setOnLoadMoreListener(this,recyclerView);
        mMyGroupAdapter.setOnItemClickListener((adapter, view, position) -> {
            MyGroupBean item = mMyGroupAdapter.getItem(position);
            if(item.getStatus() == 3 && item.getIs_open() == 0){
                ToastUtils.show("该"+mMyGroupAdapter.getGoodsTypeName(item)+"已过期");
                return;
            }
            GroupDetailActivity.start(getActivity(), item.getGroup_id(), item.getType(), item.getStatus(), item.getIs_newcomers());
        });

        mMyGroupAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            MyGroupBean item = mMyGroupAdapter.getItem(position);
            if(item.getStatus() == 1){//拼团中
                showShareDialog(item);

            }else if(item.getStatus() == 2 || item.getStatus() == 4){//拼团失败 或 拼团取消
                //GroupLaunchActivity.start(getContext(), item.getType(), item.getCode());
                if(!MainAppUtils.checkLogin(getContext())) return;
                //groupID传0表示发起拼团
                mPresenter.getDiscountCard(item);

            }else if(item.getStatus() == 3){//拼团成功
                HtmlVideoActivity.start(getContext(), item.getCode(), item.getType(), item.getSource_type(), 0, item.getProduct_id(), "");
            }
        });
    }

    @Override
    public void setData(@Nullable Object data) {

    }

    /**显示分享弹窗
     * 当有自己发起的拼团，则链接包含拼团ID，其他用户打开这个分享链接，需要调用{@link GroupGoodInfoFragment#showJoinGroupDialog(GroupBean)}方法
     */
    public void showShareDialog(MyGroupBean detailBean) {
        String params =  "action=" + StartFinal.GROUP
                + "&r_id=" + (UserCache.getUserCache() == null ? 0 : UserCache.getUserCache().getUser_id())
                + "&group_id=" + detailBean.getGroup_id()
                + "&type=" + detailBean.getType()
                + "&code=" + detailBean.getCode()
                + "&skill_id=" + mPresenter.getSkill_id()
                + "&professions_id=" + UserCache.getProfession().getId()
                + "&product_id=" + detailBean.getVideo_type();
        String url = String.format("%s?%s", Constant.SHARE_GROUP, UrlAddParamUtil.encode(params));
        LogUtil.d("dddd", url);

        new ShareDialog.Builder(getContext())
                .setShareTitle(detailBean.getGroup_share_text())
                .setShareContent(detailBean.getGroup_share_desc())
                .setShareIcon(R.drawable.group_share_icon)
                .setShareUrl(url)
                .setOnShareListener((type) -> {
                    new UmengEventUtils(getContext())
                            .addParams("path", "技能证拼团详情：我的拼团-》邀请好友")
                            .addParams("platform", type)
                            .pushEvent(UmengEventUtils.CLICK_SHARE_APP);
                })
                .builder().show();
    }

    @Override
    public void showPayDialog(List<DiscountBean> discountList, MyGroupBean payGroupBean){
        String price;
        if(payGroupBean.getIs_newcomers() == StartFinal.YES){
            price = TextUtils.isEmpty(payGroupBean.getNewcomers_price()) ? "0" : payGroupBean.getNewcomers_price();
        }else {
            price = UserCache.isMemberShip() ? payGroupBean.getMember_group_price() : payGroupBean.getGroup_price();
        }
        new DiscountPayDialog.Builder(getContext(), payGroupBean.getType())
                .setGoodsName(String.format("【%s】%s", mPresenter.getGoodTypeName(payGroupBean.getType()), payGroupBean.getName()))
                .setPrice(Float.parseFloat(payGroupBean.getGroup_price()))
                .setPayPrice(Float.parseFloat(price))
                .setDiscountList(discountList)
                .setOnPayListener((joinGroup, discount, payType) -> mPresenter.pay(payGroupBean.getCode(), payType, payGroupBean, discount))
                .setShowGuobi(false)
                .build()
                .show();
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void showLoading() {
        mLoadService.showCallback(LoadingCallBack.class);
    }

    @Override
    public void hideLoading() {
        mLoadService.showSuccess();
    }

    //接受一个拼团详情发出的拼团通知进行刷新
    @Override
    public void onRefresh() {
        mPresenter.refresh();
    }

    //接受一个拼团详情发出的拼团通知进行刷新
    @Subscriber(tag = EventBusTags.GROUP_STATUS_CHANGE)
    public void groupStatusChange(int status){
        onRefresh();
    }

    @Override
    public void finishRefresh(){
        swipeLayout.setRefreshing(false);
    }

    @Override
    public void onLoadMoreRequested() {
        mPresenter.loadMore();
    }

    @Override
    public void showErrorView(Throwable e) {
        if (e instanceof UnknownHostException)
            mLoadService.showCallback(NetworkCallBack.class);
        else
            mLoadService.showCallback(ErrorCallBack.class);

        TextView refreshView = mLoadService.getLoadLayout().findViewById(R.id.refreshView);
        refreshView.setOnClickListener(view -> mPresenter.initRefresh());
    }

    @Override
    public void showEmptyView() {
        mLoadService.showCallback(EmptyContentCallBack.class);
    }

    @Override
    public void onResume() {
        super.onResume();
        if(mMyGroupAdapter.getData().size() > 0){
            swipeLayout.setRefreshing(true);
            onRefresh();
        }
    }

    @Override
    public void paySuccess(String groupId, String goodsType, int newcomers) {
        mPresenter.refresh();
        //发起拼团，给后台2秒的支付回调时间，2秒后跳到拼团详情
        if(loadingDialog == null) {
            loadingDialog = new LoadingDialog(getContext());
        }
        loadingDialog.show();

        new Handler().postDelayed(() -> {
            if(loadingDialog != null) {
                loadingDialog.dismiss();
            }
            GroupDetailActivity.start(getActivity(), groupId, mPresenter.getSkill_id(), goodsType, 1, newcomers);
        }, 2000);
    }

    @Override
    public void payCancel() {

    }
}