package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import androidx.databinding.DataBindingUtil;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.dep.biguo.R;
import com.dep.biguo.bean.SkillBean;
import com.dep.biguo.databinding.SkillTypeVideoActivityBinding;
import com.dep.biguo.mvp.ui.adapter.ViewPager2Adapter;
import com.dep.biguo.mvp.ui.fragment.SkillTypeVideoFragment;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.widget.ToolBar;
import com.google.android.material.tabs.TabLayoutMediator;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;

import java.util.ArrayList;
import java.util.List;

public class SkillAllTypeVideoActivity extends BaseActivity{
    private static final String CERTS = "certs";
    private SkillTypeVideoActivityBinding binding;

    private List<BaseFragment> fragments = new ArrayList<>();
    private List<SkillBean.ScrollBean> certs;

    public static void Start(Context context, List<SkillBean.ScrollBean> certs){
        Intent intent = new Intent(context, SkillAllTypeVideoActivity.class);
        intent.putExtra(CERTS, GsonUtils.toJson(certs));
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {

    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.skill_type_video_activity);
        new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText("视频")
                .build();
        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        certs = GsonUtils.fromJson(getIntent().getStringExtra(CERTS), new TypeToken<List<SkillBean.ScrollBean>>(){}.getType());
        for(SkillBean.ScrollBean scrollBean : certs) {
            fragments.add(SkillTypeVideoFragment.newInstance(scrollBean.getSkill_id(), scrollBean.getSource_type()));
        }
        //初始化tabLayout
        binding.viewPager.setOffscreenPageLimit(6);
        binding.viewPager.setAdapter(new ViewPager2Adapter<>(getSupportFragmentManager(), getLifecycle(), fragments));
        TabLayoutMediator tabLayoutMediator = new TabLayoutMediator(binding.tabLayout, binding.viewPager, false, true, (tab, position) ->{
            tab.setText(certs.get(position).getName());
        });
        //这句话很重要，viewPager与tabLayout绑定
        tabLayoutMediator.attach();
    }

}