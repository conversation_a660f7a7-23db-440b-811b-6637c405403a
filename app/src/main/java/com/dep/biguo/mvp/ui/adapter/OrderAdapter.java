package com.dep.biguo.mvp.ui.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.AppUtil;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.mvp.ui.activity.OrderDetailActivity;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayUtils;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;

public class OrderAdapter extends CommonAdapter<OrderBean> {
    private int listType;//订单类型

    public static final int CLOSE = -1;//已关闭
    public static final int CANCEL = -2;//已取消
    public static final int WAIT_PAY = 1;//待付款
    public static final int WAIT_SEND = 2;//待发货
    public static final int WAIT_SIGN = 3;//待收货
    public static final int ORDER_FINISH = 4;//已完成

    private ConfirmShopAdapter confirmShopAdapter;

    public OrderAdapter(@Nullable List<OrderBean> data) {
        super(R.layout.item_indent, data);
    }

    @Override
    protected void convert(BaseViewHolder holder, final OrderBean item) {
        confirmShopAdapter = new ConfirmShopAdapter(item.getGoods_data());
        confirmShopAdapter.setOrderType(item.getType());
        confirmShopAdapter.setGoodsType(item.getType());
        confirmShopAdapter.setRefundState(item.getRefund_status());
        confirmShopAdapter.setBuy(item.getRefund_status() == 0 && item.getState() == OrderDetailActivity.ORDER_FINISH);
        RecyclerView goodsRecyclerView = holder.getView(R.id.goodsRecyclerView);
        goodsRecyclerView.setAdapter(confirmShopAdapter);

        holder.setImageDrawable(R.id.typeImageView, getTypeTitleDrawable(holder.itemView.getContext()));
        holder.setText(R.id.tv_type_name, item.getName());
        holder.setText(R.id.tv_price, String.format("实付 ¥%s", item.getTotal_fee()));

        setStateView(holder);
        setScholarship(holder, item);

        //垃圾框架，被嵌套的RecyclerView不需要点击事件，也拦截了点击事件。需要调用外层的监听对象中转一下
        confirmShopAdapter.setOnItemClickListener((adapter, view, position) -> setOnItemClick(view, holder.getAbsoluteAdapterPosition()));
    }

    /**根据订单类型返回对应的图标
     * @return
     */
    private Drawable getTypeTitleDrawable(Context context){
        int iconResId;
        if(listType == PayUtils.BOOK_LIST){
            iconResId = R.drawable.order_book_image;
        }else if(listType == PayUtils.CONSUMPTION_LIST){
            iconResId = R.drawable.order_consumption_image;
        }else {
            iconResId = R.drawable.order_service_image;
        }
        Drawable drawable = ResourcesCompat.getDrawable(context.getResources(), iconResId, context.getTheme());
        drawable.setBounds(0,0,drawable.getMinimumWidth(),drawable.getMinimumHeight());
        return drawable;
    }

    /**
     * 待付款：联系客服 取消订单 立即付款
     * 待发货：联系客服
     * 待收货：联系客服 查看物流 确认收货
     * 已完成：联系客服 我要评价(是VIP、yami、video且未评价才显示) 查看物流 删除订单
     * 已关闭：删除订单
     */
    private void setStateView(BaseViewHolder holder) {
        //给按钮添加点击事件，并给全都按钮都恢复成默认设置，后面再根据订单类型和状态设置按钮的显示或隐藏
        holder.setGone(R.id.tv_cancel, false).addOnClickListener(R.id.tv_cancel)
                .setGone(R.id.tv_pay, false).addOnClickListener(R.id.tv_pay)
                .setGone(R.id.tv_path, false).addOnClickListener(R.id.tv_path)
                .setGone(R.id.tv_confirm, false).addOnClickListener(R.id.tv_confirm)
                .setGone(R.id.tv_delete, false).addOnClickListener(R.id.tv_delete)
                .setGone(R.id.tv_customer, true).addOnClickListener(R.id.tv_customer)
                .setGone(R.id.tv_assess, false).addOnClickListener(R.id.tv_assess)
                .setGone(R.id.tv_receipt, false).addOnClickListener(R.id.tv_receipt)
                .setGone(R.id.tv_internet_info, false).addOnClickListener(R.id.tv_internet_info)
                .setGone(R.id.tv_graduate_progress_view, false).addOnClickListener(R.id.tv_graduate_progress_view);

        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        if(item.getState() == WAIT_PAY) {
            waitPayState(holder);

        }else if(item.getState() == WAIT_SEND) {
            waitSendState(holder);

        }else if(item.getState() == WAIT_SIGN) {
            waitSignState(holder);

        }else if(item.getState() == ORDER_FINISH) {
            orderFinishState(holder);

        }else if(item.getState() == CLOSE || item.getState() == CANCEL) {
            orderCloseOrCancelState(holder);
        }

    }

    public void setScholarship(BaseViewHolder holder, OrderBean item){
        if(!AppUtil.isEmpty(item.getGoods_data())){
            ShopBean.Scholarship scholarship = item.getGoods_data().get(0).getScholarship();
            if(scholarship == null) {
                holder.setGone(R.id.scholarshipLayout, false);

            }else {
                holder.setGone(R.id.scholarshipLayout, scholarship.getStatus() > 2);
                if (scholarship.getStatus() == 3) {
                    holder.setText(R.id.scholarshipEnterView, "填报成绩");
                } else if (scholarship.getStatus() == 4) {
                    holder.setText(R.id.scholarshipEnterView, "查看排名");
                } else {
                    holder.setText(R.id.scholarshipEnterView, "");
                }
            }
        }else {
            holder.setGone(R.id.scholarshipLayout, false);
        }
        holder.addOnClickListener(R.id.scholarshipLayout);
    }

    /**待支付状态
     *
     */
    private void waitPayState(BaseViewHolder holder){
        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        holder.setText(R.id.tv_state_payment, getOrderStatusText(item));
        holder.setGone(R.id.tv_cancel, true);
        holder.setGone(R.id.tv_pay, true);
    }

    /**待发货状态
     *
     */
    private void waitSendState(BaseViewHolder holder){
        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        holder.setText(R.id.tv_state_payment, getOrderStatusText(item));
    }

    /**待确认收货状态
     *
     */
    private void waitSignState(BaseViewHolder holder){
        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        holder.setText(R.id.tv_state_payment, getOrderStatusText(item));
        holder.setGone(R.id.tv_path, true);
        holder.setGone(R.id.tv_confirm, true);
    }

    /**订单已完成状态
     *
     */
    private void orderFinishState(BaseViewHolder holder){
        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        //只有这些商品类型可以显示评价按钮
        List<String> assessList = Arrays.asList(PayUtils.VIP, PayUtils.YAMI, PayUtils.YAMI_RESERVE, PayUtils.VIDEO,
                PayUtils.VOCATION_VIDEO, PayUtils.SKILL_VIDEO, PayUtils.INTERNET_STUDY);
        //网络助学的个人信息填写状态
        List<String> internetStateList = Arrays.asList("完善报考信息", "修改报考信息", "查看报考信息");

        holder.setText(R.id.tv_state_payment, getOrderStatusText(item));
        holder.setGone(R.id.tv_assess, assessList.contains(item.getType()) && item.getIs_discuss() == 0);
        holder.setGone(R.id.tv_path, listType == PayUtils.BOOK_LIST);

        holder.setGone(R.id.tv_internet_info, PayUtils.INTERNET_STUDY.equals(item.getType()));
        holder.setText(R.id.tv_internet_info, internetStateList.get(item.getExam_info_status()));

        holder.setGone(R.id.tv_graduate_progress_view, PayUtils.GRADUATION_AGENCY.equals(item.getType()));
        if( PayUtils.GRADUATION_AGENCY.equals(item.getType())){
            boolean visibility = item.getIs_confirm_receipt() == 1;
            holder.setGone(R.id.tv_confirm, visibility);
        }
    }

    /**订单已取消或已关闭状态
     *
     */
    private void orderCloseOrCancelState(BaseViewHolder holder){
        OrderBean item = getItem(holder.getAbsoluteAdapterPosition());
        holder.setText(R.id.tv_state_payment, getOrderStatusText(item));
        holder.setGone(R.id.tv_customer, false);
        holder.setGone(R.id.tv_delete, true);
    }

    /**获取订单状态的文字提示
     *
     * @return 文字提示
     */
    private String getOrderStatusText(OrderBean item){
        if(item.getOrder_id() == 3053424){

        }
        if(item.getRefund_status() == 1){
            return "退款申请受理中";
        }else if(item.getRefund_status() == 2){
            return "退款申请已驳回";
        }else if(item.getRefund_status() == 3){
            return "订单已退款";
        }else if(item.getRefund_status() == 4){
            return "退款申请异常";
        }else if(item.getRefund_status() == 5){
            return "订单退款中";
        }else if(item.getRefund_status() == 6){
            return "订单退款失败";
        }else if(item.getRefund_status() == 0){
            if(item.getState() == OrderDetailActivity.WAIT_PAY){
                //待支付状态
                return "等待买家付款";

            }else if(item.getState() == OrderDetailActivity.WAIT_SEND && PayUtils.BOOK.equals(item.getType())){
                //待发货状态根据订单的类型返回提示文案
                return "等待卖家发货";

            }else if(item.getState() == OrderDetailActivity.WAIT_SEND && item.getIs_group() == StartFinal.YES){
                //待发货状态根据订单的类型返回提示文案
                return "正在拼团中";

            }else if(item.getState() == OrderDetailActivity.WAIT_SIGN){
                //待收货，只有图书订单有这个状态
                return "等待买家收货";

            }else if(item.getState() == OrderDetailActivity.ORDER_FINISH){
                //已完成状态
                if(item.getIs_group() == StartFinal.YES){
                    return "拼团已完成";
                }
                //已完成状态
                return "订单已完成";

            }else if(item.getState() == OrderDetailActivity.CLOSE){
                //已关闭状态
                return "订单已关闭";
            }else if(item.getState() == OrderDetailActivity.CANCEL){
                //已关闭状态
                return "拼团已取消";
            }
        }
        return "";
    }

    public String getInvoiceStatus(OrderBean item){
        if(item.getInvoice_status() == 1){
            return "申请开票";
        }else if(item.getInvoice_status() == 2){
            return "发票审核中";
        }else if(item.getInvoice_status() == 3){
            return "再次申请开票";
        }else if(item.getInvoice_status() == 4){
            return "发票开具中";
        }else if(item.getInvoice_status() == 5){
            return "查看发票";
        }else if(item.getInvoice_status() == 6){
            return "再次申请开票";
        }else {
            return "";
        }
    }

    public void setListType(int listType) {
        this.listType = listType;
    }
}
