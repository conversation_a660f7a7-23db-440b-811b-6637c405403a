package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.ArticleBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.CourseBean;
import com.dep.biguo.bean.IconBean;
import com.dep.biguo.bean.ZkHomeBean;
import com.dep.biguo.bean.ZkNewHomeBean;
import com.dep.biguo.mvp.contract.HomeContract;
import com.dep.biguo.mvp.ui.activity.MainActivity;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.mmkv.CacheInterfaceData;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.annotations.NonNull;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class HomePresenter extends BasePresenter<HomeContract.Model, HomeContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private List<ArticleBean> examNewList;//考试资讯列表
    private List<ArticleBean> schoolNewList;//学校新闻列表

    /*本地使用的常量*/
    public static final int EXAM_NEW = 1;//考试资讯
    public static final int SCHOOL_NEW = 2;//学校新闻

    private ZkNewHomeBean homeBean;

    private CourseBean courseBean;

    @Inject
    public HomePresenter(HomeContract.Model model, HomeContract.View rootView) {
        super(model, rootView);
    }

    public void getCacheHomeData(){
        //在未请求到数据时，先使用缓存数据展示首页
        try {
            homeBean = CacheInterfaceData.getZkHomeBean();
            setHomeData(homeBean);
        }catch (Exception e){
            CacheInterfaceData.cacheZkHomeBean(null);
            LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
        }
    }

    public void getHomeCourse(String code, boolean isShow){
        if(!TextUtils.isEmpty(UserCache.getHomeCode())){
            getHomeData(code, isShow);
            return;
        }
        //请求服务器
        mModel.getHomeCourse(code, true, UserCache.getProvince().getId(), UserCache.getProfession().getId(), 1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable ->{if(isShow) mRootView.showLoadingDialog();})
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CourseBean.CourseItemBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CourseBean.CourseItemBean>> response) {
                        if (response.isSuccess()) {
                            if(!AppUtil.isEmpty(response.getData())) {
                                getHomeData(response.getData().get(0).getCode(), isShow);
                            }else {
                                ToastUtils.show("无可选择的课程");
                            }
                        }else {
                            ToastUtils.show("获取首页课程失败");
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        ToastUtils.show("获取首页课程失败");
                    }
                });
    }

    /**获取首页数据
     *
     */
    public void getHomeData(String code, boolean isShow) {
        if(homeBean == null){
            getCacheHomeData();
        }
        if(TextUtils.isEmpty(code) && UserCache.getHomeShowEnrollCourse()){
            getHomeCourse(code, isShow);
            return;
        }
        //刷新的情况下，清除已缓存的新闻
        examNewList = null;
        schoolNewList = null;
        mModel.getHomeData(code, true, UserCache.getProvince().getId(), UserCache.getProfession().getId(), 1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable ->{if(isShow) mRootView.showLoadingDialog();})
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() ->mRootView.hideLoadingDialog())
                .subscribe(new ErrorHandleSubscriber<ZkNewHomeBean>(mErrorHandler) {
                    @Override
                    public void onNext(@NonNull ZkNewHomeBean zkHomeBean) {
                        try {
                            if (zkHomeBean.isSuccess()) {

                                //刷新之后，可能课程有变，所以清空一下请求到的课程
                                mRootView.closeShowSelectCourse();
                                courseBean = null;

                                homeBean = zkHomeBean.getData();
                                if(homeBean == null) return;

                                //遇到微信群，且没有加入折扣卡，就用折扣卡替换一下，方便过审，如果两个都有，那就不替换了，超出处理范围
                                IconBean wxGroupIcon = null;
                                boolean hasMember = false;
                                for(IconBean icon : homeBean.getIcon2()) {
                                    if(TextUtils.equals(icon.getName(), "微信群")){
                                        wxGroupIcon = icon;
                                    }
                                    if(TextUtils.equals(icon.getType(), "member")){
                                        hasMember = true;
                                    }
                                }
                                if (wxGroupIcon != null && !hasMember && !UserCache.isVersionReview()) {
                                    wxGroupIcon.setName("折扣卡");
                                    wxGroupIcon.setType("member");
                                    wxGroupIcon.setImg("https://cdn.biguotk.com/img/20210918095339d8937b64d9b030c5d121b5a592022f8c.png");
                                    wxGroupIcon.setTarget_url("");
                                    wxGroupIcon.setNeed_login(1);
                                    wxGroupIcon.setEnable(1);
                                    wxGroupIcon.setTip_img("");
                                    wxGroupIcon.setXcx_path("");
                                }

                                try {
                                    setHomeData(homeBean);
                                    CacheInterfaceData.cacheZkHomeBean(homeBean);//缓存首页数据
                                }catch (Exception e){
                                    LogUtil.e("dddd", Arrays.toString(e.getStackTrace()), e);
                                }
                            }else {
                                mRootView.refreshFinish();
                                mRootView.showMessage("首页刷新失败");
                            }
                        }catch (Exception e){

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        mRootView.refreshFinish();
                        try {
                            if(AppManager.getAppManager().getCurrentActivity() instanceof MainActivity) {
                                mRootView.showMessage("首页刷新失败");
                            }
                        }catch (Exception e){
                            LogUtil.d("dddd", e);
                        }
                    }
                });
    }


    /**获取是否有可用的优惠券
     *
     */
    public void getDiscountStatus(){
        if(UserCache.getUserCache() == null) {
            mRootView.isShowDiscountDrag2View(0);
            return;
        }

        mModel.getDiscountStatus()
                .subscribeOn(Schedulers.io()).subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Map<String, Object>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NonNull BaseResponse<Map<String, Object>> mapBaseResponse) {
                        if(mapBaseResponse.getData() != null && mapBaseResponse.getData().containsKey("coupon_left_time")) {
                            try {
                                double expireTime = (double) mapBaseResponse.getData().get("coupon_left_time");
                                mRootView.isShowDiscountDrag2View((int) expireTime);
                            }catch (Exception e){
                                mRootView.isShowDiscountDrag2View(0);
                                LogUtil.d("dddd", "强转失败");
                            }
                        }else {
                            mRootView.isShowDiscountDrag2View(0);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        LogUtil.d("dddd", "获取优惠券失败");
                    }
                });
    }

    public void setHomeData(ZkNewHomeBean homeBean){
        if(homeBean == null) return;

        //需要弹出报考弹窗，并点击了课程，若条件满足，首页就必须要一个选中的课程，
        //但是切换专业之后又不知道选哪个课程，所以第一次获取到首页课程，就要从课程列表中选出第一个课程作为默认课程，请求首页数据
        if(UserCache.getHomeShowEnrollCourse() && TextUtils.isEmpty(homeBean.getCode())){
            if(!AppUtil.isEmpty(homeBean.getCourses())){
                getHomeData(homeBean.getCourses().get(0).getCode(), true);
                return;
            }
        }

        examNewList = homeBean.getFindings();//保存考试资讯数据，当切换新闻时，无需再请求服务器

        UserCache.cacheHomeCode(homeBean.getCode());
        mRootView.setHomeData(homeBean);
    }

    public void clearCourse(){
        courseBean = null;
    }


    public void getCourse(){
        if(courseBean != null){
            mRootView.getCourseSuccess(courseBean);
            return;
        }
        //请求服务器
        mModel.getCourse(UserCache.getProfession().getId(), UserCache.getSchool().getId(), true)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<CourseBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<CourseBean> response) {
                        if (response.isSuccess()) {
                            courseBean = response.getData();
                            //后台的课程数据
                            mRootView.getCourseSuccess(response.getData());
                        }
                    }
                });
    }

    /**管理报考
     *
     */
    public void manageCourse(List<CourseBean.CourseItemBean> formList, CourseBean.CourseItemBean join, CourseBean.CourseItemBean againJoin, CourseBean.CourseItemBean cancel) {
        String joinCourseId = join == null ? "" : (join.getCourses_id()+"");
        String againJoinCourseId = againJoin == null ? "" : (againJoin.getCourses_id()+"");
        String cancelCourseId = cancel == null ? "" : (cancel.getCourses_id()+"");

        if(UserCache.getUserCache() == null) return;

        mModel.manageCourse(joinCourseId, againJoinCourseId, cancelCourseId)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        //同步到服务器不需要等服务器响应
                        if(response.isSuccess()) {
                            homeBean.setIs_join(1);
                            if(join != null) {
                                formList.remove(join);
                                courseBean.getCourses_joined().add(join);
                                mRootView.manageCourseSuccess();
                            }else if(againJoin != null){
                                formList.remove(againJoin);
                                courseBean.getCourses_joined().add(againJoin);
                                againJoin.setScore(0);
                                mRootView.manageCourseSuccess();
                            }else {
                                formList.remove(cancel);
                                courseBean.getCourses_not_joined().add(cancel);
                                mRootView.manageCourseSuccess();
                            }
                        }
                    }
                });
    }

    public void setScore(CourseBean.CourseItemBean courseItemBean, String score){
        if(TextUtils.isEmpty(score)){
            mRootView.showMessage("请输入成绩");
            return;
        }

        mModel.setScore(courseItemBean.getCourses_id(), score)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse baseResponse) {
                        if(baseResponse.isSuccess()){
                            try {
                                courseItemBean.setScore(Float.parseFloat(score));
                            }catch (NumberFormatException e){
                                mRootView.showMessage("成绩格式错误");
                            }

                            //埋点统计输入成绩
                            new UmengEventUtils(mRootView.getContext())
                                    .addParams("write_score", score)
                                    .pushEvent(UmengEventUtils.INPUT_SCORE);

                            if(courseItemBean.getScore() >= courseBean.getPass_score()) {
                                if(courseBean.getCourses_joined().contains(courseItemBean)) {
                                    courseBean.getCourses_joined().remove(courseItemBean);

                                }else if(courseBean.getCourses_not_joined().contains(courseItemBean)){
                                    courseBean.getCourses_not_joined().remove(courseItemBean);
                                }
                                courseBean.getCourses_passed().add(courseItemBean);
                            }

                            mRootView.manageCourseSuccess();
                        }
                    }
                });
    }

    public void getCircle() {
        mModel.getCircle(1)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                //因为进入首页要弹出登录界面，导致生命周期使得请求关闭了，因此设置该请求不受生命周期影响,但有可能mRootView为空，需要抓取异常
                //.compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CircleBean.Moment>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CircleBean.Moment>> s) {
                        try {
                            if (s.isSuccess()) {
                                if(AppUtil.isEmpty(s.getData())){
                                    mRootView.getCircleDataSuccess(new ArrayList<>());
                                }else {
                                    mRootView.getCircleDataSuccess(s.getData());

                                }
                            }else {
                                mRootView.getCircleDataFail();
                            }
                        }catch (Exception e){

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        try {
                            mRootView.getCircleDataFail();
                        }catch (Exception e){

                        }
                    }
                });
    }

    /**点赞/转发 计数
     * @param moment 被点赞的动态
     * @param relay 0点赞,1转发
     */
    public void momentGoodOrShare(CircleBean.Moment moment, int relay) {
        mModel.momentGoodOrShare(moment.getPosts_id(), relay, 1 - moment.getIs_like())
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(relay == 1){
                            mRootView.shareSuccess(moment);
                            mRootView.showMessage("分享成功");
                        }
                    }
                });
    }
    /**删除动态
     * @param moment 动态
     */
    public void deleteMoment(CircleBean.Moment moment) {
        mModel.deleteMoment(moment.getPosts_id())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(s.isSuccess()) {
                            mRootView.deleteComment(moment);
                            mRootView.showMessage("删除成功");
                        }
                    }
                });
    }

    /**举报动态
     * @param post_id 动态ID
     * @param reason 举报原因
     */
    public void reportMoment(int post_id, String reason) {
        mModel.reportMoment(post_id, reason)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(s.isSuccess()) {
                            mRootView.showMessage("举报成功");
                        }
                    }
                });
    }

    /**获取新闻列表
     * @param type {@link HomePresenter#EXAM_NEW,HomePresenter#SCHOOL_NEW}
     */
    public void getNewList(int type){
        if(type == EXAM_NEW){//考试资讯在获取首页数据时一并返回了，无需再请求
            mRootView.setNewsData(examNewList);

        }else if(type == SCHOOL_NEW && schoolNewList != null){//当学校新闻请求过一次时，同样无需再请求
            mRootView.setNewsData(schoolNewList);

        }else {
            mModel.getNewList(UserCache.getProvince().getId(), 1, 3)
                    .subscribeOn(Schedulers.io())
                    .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                    .subscribeOn(AndroidSchedulers.mainThread())
                    .observeOn(AndroidSchedulers.mainThread())
                    .doFinally(() -> mRootView.hideLoadingDialog())
                    .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                    .subscribe(new ErrorHandleSubscriber<BaseResponse<List<ArticleBean>>>(mErrorHandler) {
                        @Override
                        public void onNext(@NonNull BaseResponse<List<ArticleBean>> response) {
                            if (response.isSuccess()) {
                                schoolNewList = response.getData();
                                mRootView.setNewsData(schoolNewList);
                            }
                        }

                        @Override
                        public void onError(Throwable t) {
                            super.onError(t);
                            mRootView.showErrorView(t);
                        }
                    });
        }
    }

    /**仅计算资讯已读，新闻在调用查看详情的接口时才计数
     * @param articleBean 点击的资讯
     */
    public void redNews(ArticleBean articleBean){
        mModel.redNews(articleBean.getId())
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(@NonNull BaseResponse response) {
                        if (response.isSuccess()) {
                            articleBean.setPageview(articleBean.getPageview() + 1);
                            mRootView.notifyNews();
                        }
                    }
                });

    }

    /**返回banner图列表
     * @param type 1顶部banner图，2笔果辅导班banner图
     * @return
     */
    public List<String> getBannerImgList(ZkNewHomeBean zkHomeBean, int type){
        List<String> list = new ArrayList<>();
        List<ZkNewHomeBean.Banner> checkList;
        if(type == 1){
            checkList = zkHomeBean.getSwipers();
        }else{
            checkList = zkHomeBean.getTop_banner();
        }
        if(checkList != null) {
            for (ZkNewHomeBean.Banner banner : checkList) {
                list.add(banner.getImg());
            }
        }
        return list;
    }

    /**返回banner图对应的数据
     * @param type 1顶部banner图，2笔果辅导班banner图
     * @param position banner图的下标
     * @return
     */
    public ZkNewHomeBean.Banner getBannerBean(int type, int position){
        List<ZkNewHomeBean.Banner> checkList = (type == 1 ? CacheInterfaceData.getZkHomeBean().getSwipers() : CacheInterfaceData.getZkHomeBean().getTop_banner());
        return checkList.get(position);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
