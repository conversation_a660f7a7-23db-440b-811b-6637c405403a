package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.biguo.utils.util.DisplayHelper;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.bean.UserFragmentBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.CkUserFragmentBinding;
import com.dep.biguo.di.component.DaggerCKUserComponent;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.CKUserContract;
import com.dep.biguo.mvp.presenter.CKUserPresenter;
import com.dep.biguo.mvp.ui.activity.CKScoreActivity;
import com.dep.biguo.mvp.ui.activity.CKStudydataActivity;
import com.dep.biguo.mvp.ui.activity.DownloadManageActivity;
import com.dep.biguo.mvp.ui.activity.FeedbackActivity;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.activity.MessageCenterActivity;
import com.dep.biguo.mvp.ui.activity.MyActivity;
import com.dep.biguo.mvp.ui.activity.MyCouponActivity;
import com.dep.biguo.mvp.ui.activity.MyVideoActivity;
import com.dep.biguo.mvp.ui.activity.OrderListActivity;
import com.dep.biguo.mvp.ui.activity.RechargeV2Activity;
import com.dep.biguo.mvp.ui.activity.SearchUrlActivity;
import com.dep.biguo.mvp.ui.activity.SettingActivity;
import com.dep.biguo.mvp.ui.activity.UserinfoActivity;
import com.dep.biguo.mvp.ui.activity.ZkSettingActivity;
import com.dep.biguo.mvp.ui.adapter.UserCourseAdapter;
import com.dep.biguo.mvp.ui.adapter.UserOtherAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.ShareUtil;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.UmengEventUtils;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.widget.AlignGridItemDecoration;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.umeng.analytics.MobclickAgent;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;

import butterknife.BindView;
import butterknife.OnClick;

import static com.jess.arms.utils.Preconditions.checkNotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CKUserFragment extends BaseFragment<CKUserPresenter> implements CKUserContract.View, View.OnClickListener {
    private CkUserFragmentBinding binding;

    public static CKUserFragment newInstance() {
        CKUserFragment fragment = new CKUserFragment();
        return fragment;
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        DaggerCKUserComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.ck_user_fragment, container, false);
        binding.setOnClickListener(this);

        initCourseRecyclerView();
        initOtherRecyclerView();

        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
    }

    private void initCourseRecyclerView() {
        List<UserCourseAdapter.Bean> courseList = new ArrayList<>();
        courseList.add(new UserCourseAdapter.Bean(true, "我的题库", R.drawable.wodetiku));
        courseList.add(new UserCourseAdapter.Bean(true, "我的视频", R.drawable.wodeshipin));
        courseList.add(new UserCourseAdapter.Bean(true, "我的订单", R.drawable.wpdedingdan));
        courseList.add(new UserCourseAdapter.Bean(true, "押密退款", R.drawable.ckyamituikuang));
        binding.courseRecyclerView.setItemAnimator(null);
        binding.courseRecyclerView.addItemDecoration(new AlignGridItemDecoration(AlignGridItemDecoration.ALIGN_SIDES));
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) binding.courseRecyclerView.getLayoutParams();
        layoutParams.width = DisplayHelper.getWindowWidth(getContext()) - layoutParams.getMarginStart() - layoutParams.getMarginEnd();
        UserCourseAdapter courseAdapter = new UserCourseAdapter(getContext(), courseList);
        courseAdapter.bindToRecyclerView(binding.courseRecyclerView);
        courseAdapter.setOnItemClickListener((adapter, view, position) -> {
            if(!MainAppUtils.checkLogin(getContext())) return;

            UserCourseAdapter.Bean clickBean = courseAdapter.getItem(position);
            if(clickBean.name.equals("我的题库")){//我的题库
                MyActivity.start(getActivity(), 0);
            }else if(clickBean.name.equals("我的视频")){//我的视频
                MyVideoActivity.start(getActivity());
            }else if(clickBean.name.equals("我的订单")){//我的订单
                OrderListActivity.start(getContext(), false);
            }else if(clickBean.name.equals("押密退款")){//押密退款
                HtmlActivity.start(getActivity(), Constant.YAMI_CK_REFUND);
            }
        });

        courseAdapter.notifyChange();
    }

    private void initOtherRecyclerView() {
        List<UserOtherAdapter.Bean> otherList = new ArrayList<>();
        otherList.add(new UserOtherAdapter.Bean(true, "优惠券", R.drawable.ck_user_item_coupon));
        otherList.add(new UserOtherAdapter.Bean(true, "综合查询", R.drawable.ck_user_item_query));
        otherList.add(new UserOtherAdapter.Bean(true, "学籍资料", R.drawable.ck_user_item_student_info));
        otherList.add(new UserOtherAdapter.Bean(true, "成绩管理", R.drawable.ck_user_item_secore));
        otherList.add(new UserOtherAdapter.Bean(true, "下载管理", R.drawable.ck_user_item_download));
        otherList.add(new UserOtherAdapter.Bean(true, "分享好友", R.drawable.ck_user_item_share));
        otherList.add(new UserOtherAdapter.Bean(true, "意见反馈", R.drawable.ck_user_item_feedback));
        UserOtherAdapter otherAdapter = new UserOtherAdapter();
        otherAdapter.setNewData(otherList);
        otherAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            UserOtherAdapter.Bean clickBean = otherAdapter.getItem(i);

            //先判断是否已登录
            String[] mustLoginArray = {"优惠券","学籍资料","成绩管理","意见反馈"};
            if(Arrays.asList(mustLoginArray).contains(clickBean.name) && !MainAppUtils.checkLogin(getContext())) return;

            if(clickBean.name.equals("优惠券")){//优惠券
                ArmsUtils.startActivity(MyCouponActivity.class);

            }else if(clickBean.name.equals("综合查询")){//综合查询
                ArmsUtils.startActivity(SearchUrlActivity.class);

            }else if(clickBean.name.equals("学籍资料")){//学籍资料
                ArmsUtils.startActivity(CKStudydataActivity.class);

            }else if(clickBean.name.equals("成绩管理")){//成绩管理
                ArmsUtils.startActivity(CKScoreActivity.class);

            }else if(clickBean.name.equals("下载管理")){//下载管理
                ArmsUtils.startActivity(DownloadManageActivity.class);

            }else if(clickBean.name.equals("分享好友")){//分享好友
                showShareDialog();

            }else if(clickBean.name.equals("意见反馈")){//意见反馈
                ArmsUtils.startActivity(FeedbackActivity.class);
            }
        });
        binding.otherRecyclerView.setAdapter(otherAdapter);
    }

    @Override
    public void setData(@Nullable Object o) {
        if(UserCache.getUserCache() != null) {
            binding.nicknameView.setText(UserCache.getUserCache().getNickname());
            binding.guobiView.setText(UserCache.getUserCache().getFruit_coin());
        }
    }


    @Override
    public void onResume() {
        super.onResume();
        mPresenter.getUserFragmentInfo();
    }

    public void showShareDialog(){
        new ShareDialog.Builder(getActivity())
                .setOnShareListener((type) -> {
                    new UmengEventUtils(getContext())
                            .addParams("path", "APP：成考-》我的-》分享好友")
                            .addParams("platform", type)
                            .pushEvent(UmengEventUtils.CLICK_SHARE_APP);
                })
                .builder()
                .show();
    }

    @Override
    public void onClick(View view) {
        if(view == binding.settingView){//设置
            ArmsUtils.startActivity(ZkSettingActivity.class);

        }else if(view == binding.notifyView){//通知消息
            ArmsUtils.startActivity(MessageCenterActivity.class);

        }else if(view == binding.nicknameView || view == binding.avatarView || view == binding.editInfoView){//个人信息
            if (!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(UserinfoActivity.class);

        }else if(view == binding.guobiView){//果币
            if (!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(RechargeV2Activity.class);

        }
    }

    @Override
    public void getUserFragmentInfoSuccess(UserFragmentBean bean) {
        binding.nicknameView.setText(bean.getNickname());
        binding.guobiView.setText(bean.getFruit_coin());
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    private void getLoginPushCount(){
        mPresenter.getMessageUnreadCount(UserCache.getUserCache().getUser_id(), DeviceCache.getUmengDeviceToken());
    }

    @Subscriber(tag = EventBusTags.LOGOUT_SUCCESS)
    private void logoutSuccess(UserBean user) {
        binding.nicknameView.setText("未登录");
        binding.guobiView.setText("0");
        mPresenter.getMessageUnreadCount(0, DeviceCache.getUmengDeviceToken());
    }

    @Override
    @Subscriber(tag = EventBusTags.PUSH_MESSAGE_RECEIVER)
    public void getMessageUnreadCountSuccess(int count) {
        //消息数量
        binding.badgeCount.setText(String.format("%s", count > 99 ? "99+" : count));
        binding.badgeCount.setVisibility(count > 0 ? View.VISIBLE : View.GONE);
        EventBus.getDefault().post(count, EventBusTags.SYSTEM_UNREAD_MESSAGE_COUNT);
    }


    @Override
    public void showMessage(@NonNull String s) {
        ToastUtils.show(s);
    }

}
