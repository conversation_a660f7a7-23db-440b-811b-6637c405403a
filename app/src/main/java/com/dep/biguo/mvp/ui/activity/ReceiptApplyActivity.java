package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.OrderDetailBean;
import com.dep.biguo.bean.ReceiptTitleBean;
import com.dep.biguo.databinding.ReceiptApplyActivityBinding;
import com.dep.biguo.di.component.DaggerReceiptApplyComponent;
import com.dep.biguo.dialog.ReceiptTitleListDialog;
import com.dep.biguo.mvp.contract.ReceiptApplyContract;
import com.dep.biguo.mvp.presenter.ReceiptApplyPresenter;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.hjq.toast.ToastUtils;
import com.jess.arms.di.component.AppComponent;

import java.util.List;

public class ReceiptApplyActivity extends BaseLoadSirActivity<ReceiptApplyPresenter> implements ReceiptApplyContract.View, View.OnClickListener {
    private static final String ORDER_NUMBER = "order_number";
    private static final String COVER_IMAGE = "coverImage";
    private static final String TOTAL_FEE = "total_fee";
    private static final String INVOICE_STATUS = "invoice_status";

    private ReceiptApplyActivityBinding binding;
    private ReceiptTitleListDialog titleListDialog;

    private String order_number;
    private String coverImage;
    private String total_fee;
    private int invoice_status;
    private ReceiptTitleBean checkTitleBean;
    private ActivityResultLauncher<Intent> launcher;

    public static void Start(Context context, OrderBean orderBean){
        Intent intent = new Intent(context, ReceiptApplyActivity.class);
        intent.putExtra(ORDER_NUMBER, orderBean.getOrder_number());
        if(!AppUtil.isEmpty(orderBean.getGoods_data())) {
            intent.putExtra(COVER_IMAGE, orderBean.getGoods_data().get(0).getImg());
        }else {
            intent.putExtra(COVER_IMAGE, "");
        }
        intent.putExtra(TOTAL_FEE, orderBean.getTotal_fee());
        intent.putExtra(INVOICE_STATUS, orderBean.getInvoice_status());
        context.startActivity(intent);
    }

    public static void Start(Context context, OrderDetailBean orderBean){
        Intent intent = new Intent(context, ReceiptApplyActivity.class);
        intent.putExtra(ORDER_NUMBER, orderBean.getOrder_number());
        if(!AppUtil.isEmpty(orderBean.getGoods_data())) {
            intent.putExtra(COVER_IMAGE, orderBean.getGoods_data().get(0).getImg());
        }else {
            intent.putExtra(COVER_IMAGE, "");
        }
        intent.putExtra(TOTAL_FEE, orderBean.getTotal_fee());
        intent.putExtra(INVOICE_STATUS, orderBean.getInvoice_status());
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerReceiptApplyComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.receipt_apply_activity);
        binding.setOnClickListener(this);
        new NormalToolbarUtil(this)
                .setCenterText("申请开票");

        //没有选中抬头时，就隐藏一下无关控件
        checkTitle(null);

        launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if (result.getResultCode() != RESULT_OK) return;
            mPresenter.getReceiptTitleList(true);
        });
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        order_number = getIntent().getStringExtra(ORDER_NUMBER);
        coverImage = getIntent().getStringExtra(COVER_IMAGE);
        total_fee = getIntent().getStringExtra(TOTAL_FEE);
        invoice_status = getIntent().getIntExtra(INVOICE_STATUS, 0);

        binding.orderNumberView.setText(order_number);
        binding.priceView.setText(String.format("¥%s", total_fee));
        ImageLoader.loadImage(binding.goodsCoverView, coverImage);
    }

    @Override
    public void onRequest() {
        mPresenter.getReceiptTitleList(false);
    }

    public void showPerson(){
        if(checkTitleBean == null) return;

        binding.invoiceTypeView.setRightText(getReceiptType(checkTitleBean));
        binding.invoiceTitleView.setRightText(checkTitleBean.getName());
        binding.titleTypeView.setRightText(getTitleType(checkTitleBean));
    }

    public void showCompany(){
        if(checkTitleBean == null) return;

        binding.invoiceTypeView.setRightText(getReceiptType(checkTitleBean));
        binding.invoiceTitleView.setRightText(checkTitleBean.getName());
        binding.titleTypeView.setRightText(getTitleType(checkTitleBean));
        binding.taxNumberView.setRightText(checkTitleBean.getTax_number());
        binding.registerBankView.setRightText(checkTitleBean.getBank());
        binding.bankNumberView.setRightText(checkTitleBean.getBank_account());
        binding.companyAddressView.setRightText(checkTitleBean.getAddress());
        binding.companyCallView.setRightText(checkTitleBean.getPhone());
    }


    private String getReceiptType(ReceiptTitleBean receiptTitleBean){
        if(receiptTitleBean.getInvoice_type() == 1){
            return "专用发票-电子";
        }else if(receiptTitleBean.getInvoice_type() == 2){
            return "普通发票-电子";
        }else {
            return "未知";
        }
    }

    private String getTitleType(ReceiptTitleBean receiptTitleBean){
        if(receiptTitleBean.getType() == 1){
            return "企业抬头";
        }else if(receiptTitleBean.getType() == 2){
            return "个人或事业单位抬头";
        }else {
            return "未知";
        }
    }
    @Override
    public void onClick(View view) {
        if(view == binding.invoiceTitleView){
            showTitleDialog();

        }else if(view == binding.commitView){
            if(checkTitleBean == null){
                ToastUtils.show("请先选择发票类型");
                return;
            }
            //是否申请过发票
            int is_again_apply = invoice_status != 1 ? 1 : 0;
            mPresenter.commitReceiptApply(checkTitleBean.getId(), order_number, is_again_apply);
        }
    }

    public void checkTitle(ReceiptTitleBean select){
        checkTitleBean = select;
        if(checkTitleBean == null){
            binding.companyInfoLayout.setVisibility(View.GONE);
            binding.invoiceTypeView.setVisibility(View.GONE);
            binding.titleTypeView.setVisibility(View.GONE);

        }else if(checkTitleBean.getType() == 1){
            binding.companyInfoLayout.setVisibility(View.VISIBLE);
            binding.invoiceTypeView.setVisibility(View.VISIBLE);
            binding.titleTypeView.setVisibility(View.VISIBLE);
            showCompany();

        }else {
            binding.companyInfoLayout.setVisibility(View.GONE);
            binding.invoiceTypeView.setVisibility(View.VISIBLE);
            binding.titleTypeView.setVisibility(View.VISIBLE);
            showPerson();
        }
    }

    public void clearTitle(){
        checkTitle(null);
        binding.invoiceTypeView.setRightText("");
        binding.invoiceTitleView.setRightText("");
        binding.titleTypeView.setRightText("");
        binding.taxNumberView.setRightText("");
        binding.registerBankView.setRightText("");
        binding.bankNumberView.setRightText("");
        binding.companyAddressView.setRightText("");
        binding.companyCallView.setRightText("");
    }

    public void showTitleDialog(){
        titleListDialog = new ReceiptTitleListDialog(this);
        titleListDialog.setList(mPresenter.getTitleList())
                .setOnSelectListener(new ReceiptTitleListDialog.OnSelectListener() {
                    @Override
                    public void onSelect(ReceiptTitleBean select) {
                        checkTitle(select);
                    }

                    @Override
                    public void onEdit(ReceiptTitleBean select) {
                        ReceiptAddTitleActivity.Start(ReceiptApplyActivity.this, select, launcher);
                    }

                    @Override
                    public void onDel(ReceiptTitleBean select) {
                        new MessageDialog.Builder(getSupportFragmentManager())
                                .setContent("是否删除抬头")
                                .setNegativeText("取消")
                                .setPositiveText("删除")
                                .setPositiveClickListener(v -> mPresenter.delReceiptTitle(select))
                                .builder()
                                .show();
                    }
                }).show();
    }

    @Override
    public void getReceiptTitleListSuccess(List<ReceiptTitleBean> titleList, boolean isReplace) {
        for(ReceiptTitleBean bean : titleList){
            if(checkTitleBean != null && checkTitleBean.getId() == bean.getId()){
                checkTitle(bean);
                break;

            }else if(checkTitleBean == null && bean.getIs_default() == 1){
                checkTitle(bean);
                break;
            }
        }

        if(titleListDialog != null){
            titleListDialog.setList(titleList);
        }
    }

    @Override
    public void delReceiptTitleSuccess(ReceiptTitleBean select) {
        if(checkTitleBean != null && checkTitleBean.getId() == select.getId()){
            clearTitle();
        }

        if(titleListDialog != null){
            titleListDialog.removeTitle(select);
        }
    }

    @Override
    public void commitReceiptApplySuccess() {
        new MessageDialog.Builder(getSupportFragmentManager())
                .setContent("发票申请成功")
                .setPositiveText("确定")
                .setPositiveClickListener(v -> finish())
                .builder()
                .show();
    }
}