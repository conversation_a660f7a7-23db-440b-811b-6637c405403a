package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.R;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.IndentContract;
import com.dep.biguo.mvp.ui.adapter.OrderAdapter;
import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.FragmentEvent;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class IndentPresenter extends BasePresenter<IndentContract.Model, IndentContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    OrderAdapter mAdapter;

    private PayResultListener mPayListener;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;


    @Inject
    public IndentPresenter(IndentContract.Model model, IndentContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        mPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }


    public void initRefresh(){
        requestIndentData(INIT,1);
    }

    public void refresh(){
        requestIndentData(REFRESH,1);
    }

    public void loadMore(){
        requestIndentData(LOAD,mPage+1);
    }

    public void requestIndentData(int action, int page) {
        mModel.getOrder(mRootView.getListType(), mRootView.getState(), page)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {
                    if (action == INIT)
                        mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    if (action == INIT)
                        mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, FragmentEvent.DESTROY_VIEW))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrderBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrderBean>> s) {
                        if (s.isSuccess()) {
                            if (action == INIT || action == REFRESH) {
                                mAdapter.setNewData(s.getData());
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(s.getData()))
                                    mRootView.showEmptyView();
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                mAdapter.addData(s.getData());
                                mAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if (AppUtil.isEmpty(s.getData()))
                                mAdapter.loadMoreEnd();
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(mAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:mAdapter.loadMoreFail();break;
                        }
                    }
                });
    }

    public void cancelOrder(int order_id) {
        mModel.cancelOrder(order_id,mRootView.getListType())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.cancelSuccess();
                        }
                    }
                });
    }

    public void deleteOrder(int order_id) {
        mModel.delete(order_id,mRootView.getListType())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.deleteSuccess();
                        }
                    }
                });
    }

    public void confirmOrder(int order_id) {
        mModel.confirmOrder(order_id,mRootView.getListType())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.confirmSuccess();
                        }
                    }
                });
    }

    /**我要评论
     * @param assess
     */
    public void addAssess(OrderBean orderBean, String assess) {
        LogUtil.d("dddd", orderBean);
        if(orderBean.getGoods_data() == null || orderBean.getGoods_data().isEmpty()){
            mRootView.showMessage("该订单中没有可评价的商品，请联系客服");
            return;
        }
        ShopBean shopBean = orderBean.getGoods_data().get(0);
        StringBuilder value = new StringBuilder();
        if(PayUtils.VIP.equals(orderBean.getType())
                || PayUtils.YAMI.equals(orderBean.getType())
                || PayUtils.YAMI_RESERVE.equals(orderBean.getType())
                || PayUtils.VIDEO.equals(orderBean.getType())){
            value = new StringBuilder(shopBean.getCode());
        }else if(PayUtils.SKILL_VIDEO.equals(orderBean.getType())){
            value = new StringBuilder(shopBean.getProduct_id() + "");
        }else if(PayUtils.VOCATION_VIDEO.equals(orderBean.getType())){
            value = new StringBuilder(shopBean.getProduct_id() + "");
        }else if(PayUtils.INTERNET_STUDY.equals(orderBean.getType())){
            for(ShopBean goods : orderBean.getGoods_data()){
                value.append(goods.getCode()).append("_");
            }
            if(value.length() > 0){
                value.delete(value.length() - 1, value.length());
            }

        }

        mModel.addAssess(orderBean.getType(), orderBean.getSkill_id(), value.toString(), assess, orderBean.getSource_type(),orderBean.getOrder_id()+"", orderBean.getCert_type())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            mRootView.assessSuccess();
                        }
                    }
                });
    }

    /**返回支付弹窗里需要显示的提示语
     *
     */
    public String getPayDialogHint(String orderType){
        String hint = (PayUtils.VIP.equals(orderType)
                || PayUtils.YAMI.equals(orderType)
                || PayUtils.VIDEO.equals(orderType)
                || PayUtils.SUPER_VIP.equals(orderType)
        ) && UserCache.isMemberShip() ? "您已开通笔果折扣卡，当前享受折扣卡优惠\n" : "";

        if(PayUtils.BOOK.equals(orderType)){
            hint += "付款前请认真确认购买信息，避免购买错误";
        }else if(PayUtils.YAMI.equals(orderType)){
            hint += "您将购买虚拟商品，购买后不支持立即退款\n如需退款，请联系客服";
        }else {
            hint += mRootView.getActivity1().getString(R.string.paydialog_hint);
        }
        return hint;
    }


    /**判断是否显示果币支付
     * @return
     */
    public boolean isShowGuobi(String orderType){
        return PayUtils.VIP.equals(orderType)
                || PayUtils.REAL_PAPER.equals(orderType)
                || PayUtils.CHAPTER.equals(orderType)
                || PayUtils.HIGH_FREQUENCY.equals(orderType);
    }

    /**判断是否
     * @return
     */
    public boolean isShowPoint(String orderType, boolean isGroup){
        return PayUtils.VIP.equals(orderType) && !isGroup;
    }


    public void pay(int isGroup, String payType, int order_id) {
        Observable<BaseResponse<Object>> observable;
        if(isGroup == 0) {
            observable = mModel.singlePay(payType, order_id);
        }else {
            observable = mModel.groupPay(payType, order_id);
        }

        observable.subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<Object>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<Object> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity1(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity1(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().toString());
                                PayListenerUtils.getInstance().setListener(mPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }


    public String getPayGoodName(OrderBean selectedOrderBean){
        if(selectedOrderBean.getGoods_data().isEmpty()){
            return "";
        }

        ShopBean shopBean =  selectedOrderBean.getGoods_data().get(0);
        String goodsType = selectedOrderBean.getType();

        if(PayUtils.CHAPTER.equals(goodsType)){
            return shopBean.getName();

        }else if(PayUtils.REAL_PAPER.equals(goodsType)){
            return shopBean.getName();

        }else if(PayUtils.VIP.equals(goodsType)){
            return String.format("【VIP题库】%s", shopBean.getName());

        }else if(PayUtils.YAMI.equals(goodsType)){
            return String.format("【考前押密】%s", shopBean.getName());

        }else if(PayUtils.YAMI_RESERVE.equals(goodsType)){
            return String.format("【预定押密】%s", shopBean.getName());

        }else if(PayUtils.VIDEO.equals(goodsType)){
            if(shopBean.getVideo_type() == 2){
                return String.format("【%s】%s", "串讲视频", shopBean.getName());
            }else if(shopBean.getVideo_type() == 4){
                return String.format("【%s】%s", "直播特训班", shopBean.getName());
            }else {
                return String.format("【%s】%s", "精讲视频", shopBean.getName());
            }

        }else if(PayUtils.SUPER_VIP.equals(goodsType)){
            return "超级VIP";

        }else if(PayUtils.CLASS_ROOM.equals(goodsType)){
            return String.format("【VIP课堂】%s", shopBean.getName());

        }else if(PayUtils.FRUIT_COIN.equals(goodsType)){
            return shopBean.getName();

        }else if(PayUtils.TUITION.equals(goodsType)){
            return "缴纳学费";

        }else if(PayUtils.MEMBERSHIP.equals(goodsType)){
            return "笔果折扣卡";

        }else if(PayUtils.BOOK.equals(goodsType)){
            return "自考图书";

        }else if(PayUtils.HOME_ENGLISH_TWO.equals(goodsType)){
            return shopBean.getName();

        }else if(PayUtils.SKILL_VIDEO.equals(goodsType)){
            return String.format("【职业技能】%s", shopBean.getName());

        }else if(PayUtils.VOCATION_VIDEO.equals(goodsType)){
            return String.format("【职场提升】%s", shopBean.getName());

        }else if(PayUtils.HIGH_FREQUENCY.equals(goodsType)){
            return String.format("【高频考点】%s", shopBean.getName());

        }else if(PayUtils.INTERNET_STUDY.equals(goodsType)){
            return String.format("【自考网络助学】%s", shopBean.getName());

        }else if(PayUtils.DEDUCTION_CARD.equals(goodsType)){
            return String.format("【自习室抵扣卡】%s", shopBean.getName());

        }else if(PayUtils.COUNSELLING_DAZIKAO.equals(goodsType)){
            return shopBean.getName();

        }else if(PayUtils.COUNSELLING_ZIXUAN.equals(goodsType)){
            return shopBean.getName();

        }
        return "";
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        this.mAdapter = null;
        LoadingDialog.hideLoadingDialog();
    }
}
