package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import android.view.View;
import android.widget.ImageView;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.SecretBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.SecretActivityBinding;
import com.dep.biguo.di.component.DaggerSecretComponent;
import com.dep.biguo.dialog.InputEmailDialog;
import com.dep.biguo.dialog.PopupDialog;
import com.dep.biguo.mvp.contract.SecretContract;
import com.dep.biguo.mvp.presenter.SecretPresenter;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.socket.WebSocketConnect;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.wxapi.WxMinApplication;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;

import org.simple.eventbus.Subscriber;

import java.util.HashMap;
import java.util.Map;

public class SecretActivity extends BaseLoadSirActivity<SecretPresenter> implements SecretContract.View {
    public static final String CODE = "code";
    public static final String TITLE = "title";
    public static final String DOWNLOAD_STATUS = "downloadStatus";

    private SecretActivityBinding binding;

    private String mCode;
    private int mDownloadStatus;

    private SecretBean mSecretBean;
    private WebSocketConnect socketConnect;//长链接统计学习时间

    public static void start(Context context, String code, int downloadStatus) {
        Intent intent = new Intent(context, SecretActivity.class);
        intent.putExtra(CODE, code);
        intent.putExtra(DOWNLOAD_STATUS, downloadStatus);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerSecretComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.secret_activity);
        ToolBar toolBar = new ToolBar.Builder(this)
                .setTarget(ToolBar.Builder.TITLE)
                .setText("考前押密")
                .setTarget(ToolBar.Builder.RIGHT)
                .setViewLayout(R.layout.right_toolbar_view)
                .build();

        //设置商品详情按钮
        toolBar.getViewById(R.id.firstView).setOnClickListener(v -> {
            GroupGoodsActivity.start(SecretActivity.this, mCode, StartFinal.YAMI);
        });
        //设置调整字体按钮的图片和点击事件
        ImageView fontView = toolBar.getViewById(R.id.secondView);
        fontView.setImageResource(R.drawable.font_setting);
        fontView.setOnClickListener(v -> {
            new PopupDialog.Builder<>(this, "缩小-", "放大+")
                    .setAlwaysShow(true)
                    .setForeachData(PopupDialog.ForeachData.getForeachDataInstance())
                    .setArrowXAnchor(PopupDialog.ARROW_END)
                    .setOnItemClickListener((itemView, data, position) -> {
                        int size = binding.contentView.getSettings().getTextZoom();
                        size += position == 0 ? -1 : 1;
                        binding.contentView.getSettings().setTextZoom(size);
                    })
                    .build()
                    .showAsDropDown(fontView, 0, -DisplayHelper.dp2px(v.getContext(), 10));
        });
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        mCode = getIntent().getStringExtra(CODE);
        mDownloadStatus = getIntent().getIntExtra(DOWNLOAD_STATUS, 0);

        //未登录状态，不使用长链接
        if(UserCache.getUserCache() != null) {
            socketConnect = new WebSocketConnect();
            socketConnect.create(mCode, 7, 0);
        }
    }

    @Override
    public void onRequest() {
        mPresenter.getSecret();
    }

    @Override
    public void setSecretData(SecretBean data) {
        mSecretBean = data;

        //加载富文本
        binding.contentView.setHtml(data.getA());
        //滚动到上次阅读的位置
        onUpdateScroll();

        showSendEmailDialog();
    }

    /**
     * 弹出领取押密电子档对话框
     */
    private void showSendEmailDialog() {
        if (mSecretBean == null) return;
        if (UserCache.getUserCache() == null) return;

        if (mDownloadStatus == 1) {
            new InputEmailDialog(this)
                    .setTitle("领取押密电子档")
                    .setContent(mSecretBean.getEmail())
                    .setPositiveText("马上领取")
                    .setOnPositiveListener(email -> mPresenter.sendEmail(email))
                    .show();

        }else if(mDownloadStatus == 4){
            new MessageDialog.Builder(getSupportFragmentManager())
                    .setContent("领取失败，请与客服联系")
                    .setNegativeText(null)
                    .setPositiveText("联系客服")
                    .setPositiveClickListener(v -> WxMinApplication.StartWechat(this))
                    .builder()
                    .show();
        }
    }

    @Override
    public String getCode() {
        return mCode;
    }

    long startStudyTime;
    @Override
    protected void onResume() {
        super.onResume();
        if(socketConnect != null){
            socketConnect.resume();
        }
        startStudyTime = System.currentTimeMillis();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if(socketConnect != null) {
            socketConnect.pause();
        }

        long studyDuration = (System.currentTimeMillis() - startStudyTime) / 1000;
        if(studyDuration > 60 && !AppUtil.isEmpty(mCode)) {
            Map<String, Object> map = new HashMap<>();
            map.put("answer_time", studyDuration);
            map.put("mainType", 7);
            map.put("topic_type", 0);
            map.put("code", mCode);
            map.put("cert_type", Constant.ZK.equals(UserCache.getAppType()) ? 0 : (Constant.CK.equals(UserCache.getAppType()) ? 1 : 2));
            map.put("professions_id", UserCache.getProfession().getId());
            mPresenter.commitStudyTime(map);
        }

        //保存滚动进度
        if(UserCache.getUserCache() != null) {
            int userId = UserCache.getUserCache().getUser_id();
            int scrollY = binding.contentView.getScrollY();
            KVHelper.putValue(String.valueOf(userId).concat(mCode), scrollY);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if(socketConnect != null) {
            socketConnect.destroy();
        }
    }

    public void onUpdateScroll() {
        int userId = UserCache.getUserCache().getUser_id();
        int position = KVHelper.getInt(String.valueOf(userId).concat(mCode));
        binding.contentView.scrollTo(0, position);
        showMessage("已跳转到上次浏览位置");
    }

    @Subscriber(tag = EventBusTags.LOGIN_SUCCESS)
    private void loginSuccess(UserBean user) {
        if (mPresenter == null) return;
        new Handler().postDelayed(() -> mPresenter.getSecret(), 200);
    }

    @Override
    public void finish() {
        if (!AppManager.getAppManager().activityClassIsLive(MainActivity.class)
                && !AppManager.getAppManager().activityClassIsLive(CKMainActivity.class)) {
            ArmsUtils.startActivity(MainActivity.class);
        }
        super.finish();
    }

    private static class WatermarkDrawable extends Drawable {
        @Override
        public void draw(@NonNull Canvas canvas) {
            Paint paint = new Paint();
            paint.setColor(Color.parseColor("#80000000"));
            paint.setTextSize(40);
            paint.setAntiAlias(true);
            paint.setAlpha(50); // 设置透明度
            paint.setTextAlign(Paint.Align.LEFT);

            String watermarkText = "Watermark";
            int width = getBounds().width();
            int height = getBounds().height();
            int stepX = 100;  // 水平方向间隔
            int stepY = 100;  // 垂直方向间隔

            // 绘制多个水印
            for (int x = 0; x < width; x += stepX) {
                for (int y = 0; y < height; y += stepY) {
                    canvas.save();
                    canvas.rotate(-45, x, y);  // 旋转水印
                    canvas.drawText(watermarkText, x, y, paint);
                    canvas.restore();
                }
            }
        }

        @Override
        public void setAlpha(int alpha) {
            // 不需要实现
        }

        @Override
        public void setColorFilter(@Nullable ColorFilter colorFilter) {
            // 不需要实现
        }

        @Override
        public int getOpacity() {
            return PixelFormat.TRANSLUCENT;
        }
    }
}
