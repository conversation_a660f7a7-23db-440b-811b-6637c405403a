package com.dep.biguo.di.component;

import dagger.BindsInstance;
import dagger.Component;

import com.jess.arms.di.component.AppComponent;

import com.dep.biguo.di.module.LivePlanListModule;
import com.dep.biguo.mvp.contract.LivePlanListContract;

import com.jess.arms.di.scope.ActivityScope;
import com.dep.biguo.mvp.ui.activity.LivePlanListActivity;

@ActivityScope
@Component(modules = LivePlanListModule.class, dependencies = AppComponent.class)
public interface LivePlanListComponent {
    void inject(LivePlanListActivity activity);

    @Component.Builder
    interface Builder {
        @BindsInstance
        Builder view(LivePlanListContract.View view);

        Builder appComponent(AppComponent appComponent);

        LivePlanListComponent build();
    }
}