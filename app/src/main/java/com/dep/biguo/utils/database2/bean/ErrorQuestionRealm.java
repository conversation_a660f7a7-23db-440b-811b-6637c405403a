package com.dep.biguo.utils.database2.bean;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class ErrorQuestionRealm extends RealmObject {
    @PrimaryKey private String key;
    private String code;
    private String courseName;
    private String courseId;
    private int mainType;
    private String mainTypeName;
    private String chapterId;
    private String chapterName;
    private String chapterSectionId;
    private String chapterSectionName;
    private String truePaperId;
    private String truePaperName;
    private String sumiId;
    private String sumiName;
    private int topic_type;
    private String topic_type_name;

    private int id;
    private String questionAsk;//问题
    private String A;//选项A
    private String B;//选项B
    private String C;//选项C
    private String D;//选项D
    private String E;//选项E
    private String F;//选项F
    private String correctOption;//参考答案
    private String explanation;//题目详解
    private int isCollection;//是否已收藏
    private int mark;//分数
    private String video_parse_cover;//封面图
    private String audio_url;//音频

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public int getMainType() {
        return mainType;
    }

    public void setMainType(int mainType) {
        this.mainType = mainType;
    }

    public String getMainTypeName() {
        return mainTypeName;
    }

    public void setMainTypeName(String mainTypeName) {
        this.mainTypeName = mainTypeName;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getChapterSectionId() {
        return chapterSectionId;
    }

    public void setChapterSectionId(String chapterSectionId) {
        this.chapterSectionId = chapterSectionId;
    }

    public String getChapterSectionName() {
        return chapterSectionName;
    }

    public void setChapterSectionName(String chapterSectionName) {
        this.chapterSectionName = chapterSectionName;
    }

    public String getTruePaperId() {
        return truePaperId;
    }

    public void setTruePaperId(String truePaperId) {
        this.truePaperId = truePaperId;
    }

    public String getTruePaperName() {
        return truePaperName;
    }

    public void setTruePaperName(String truePaperName) {
        this.truePaperName = truePaperName;
    }

    public String getSumiId() {
        return sumiId;
    }

    public void setSumiId(String sumiId) {
        this.sumiId = sumiId;
    }

    public String getSumiName() {
        return sumiName;
    }

    public void setSumiName(String sumiName) {
        this.sumiName = sumiName;
    }

    public int getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(int topic_type) {
        this.topic_type = topic_type;
    }

    public String getTopic_type_name() {
        return topic_type_name;
    }

    public void setTopic_type_name(String topic_type_name) {
        this.topic_type_name = topic_type_name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getQuestionAsk() {
        return questionAsk;
    }

    public void setQuestionAsk(String questionAsk) {
        this.questionAsk = questionAsk;
    }

    public String getA() {
        return A;
    }

    public void setA(String a) {
        A = a;
    }

    public String getB() {
        return B;
    }

    public void setB(String b) {
        B = b;
    }

    public String getC() {
        return C;
    }

    public void setC(String c) {
        C = c;
    }

    public String getD() {
        return D;
    }

    public void setD(String d) {
        D = d;
    }

    public String getE() {
        return E;
    }

    public void setE(String e) {
        E = e;
    }

    public String getF() {
        return F;
    }

    public void setF(String f) {
        F = f;
    }

    public String getCorrectOption() {
        return correctOption;
    }

    public void setCorrectOption(String correctOption) {
        this.correctOption = correctOption;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public int getIsCollection() {
        return isCollection;
    }

    public void setIsCollection(int isCollection) {
        this.isCollection = isCollection;
    }

    public int getMark() {
        return mark;
    }

    public void setMark(int mark) {
        this.mark = mark;
    }

    public String getVideo_parse_cover() {
        return video_parse_cover;
    }

    public void setVideo_parse_cover(String video_parse_cover) {
        this.video_parse_cover = video_parse_cover;
    }

    public String getAudio_url() {
        return audio_url;
    }

    public void setAudio_url(String audio_url) {
        this.audio_url = audio_url;
    }
}
