package com.dep.biguo.bean;


import java.io.Serializable;
import java.util.List;

public class OrganizationRecommendBean implements Serializable {
    private List<Banner> ads;
    private List<Banner> ads2;
    private List<String> dist;
    private List<IconBean> icons;
    private String xcx_path;
    private String url;

    public List<Banner> getAds() {
        return ads;
    }

    public void setAds(List<Banner> ads) {
        this.ads = ads;
    }

    public List<String> getDist() {
        return dist;
    }

    public void setDist(List<String> dist) {
        this.dist = dist;
    }

    public String getXcx_path() {
        return xcx_path;
    }

    public void setXcx_path(String xcx_path) {
        this.xcx_path = xcx_path;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<Banner> getAds2() {
        return ads2;
    }

    public void setAds2(List<Banner> ads2) {
        this.ads2 = ads2;
    }

    public List<IconBean> getIcons() {
        return icons;
    }

    public void setIcons(List<IconBean> icons) {
        this.icons = icons;
    }

    public static class Banner{
        private int id;//ID
        private String name;//名字
        private String img;//图片
        private String target_url;//跳转链接
        private String xcx_path;//小程序路径
        private int need_login;//是否需要登录

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }
    }

    public static class Organization implements Serializable{
        private int id;//机构ID
        private String name;//机构名称
        private String logo;//机构logo
        private String address;//机构地址
        private float km;//与用户的距离
        private String intro;//机构简介
        private String longitude;//精度
        private String latitude;//纬度
        private String province_name;//机构所在省份名称
        private String city_code;//机构所在城市编码
        private String city_name;//机构所在城市名称
        private int is_recommend;
        private int is_apply;//是否支持报名
        private int city_pid;
        private boolean self_study_room;
        private boolean trial_course;
        private int need_login;
        private String target_url;
        private String xcx_path;


        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getIntro() {
            return intro;
        }

        public void setIntro(String intro) {
            this.intro = intro;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public float getKm() {
            return km;
        }

        public void setKm(float km) {
            this.km = km;
        }

        public int getIs_recommend() {
            return is_recommend;
        }

        public void setIs_recommend(int is_recommend) {
            this.is_recommend = is_recommend;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getProvince_name() {
            return province_name;
        }

        public void setProvince_name(String province_name) {
            this.province_name = province_name;
        }

        public String getCity_code() {
            return city_code;
        }

        public void setCity_code(String city_code) {
            this.city_code = city_code;
        }

        public String getCity_name() {
            return city_name;
        }

        public void setCity_name(String city_name) {
            this.city_name = city_name;
        }

        public int getIs_apply() {
            return is_apply;
        }

        public void setIs_apply(int is_apply) {
            this.is_apply = is_apply;
        }

        public int getCity_pid() {
            return city_pid;
        }

        public void setCity_pid(int city_pid) {
            this.city_pid = city_pid;
        }

        public boolean isSelf_study_room() {
            return self_study_room;
        }

        public void setSelf_study_room(boolean self_study_room) {
            this.self_study_room = self_study_room;
        }

        public boolean isTrial_course() {
            return trial_course;
        }

        public void setTrial_course(boolean trial_course) {
            this.trial_course = trial_course;
        }

        public int getNeed_login() {
            return need_login;
        }

        public void setNeed_login(int need_login) {
            this.need_login = need_login;
        }

        public String getTarget_url() {
            return target_url;
        }

        public void setTarget_url(String target_url) {
            this.target_url = target_url;
        }

        public String getXcx_path() {
            return xcx_path;
        }

        public void setXcx_path(String xcx_path) {
            this.xcx_path = xcx_path;
        }
    }
}
