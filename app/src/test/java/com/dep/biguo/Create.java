package com.dep.biguo;

import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Scanner;

/**创建新的armsMvp架构的文件
 * 1.使用Builder构建创建的内容，Builder中的所有属性必须全部赋值
 * 2.调用create()方法，等待创建
 * 3.创建完成后，编译一下项目创建关联文件
 *
 * 注意1：项目移动位置后，需要更改BASE_PATH属性的值
 * 注意2：文件创建失败，可能是需要创建的文件已经存在，目的是防止IO操作覆盖原文件，若确定文件可以被覆盖，则注释掉writeFile()方法创建文件的语句，直接进入写的操作
 * 注意3：delete()方法删错文件，将无法找回
 * */
public class Create {
    private Builder builder;

    public Create(Builder builder) {
        this.builder = builder;
    }

    public void create(){
        if(check()) {
            println("\n开始创建******************************************************************************************", 0);
            writeFile(getCreatePath(componentPath), builder.componentContent);
            writeFile(getCreatePath(modulePath), builder.moduleContent);
            writeFile(getCreatePath(contractPath), builder.contractContent);
            writeFile(getCreatePath(builder.uiType == Builder.ACTIVITY ? activityPath : fragmentPath), builder.uiContent);
            writeFile(getCreatePath(presenterPath), builder.presenterContent);
            writeFile(getCreatePath(modelPath), builder.modelContent);
            println("完成创建******************************************************************************************", 0);
            System.exit(0);
        }
    }


    /**删除文件
     * 删错文件，将无法找回
     */
    public void delete(){
        /*input("确定删除“"+builder.name+"”?", new InputListener() {
            @Override
            public void input() {
                boolean isDelete = new File(getCreatePath(componentPath)).delete()
                        && new File(getCreatePath(modulePath)).delete()
                        && new File(getCreatePath(contractPath)).delete()
                        && new File(getCreatePath(modelPath)).delete()
                        && new File(getCreatePath(presenterPath)).delete()
                        && new File(getCreatePath(builder.uiType == Builder.ACTIVITY ? activityPath : fragmentPath)).delete();

                println(isDelete ? "删除成功":"删除失败", isDelete ? 1:-1);
                System.exit(0);
            }
        });*/
        boolean isDelete = new File(getCreatePath(componentPath)).delete()
                && new File(getCreatePath(modulePath)).delete()
                && new File(getCreatePath(contractPath)).delete()
                && new File(getCreatePath(modelPath)).delete()
                && new File(getCreatePath(presenterPath)).delete()
                && new File(getCreatePath(builder.uiType == Builder.ACTIVITY ? activityPath : fragmentPath)).delete();

        println(isDelete ? "删除成功":"删除失败", isDelete ? 1:-1);
        System.exit(0);
    }

    /**检查创建文件的前提条件
     * @return
     */
    public boolean check(){
        if(builder.uiType == Builder.ACTIVITY && builder.name.contains("activity")){//当名称中包含activity时，输出警告提醒
            println("代码已自动添加activity结尾，请删除name常量中的activity字符串！", -1);
            return false;

        }else if(builder.uiType == Builder.FRAGMENT && builder.name.contains("fragment")){
            println("代码已自动添加fragment结尾，请删除name常量中的fragment字符串！", -1);
            return false;
        }
        if (TextUtils.isEmpty(builder.componentContent) || TextUtils.isEmpty(builder.moduleContent)
                ||TextUtils.isEmpty(builder.contractContent) || TextUtils.isEmpty(builder.uiContent)
                ||TextUtils.isEmpty(builder.presenterContent) || TextUtils.isEmpty(builder.modelContent)){

            println("缺少一个文件！", -1);
            return false;
        }
        return true;
    }


    /**控制台输入的回调监听
     *
     */
    public interface InputListener{
        void input();
    }

    /**输出警告，并等待确认,需要打开控制台的输入Deditable.java.test.console=true
     * @param message 警告信息
     * @param inputListener 回调对象
     */
    public  static  void input(String message, InputListener inputListener){
        System.out.println(message);
        System.out.println("Y　　N\n");
        System.out.print("请选择：");

        Scanner scanner = new Scanner(System.in);
        while (scanner.hasNext()){
            String input = scanner.next();
            if("Y".equals(input) || "y".equals(input)){
                inputListener.input();

            } else if("N".equals(input) || "n".equals(input)){
                System.exit(0);

            }else {
                println("输入无效", -1);
                System.out.print("请重新选择：");
            }
        }
    }

    public String getCreatePath(String path){
        return String.format(path, builder.name);
    }

    private static final String BASE_PATH = "D:\\project\\BiguoAppAndrord\\app\\src\\main\\java\\com\\dep\\biguo";
    private static final String split = "/";

    private static final String componentPath = BASE_PATH + "/di/component/%sComponent.java";
    private static final String modulePath = BASE_PATH + "/di/module/%sModule.java";

    private static final String contractPath = BASE_PATH + "/mvp/contract/%sContract.java";
    private static final String modelPath = BASE_PATH + "/mvp/model/%sModel.java";
    private static final String presenterPath = BASE_PATH + "/mvp/presenter/%sPresenter.java";
    private static final String activityPath = BASE_PATH + "/mvp/ui/activity/%sActivity.java";
    private static final String fragmentPath = BASE_PATH + "/mvp/ui/fragment/%sFragment.java";

    /**创建文件
     * @param path 需要创建文件的路径
     * @return 是否创建成功
     */
    public boolean createFile(String path){
        //因为Windows的复制的路径总会带上\\符号，避免麻烦，因此使用代码替换成需要的符号
        path = path.replace("\\",split).replace("/",split);

        try {
            File file = new File(path);
            if(file.exists()){
                println(path.substring(path.lastIndexOf(split)+1)+"文件已存在！",-1);
                return false;
            }else {
                //创建文件夹
                boolean createMkdirs = file.getParentFile().mkdirs();
                //创建文件
                boolean createFile = file.createNewFile();
                println(path, createFile ? 1:-1);

                return createFile;
            }
        }catch (Exception e){
            println(Arrays.toString(e.getStackTrace()),-1);
            return false;
        }
    }

    /**按照模板写入文件
     * @param path 文件路径
     * @param content 对应文件的模板
     */
    public void writeFile(String path, String content){
        if(!createFile(path)) return;

        try {
            File file = new File(path);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            fileOutputStream.write(content.replace("《文件名》",builder.name).getBytes());
            fileOutputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**打印
     * @param message 日志信息
     */
    public static void println(String message, int type){
        switch (type){
            case -1:
                System.err.println(message);
                break;
            case 0:
                System.out.println(message);
                break;
            case 1:
                System.out.println("\33[36;4m"+message+"\33[0m");
                break;
        }
    }

    public static class Builder{
        public static final int ACTIVITY = 1;//创建activity
        public static final int FRAGMENT = 2;//创建fragment

        private String name;
        private int uiType;
        private String componentContent;
        private String moduleContent;
        private String contractContent;
        private String modelContent;
        private String presenterContent;
        private String uiContent;

        public Builder(String name, int uiType) {
            this.name = name;
            this.uiType = uiType;
        }

        public Builder setComponentContent(String componentContent) {
            this.componentContent = componentContent;
            return this;
        }

        public Builder setModuleContent(String moduleContent) {
            this.moduleContent = moduleContent;
            return this;
        }

        public Builder setContractContent(String contractContent) {
            this.contractContent = contractContent;
            return this;
        }

        public Builder setModelContent(String modelContent) {
            this.modelContent = modelContent;
            return this;
        }

        public Builder setPresenterContent(String presenterContent) {
            this.presenterContent = presenterContent;
            return this;
        }

        public Builder setUiContent(String uiContent) {
            this.uiContent = uiContent;
            return this;
        }

        public Create build(){
            return new Create(this);
        }
    }
}
