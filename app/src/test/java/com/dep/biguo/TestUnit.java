package com.dep.biguo;


import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TestUnit {
    @Test
    public void test(){
        /*String moreThan = "<br/>%s<font font-size:\"12px\" color:\"%s\">  %s</font> 8888<font font-size:\"12px\" color:\"%s\">  %s</font>";
        Pattern pattern = Pattern.compile("<font(.*?)font-size:(.*?)\\d+[a-z]{0,3}(.*?)>(.*?)</font>");
        Matcher matcher = pattern.matcher(moreThan);
        String[] strings = moreThan.split("^<font(.*?)font-size:(.*?)\\d+[a-z]{0,3}(.*?)>(.*?)</font>$");
        while (matcher.find()) {
            print(moreThan.substring(matcher.start(), matcher.end()));
        }*/


        String[] topicType = {"","单选题", "多选题", "判断题", "问答题", "填空题", "完形填空", "阅读理解", "名词解释","单词练习", "简答题",
                "计算题", "论述题", "案例分析题", "综合应用题", "计算分析题", "材料题", "证明题", "词语解释题", "应用题", "综合题",
                "英译汉", "汉译英", "作文题", "会计分录"};
        for(String str : topicType){
            println(str);
        }
    }

    public void println(String str){
        System.out.println(str);
    }
}
