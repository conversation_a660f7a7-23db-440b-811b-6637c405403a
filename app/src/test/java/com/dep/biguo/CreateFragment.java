package com.dep.biguo;

import org.junit.Test;

public class CreateFragment {
    private static final String name="InviteFriends";

    @Test
    public void createFragment() {
        new Create.Builder(name, Create.Builder.FRAGMENT)
                .setComponentContent(componentContent)
                .setModuleContent(moduleContent)
                .setContractContent(contractContent)
                .setUiContent(uiContent)
                .setPresenterContent(presenterContent)
                .setModelContent(modelContent)
                .build()
                .create();
    }

    @Test
    public void deleteFragment() {
        new Create.Builder(name, Create.Builder.FRAGMENT)
                .build()
                .delete();
    }

    private static final String componentContent = "package com.dep.biguo.di.component;\n" +
            "\n" +
            "import dagger.BindsInstance;\n" +
            "import dagger.Component;\n" +
            "\n" +
            "import com.jess.arms.di.component.AppComponent;\n" +
            "\n" +
            "import com.dep.biguo.di.module.《文件名》Module;\n" +
            "import com.dep.biguo.mvp.contract.《文件名》Contract;\n" +
            "\n" +
            "import com.jess.arms.di.scope.FragmentScope;\n" +
            "import com.dep.biguo.mvp.ui.fragment.《文件名》Fragment;\n" +
            "\n" +
            "@FragmentScope\n" +
            "@Component(modules = 《文件名》Module.class, dependencies = AppComponent.class)\n" +
            "public interface 《文件名》Component {\n" +
            "    void inject(《文件名》Fragment fragment);\n" +
            "\n" +
            "    @Component.Builder\n" +
            "    interface Builder {\n" +
            "        @BindsInstance\n" +
            "        Builder view(《文件名》Contract.View view);\n" +
            "\n" +
            "        Builder appComponent(AppComponent appComponent);\n" +
            "\n" +
            "        《文件名》Component build();\n" +
            "    }\n" +
            "}";
    private static final String moduleContent = "package com.dep.biguo.di.module;\n" +
            "\n" +
            "import com.dep.biguo.mvp.contract.《文件名》Contract;\n" +
            "import com.dep.biguo.mvp.model.《文件名》Model;\n" +
            "\n" +
            "import dagger.Binds;\n" +
            "import dagger.Module;\n" +
            "\n" +
            "@Module\n" +
            "public abstract class 《文件名》Module {\n" +
            "\n" +
            "    @Binds\n" +
            "    abstract 《文件名》Contract.Model bind《文件名》Model(《文件名》Model model);\n" +
            "}";

    private static final String contractContent = "package com.dep.biguo.mvp.contract;\n" +
            "\n" +
            "import com.dep.biguo.mvp.contract.view.ICommonView;\n" +
            "import com.jess.arms.mvp.IModel;\n" +
            "\n" +
            "public interface 《文件名》Contract {\n" +
            "    interface View extends ICommonView {\n" +

            "    }\n" +
            "\n" +
            "    interface Model extends IModel {\n" +

            "    }\n" +
            "}";
    private static final String modelContent = "package com.dep.biguo.mvp.model;\n" +
            "\n" +
            "import android.app.Application;\n" +
            "\n" +
            "import com.dep.biguo.bean.AppVersionBean;\n" +
            "import com.dep.biguo.bean.BaseResponse;\n" +
            "import com.dep.biguo.mvp.contract.《文件名》Contract;\n" +
            "import com.google.gson.Gson;\n" +
            "import com.jess.arms.di.scope.FragmentScope;\n" +
            "import com.jess.arms.integration.IRepositoryManager;\n" +
            "import com.jess.arms.mvp.BaseModel;\n" +
            "\n" +
            "import javax.inject.Inject;\n" +
            "\n" +
            "\n" +
            "@FragmentScope\n" +
            "public class 《文件名》Model extends BaseModel implements 《文件名》Contract.Model {\n" +
            "    @Inject Gson mGson;\n" +
            "    @Inject Application mApplication;\n" +
            "\n" +
            "    @Inject\n" +
            "    public 《文件名》Model(IRepositoryManager repositoryManager) {\n" +
            "        super(repositoryManager);\n" +
            "    }\n" +
            "\n" +
            "    @Override\n" +
            "    public void onDestroy() {\n" +
            "        super.onDestroy();\n" +
            "        this.mGson = null;\n" +
            "        this.mApplication = null;\n" +
            "    }\n" +
            "}";

    private static final String presenterContent = "package com.dep.biguo.mvp.presenter;\n" +
            "\n" +
            "import android.app.Application;\n" +
            "import com.dep.biguo.mvp.contract.《文件名》Contract;\n" +
            "import com.biguo.utils.widget.LoadingDialog;\n" +
            "import com.jess.arms.di.scope.FragmentScope;\n" +
            "import com.jess.arms.http.imageloader.ImageLoader;\n" +
            "import com.jess.arms.integration.AppManager;\n" +
            "import com.jess.arms.mvp.BasePresenter;\n" +
            "\n" +
            "import javax.inject.Inject;\n" +
            "\n" +
            "import me.jessyan.rxerrorhandler.core.RxErrorHandler;\n" +
            "\n" +
            "@FragmentScope\n" +
            "public class 《文件名》Presenter extends BasePresenter<《文件名》Contract.Model, 《文件名》Contract.View> {\n" +
            "    @Inject RxErrorHandler mErrorHandler;\n" +
            "    @Inject Application mApplication;\n" +
            "    @Inject ImageLoader mImageLoader;\n" +
            "    @Inject AppManager mAppManager;\n" +
            "\n" +
            "    @Inject\n" +
            "    public 《文件名》Presenter(《文件名》Contract.Model model, 《文件名》Contract.View rootView) {\n" +
            "        super(model, rootView);\n" +
            "    }\n" +
            "\n" +
            "    @Override\n" +
            "    public void onDestroy() {\n" +
            "        super.onDestroy();\n" +
            "        this.mErrorHandler = null;\n" +
            "        this.mAppManager = null;\n" +
            "        this.mImageLoader = null;\n" +
            "        this.mApplication = null;\n" +
            "        LoadingDialog.hideLoadingDialog();\n" +
            "    }\n" +
            "}\n";
    private static final String uiContent = "package com.dep.biguo.mvp.ui.fragment;\n" +
            "\n" +
            "import android.os.Bundle;\n" +
            "import androidx.annotation.NonNull;\n" +
            "import androidx.annotation.Nullable;\n" +
            "import android.view.LayoutInflater;\n" +
            "import android.view.View;" +
            "\n" +
            "import com.dep.biguo.R;\n" +
            "import com.dep.biguo.di.component.Dagger《文件名》Component;\n"+
            "import com.dep.biguo.mvp.contract.《文件名》Contract;\n" +
            "import com.dep.biguo.mvp.presenter.《文件名》Presenter;\n" +
            "import com.jess.arms.base.BaseFragment;\n" +
            "import com.jess.arms.di.component.AppComponent;\n" +
            "\n" +
            "public class 《文件名》Fragment extends BaseFragment<《文件名》Presenter> implements 《文件名》Contract.View {\n" +
            "    @Override\n" +
            "    public void setupFragmentComponent(@NonNull AppComponent appComponent) {\n" +
            "        Dagger《文件名》Component\n" +
            "                .builder()\n" +
            "                .appComponent(appComponent)\n" +
            "                .view(this)\n" +
            "                .build()\n" +
            "                .inject(this);"+

            "\n" +
            "    }\n" +
            "\n" +
            "    @Override\n" +
            "    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {\n" +
            "        View view = inflater.inflate(R.layout, container, false);\n" +
            "        return view;\n" +
            "    }\n" +
            "\n" +
            "    @Override\n" +
            "    public void initData(@Nullable Bundle savedInstanceState) {\n" +
            "\n" +
            "    }\n" +
            "\n" +
            "    @Override\n" +
            "    public void showMessage(@NonNull String message) {\n" +
            "\n" +
            "    }" +
            "\n" +
            "    @Override\n" +
            "    public void setData(@Nullable Object data) {\n" +
            "\n" +
            "    }" +
            "\n}";
}
