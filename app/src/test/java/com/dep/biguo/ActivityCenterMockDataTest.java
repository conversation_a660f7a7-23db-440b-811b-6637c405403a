package com.dep.biguo;

import com.dep.biguo.bean.ActivityCenterBean;
import com.dep.biguo.util.ActivityMockDataUtil;

import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * 活动中心模拟数据测试
 */
public class ActivityCenterMockDataTest {

    @Test
    public void testGenerateBanners() {
        List<ActivityCenterBean.BannerItem> banners = ActivityMockDataUtil.generateBanners();
        
        assertNotNull("横幅列表不应为空", banners);
        assertTrue("横幅数量应大于0", banners.size() > 0);
        assertTrue("横幅数量应不超过3个", banners.size() <= 3);
        
        for (ActivityCenterBean.BannerItem banner : banners) {
            assertNotNull("横幅ID不应为空", banner.getId());
            assertNotNull("横幅标题不应为空", banner.getTitle());
            assertNotNull("横幅副标题不应为空", banner.getSubtitle());
            assertNotNull("横幅图片URL不应为空", banner.getImageUrl());
            assertNotNull("横幅跳转URL不应为空", banner.getJumpUrl());
            assertTrue("横幅类型应大于0", banner.getType() > 0);
        }
    }

    @Test
    public void testGenerateRandomActivity() {
        // 测试精选必看活动
        ActivityCenterBean.ActivityItem hotActivity = ActivityMockDataUtil.generateRandomActivity(1, "100");
        assertNotNull("精选活动不应为空", hotActivity);
        assertEquals("活动类型应为1", 1, hotActivity.getType());
        assertNotNull("活动标题不应为空", hotActivity.getTitle());
        assertNotNull("活动副标题不应为空", hotActivity.getSubtitle());
        assertNotNull("活动状态不应为空", hotActivity.getStatus());
        assertTrue("活动ID应以100开头", hotActivity.getId().startsWith("100"));
        
        // 测试读书会活动
        ActivityCenterBean.ActivityItem bookActivity = ActivityMockDataUtil.generateRandomActivity(2, "200");
        assertNotNull("读书会活动不应为空", bookActivity);
        assertEquals("活动类型应为2", 2, bookActivity.getType());
        assertTrue("活动ID应以200开头", bookActivity.getId().startsWith("200"));
        
        // 测试公益活动
        ActivityCenterBean.ActivityItem charityActivity = ActivityMockDataUtil.generateRandomActivity(3, "300");
        assertNotNull("公益活动不应为空", charityActivity);
        assertEquals("活动类型应为3", 3, charityActivity.getType());
        assertTrue("活动ID应以300开头", charityActivity.getId().startsWith("300"));
    }

    @Test
    public void testActivityDataIntegrity() {
        ActivityCenterBean.ActivityItem activity = ActivityMockDataUtil.generateRandomActivity(1, "test");
        
        // 验证时间数据
        assertNotNull("报名开始时间不应为空", activity.getRegisterStartTime());
        assertNotNull("报名结束时间不应为空", activity.getRegisterEndTime());
        assertNotNull("活动开始时间不应为空", activity.getActivityStartTime());
        assertNotNull("活动结束时间不应为空", activity.getActivityEndTime());
        
        // 验证参与人数数据
        assertTrue("最大参与人数应大于0", activity.getMaxParticipants() > 0);
        assertTrue("当前参与人数应大于等于0", activity.getCurrentParticipants() >= 0);
        assertTrue("当前参与人数不应超过最大参与人数", 
                  activity.getCurrentParticipants() <= activity.getMaxParticipants());
        
        // 验证URL数据
        assertNotNull("图片URL不应为空", activity.getImageUrl());
        assertNotNull("跳转URL不应为空", activity.getJumpUrl());
        assertTrue("图片URL应包含https", activity.getImageUrl().contains("https://"));
        assertTrue("跳转URL应包含https", activity.getJumpUrl().contains("https://"));
    }
}